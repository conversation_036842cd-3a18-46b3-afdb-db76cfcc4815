# Swift Compatibility Fixes

This directory contains patches to fix Swift compatibility issues in Expo modules.

## Issues Fixed

1. **Protocol Nesting Issues**: Swift doesn't allow protocols to be nested inside other declarations.
   - Fixed in `AnyChild.swift` and `SwiftUIHostingView.swift`

2. **Closure Type Inference**: Swift requires explicit return types for closures with multiple statements.
   - Fixed in `CoreModule.swift` and notification module files

3. **Missing Methods**: Replaced `onGeometryChange` with standard `GeometryReader` and `onAppear`.
   - Fixed in `AutoSizingStack.swift`

## How to Apply the Fixes

When you encounter Swift compilation errors, you can check if fixes are needed and apply them manually:

```bash
# From the mobile directory
npm run check-swift  # Check if fixes are needed
npm run fix-swift    # Apply the fixes
```

Or run the script directly:

```bash
# From the project root
./patches/apply-swift-fixes.sh
```

Or apply the patch manually:

```bash
# From the project root
patch -p0 < patches/swift-compatibility-fixes.patch
```

**Note**: These patches are no longer applied automatically and must be run manually when needed.

## When to Apply

Apply these fixes when you encounter Swift compilation errors like:

- `protocol 'AnyChild' cannot be nested inside another declaration`
- `cannot infer return type for closure with multiple statements; add explicit type to disambiguate`
- `value of type 'some View' has no member 'onGeometryChange'`

These errors typically occur after:
- Running `npm install`
- Updating Expo packages
- Pulling changes from git
- Running `npm run prebuild:ios` or `npm run prebuild:dev`
