#!/bin/bash

# <PERSON><PERSON>t to apply Swift compatibility fixes to Expo modules
# This fixes issues with Swift protocol nesting and closure type inference

echo "Applying Swift compatibility fixes to Expo modules..."

# Apply the patch
patch -p0 < patches/swift-compatibility-fixes.patch

# Check if patch was applied successfully
if [ $? -eq 0 ]; then
  echo "✅ Swift compatibility fixes applied successfully!"
else
  echo "❌ Failed to apply Swift compatibility fixes. You may need to apply them manually."
  exit 1
fi

echo "You can now run 'npm run ios' to build and run the app."
