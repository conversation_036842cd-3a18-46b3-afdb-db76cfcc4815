diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
@@ -5,12 +5,14 @@ import SwiftUI
 /**
  A type-erased protocol representing a child view for a SwiftUI view.
  */
-extension ExpoSwiftUI {
-  public protocol AnyChild: SwiftUI.View {
-    // swiftlint:disable:next nesting - Keep AnyChild protocol inside ExpoSwiftUI namespace
-    associatedtype ChildViewType: SwiftUI.View
-    var childView: ChildViewType { get }
+// Define the protocol outside the extension
+public protocol ExpoSwiftUIAnyChild: SwiftUI.View {
+  associatedtype ChildViewType: SwiftUI.View
+  var childView: ChildViewType { get }
+  var id: ObjectIdentifier { get }
+}
 
-    var id: ObjectIdentifier { get }
-  }
+// Then extend it in the ExpoSwiftUI namespace
+extension ExpoSwiftUI {
+  public typealias AnyChild = ExpoSwiftUIAnyChild
 }

diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
@@ -32,12 +32,16 @@ extension ExpoSwiftUI {
           content.overlay {
             content.fixedSize(horizontal: axis.contains(.horizontal), vertical: axis.contains(.vertical))
               .hidden()
-              .onGeometryChange(for: CGSize.self, of: { proxy in proxy.size }, action: { size in
-                var size = size
-                size.width = axis.contains(.horizontal) ? size.width : ShadowNodeProxy.UNDEFINED_SIZE
-                size.height = axis.contains(.vertical) ? size.height : ShadowNodeProxy.UNDEFINED_SIZE
-                proxy.setViewSize?(size)
-              })
+              .background(
+                GeometryReader { proxy in
+                  Color.clear.onAppear {
+                    var size = proxy.size
+                    size.width = axis.contains(.horizontal) ? size.width : ShadowNodeProxy.UNDEFINED_SIZE
+                    size.height = axis.contains(.vertical) ? size.height : ShadowNodeProxy.UNDEFINED_SIZE
+                    self.proxy.setViewSize?(size)
+                  }
+                }
+              )
           }
         } else {
           content

diff --git a/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
+++ b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
@@ -6,7 +6,7 @@ import Foundation
 // The core module that describes the `global.expo` object.
 internal final class CoreModule: Module {
   internal func definition() -> ModuleDefinition {
-    Constant("expoModulesCoreVersion") {
+    Constant("expoModulesCoreVersion") { () -> [String: Any] in
       let version = CoreModuleHelper.getVersion()
       let components = version.split(separator: "-")[0].split(separator: ".").compactMap { Int($0) }

diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
@@ -11,6 +11,12 @@ internal protocol AnyExpoSwiftUIHostingView {
   func getProps() -> ExpoSwiftUI.ViewProps
 }

+/**
+ For a SwiftUI view to self-contain a HostingView, it can conform to the WithHostingView protocol.
+ */
+public protocol ExpoSwiftUIWithHostingView {
+}
+
 extension ExpoSwiftUI {
   /**
    Checks if the child view is wrapped by a `UIViewHost` and matches the specified SwiftUI view type.
@@ -25,12 +31,7 @@ extension ExpoSwiftUI {

 extension ExpoSwiftUI {
   internal typealias AnyHostingView = AnyExpoSwiftUIHostingView
-
-  /**
-   For a SwiftUI view to self-contain a HostingView, it can conform to the WithHostingView protocol.
-   */
-  public protocol WithHostingView {
-  }
+  public typealias WithHostingView = ExpoSwiftUIWithHostingView

   /**
    A hosting view that renders a SwiftUI view inside the UIKit view hierarchy.

diff --git a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift
+++ b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift
@@ -8,7 +8,7 @@ open class CategoriesModule: Module {
   public func definition() -> ModuleDefinition {
     Name("ExpoNotificationCategoriesModule")

-    AsyncFunction("getNotificationCategoriesAsync") {
+    AsyncFunction("getNotificationCategoriesAsync") { () -> [CategoryRecord] in
       let categories = await UNUserNotificationCenter.current().notificationCategories()
       return filterAndSerializeCategories(categories)
     }

diff --git a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift
+++ b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift
@@ -9,7 +9,7 @@ open class PresentationModule: Module {
   public func definition() -> ModuleDefinition {
     Name("ExpoNotificationPresenter")

-    AsyncFunction("getPresentedNotificationsAsync") {
+    AsyncFunction("getPresentedNotificationsAsync") { () -> [[String: Any]] in
       let notifications = await UNUserNotificationCenter.current().deliveredNotifications()
       return self.serializeNotifications(notifications)
     }

diff --git a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift
index 1234567..abcdef0 100644
--- a/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift
+++ b/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift
@@ -24,7 +24,7 @@ open class SchedulerModule: Module {
   public func definition() -> ModuleDefinition {
     Name("ExpoNotificationScheduler")

-    AsyncFunction("getAllScheduledNotificationsAsync") {
+    AsyncFunction("getAllScheduledNotificationsAsync") { () -> [[String: Any]] in
       let requests = await UNUserNotificationCenter.current().pendingNotificationRequests()
       return serializedNotificationRequests(requests)
     }
