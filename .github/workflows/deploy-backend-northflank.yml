name: Deploy Backend to Northflank

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend-northflank.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend-northflank.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: nasserbvb/itrip-backend

jobs:
  test:
    name: Test Backend
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

  build-and-deploy-staging:
    name: Build and Deploy to Staging
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.GH_PAT }}" | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin

      - name: Build Docker image
        run: |
          cd backend
          docker build -f Dockerfile-staging -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop-${{ github.sha }} .
          docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop-${{ github.sha }} ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging-latest

      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop-${{ github.sha }}
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging-latest

      - name: Deploy to Northflank Staging
        uses: northflank/deploy-to-northflank@v1
        with:
          northflank-api-key: ${{ secrets.NORTHFLANK_API_TOKEN }}
          project-id: ${{ secrets.NORTHFLANK_PROJECT_ID }}
          service-id: ${{ secrets.NORTHFLANK_STAGING_SERVICE_ID }}
          image-path: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging-latest
          credentials-id: personal-github-registry

  build-and-deploy-production:
    name: Build and Deploy to Production
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.GH_PAT }}" | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin

      - name: Build Docker image
        run: |
          cd backend
          docker build -f Dockerfile-production -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main-${{ github.sha }} .
          docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main-${{ github.sha }} ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:production-latest

      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main-${{ github.sha }}
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:production-latest

      - name: Deploy to Northflank Production
        uses: northflank/deploy-to-northflank@v1
        with:
          northflank-api-key: ${{ secrets.NORTHFLANK_API_TOKEN }}
          project-id: ${{ secrets.NORTHFLANK_PROJECT_ID }}
          service-id: ${{ secrets.NORTHFLANK_PRODUCTION_SERVICE_ID }}
          image-path: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:production-latest
          credentials-id: personal-github-registry
