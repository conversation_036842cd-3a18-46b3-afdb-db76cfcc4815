# iTrip - Travel Itinerary Generator

A full-stack application for generating travel itineraries using AI.

## Project Structure

This project consists of multiple components:

- **Backend**: NestJS application for handling API requests and AI integration
- **Mobile**: React Native/Expo application for iOS and Android
## Getting Started

### Backend

```bash
cd backend
npm install
npm run start:dev
```

### Mobile

```bash
cd mobile
npm install
npm run fix-swift  # Apply Swift compatibility fixes
npm run ios        # For iOS
npm run android    # For Android
```

## Swift Compatibility Fixes

The mobile app includes patches to fix Swift compatibility issues in Expo modules. These fixes address:

1. Protocol nesting issues
2. Closure type inference problems
3. Missing Swift methods

### How to Apply Patches After Git Pull

When you pull a new version of the code, follow this workflow:

1. **Pull the latest code**:
   ```bash
   git pull
   ```

2. **Install dependencies** (this will automatically apply the patches):
   ```bash
   cd mobile
   npm install
   ```

   The `postinstall` script in package.j<PERSON> will automatically run and apply the Swift compatibility fixes.

3. **Build and run the app**:
   ```bash
   npm run ios
   ```

### Manual Application

If you need to manually apply the patches:

```bash
cd mobile
npm run fix-swift
```

The patches are designed to survive git pulls and npm installs, so you don't need to worry about them being lost when updating your code.

For more details, see the [mobile/.patches/README.md](mobile/.patches/README.md) file.

## Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` in each component directory and adjust the values as needed.

### Backend Configuration

- **JWT_SECRET**: Secret key for JWT token generation
- **OPENAI_API_KEY**: Your OpenAI API key
- **GENERATION_MODE**: Choose between `multi_round` (default) or `one_shot`
- **MODEL_TO_USE**: Choose between `openai`, `groq`, or `anthropic`

### Mobile Configuration

The mobile app uses Expo configuration in `app.config.js`.

## Development

### Running Tests

```bash
# Backend tests
cd backend
npm run test

# Mobile tests
cd mobile
npm run test
```

## Deployment

### 🏗️ Architecture Overview

The iTrip backend is deployed on AWS using a containerized architecture with support for multiple environments (staging and production).

#### AWS Services Used
- **AWS ECS Fargate**: Serverless container hosting
- **AWS ECR**: Container registry
- **AWS Application Load Balancer**: Traffic distribution and SSL termination
- **AWS S3**: Environment configuration files storage
- **AWS CloudWatch**: Logging and monitoring
- **AWS VPC**: Network isolation
- **AWS Route 53**: DNS management (optional)
- **AWS Certificate Manager**: SSL certificates (optional)

#### Environments
- **Staging**: `develop` branch → staging environment
- **Production**: `main` branch → production environment

### 🚀 Initial Setup

#### Prerequisites
1. AWS CLI installed and configured
2. Node.js 18+ installed
3. Docker installed
4. GitHub repository with appropriate permissions

#### 1. AWS Account Setup

```bash
# Configure AWS CLI
aws configure

# Verify access
aws sts get-caller-identity
```

#### 2. Infrastructure Deployment

```bash
# Navigate to infrastructure directory
cd infrastructure

# Install dependencies
npm install

# Deploy shared resources (ECR repository)
npm run deploy:shared

# Deploy staging environment
npm run deploy:staging

# Deploy production environment
npm run deploy:production

# Or deploy all at once
npm run deploy:all
```

#### 3. Environment Variables Setup

Create environment files and upload them to S3:

```bash
# Create environment files in backend/environments/
# staging.env and production.env

# Upload staging environment file
cd backend
./scripts/upload-env.sh staging

# Upload production environment file
./scripts/upload-env.sh production
```

#### 4. GitHub Secrets Setup

Add the following secrets to your GitHub repository:

```
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_ROLE_TO_ASSUME=arn:aws:iam::account-id:role/github-actions-role
```

### 🔄 CI/CD Pipeline

#### Workflow Triggers
- **Push to `develop`**: Deploys to staging
- **Push to `main`**: Deploys to production
- **Pull requests**: Runs tests only

#### Pipeline Stages
1. **Test**: Linting, unit tests, e2e tests
2. **Build**: Docker image build and push to ECR
3. **Deploy**: ECS service update with new image

#### Branch Strategy
```
main (production)
├── develop (staging)
│   ├── feature/new-feature
│   └── bugfix/fix-issue
```

### 🛠️ Manual Deployment

#### Build and Push Docker Image

```bash
# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Build image
cd backend
docker build -t itrip-backend .

# Tag and push
docker tag itrip-backend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/itrip-backend:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/itrip-backend:latest
```

#### Update ECS Service

```bash
# Force new deployment
aws ecs update-service \
  --cluster itrip-staging \
  --service itrip-backend-staging \
  --force-new-deployment
```

### 📊 Monitoring and Logging

#### CloudWatch Logs
- Log Group: `/ecs/itrip-backend-{environment}`
- Retention: 1 week (configurable)

#### Health Checks
- **Application**: `GET /health`
- **Load Balancer**: HTTP 200 response
- **Container**: Docker health check

#### Monitoring URLs
- Staging: `https://staging-api.itrip.com/health`
- Production: `https://api.itrip.com/health`

### 🔧 Configuration Management

#### Environment Variables
All environment variables are stored in S3 as environment files:
- Staging: `s3://itrip-config-staging-{account-id}/environments/staging/.env`
- Production: `s3://itrip-config-production-{account-id}/environments/production/.env`

#### Adding New Environment Variables

1. Update environment files:
   - `backend/environments/staging.env`
   - `backend/environments/production.env`

2. Upload updated environment files:
   ```bash
   cd backend
   ./scripts/upload-env.sh staging
   ./scripts/upload-env.sh production
   ```

3. Restart ECS services to pick up new environment variables:
   ```bash
   # Staging
   aws ecs update-service \
     --cluster itrip-staging \
     --service itrip-backend-staging \
     --force-new-deployment

   # Production
   aws ecs update-service \
     --cluster itrip-production \
     --service itrip-backend-production \
     --force-new-deployment
   ```

### 🚨 Troubleshooting

#### Common Issues

##### 1. ECS Service Won't Start
```bash
# Check service events
aws ecs describe-services \
  --cluster itrip-staging \
  --services itrip-backend-staging

# Check task logs
aws logs tail /ecs/itrip-backend-staging --follow
```

##### 2. Load Balancer Health Check Failures
```bash
# Check target group health
aws elbv2 describe-target-health \
  --target-group-arn <target-group-arn>
```

##### 3. S3 Environment File Access Issues
```bash
# Test S3 bucket access
aws s3 ls s3://itrip-config-staging-{account-id}/environments/staging/

# Download environment file to verify content
aws s3 cp s3://itrip-config-staging-{account-id}/environments/staging/.env ./temp.env
cat ./temp.env
rm ./temp.env
```

#### Useful Commands

```bash
# View running tasks
aws ecs list-tasks --cluster itrip-staging

# Execute command in running container
aws ecs execute-command \
  --cluster itrip-staging \
  --task <task-arn> \
  --container iTripBackend \
  --interactive \
  --command "/bin/sh"

# View application logs
aws logs tail /ecs/itrip-backend-staging --follow

# Scale service
aws ecs update-service \
  --cluster itrip-staging \
  --service itrip-backend-staging \
  --desired-count 2
```

### 🔒 Security Best Practices

1. **IAM Roles**: Minimal permissions for ECS tasks
2. **S3 Encryption**: Server-side encryption for environment files
3. **VPC**: Private subnets for ECS tasks
4. **Security Groups**: Restricted access rules
5. **SSL/TLS**: HTTPS termination at load balancer
6. **Container Security**: Non-root user in Docker container
7. **S3 Access**: Bucket policies restrict access to ECS tasks only

### 💰 Cost Optimization

1. **Fargate Spot**: Consider using Spot instances for staging
2. **Auto Scaling**: Configure based on CPU/memory metrics
3. **Log Retention**: Adjust CloudWatch log retention periods
4. **Resource Sizing**: Right-size CPU and memory allocations

### 📈 Scaling

#### Horizontal Scaling
```bash
# Auto scaling based on CPU utilization
aws application-autoscaling register-scalable-target \
  --service-namespace ecs \
  --scalable-dimension ecs:service:DesiredCount \
  --resource-id service/itrip-production/itrip-backend-production \
  --min-capacity 2 \
  --max-capacity 10
```

#### Vertical Scaling
Update task definition with higher CPU/memory values and redeploy.

### 🔄 Rollback Strategy

#### Automatic Rollback
ECS deployment configuration includes automatic rollback on health check failures.

#### Manual Rollback
```bash
# List previous task definitions
aws ecs list-task-definitions \
  --family-prefix itrip-backend-production

# Update service to previous task definition
aws ecs update-service \
  --cluster itrip-production \
  --service itrip-backend-production \
  --task-definition itrip-backend-production:PREVIOUS_REVISION
```

### 📞 Support

For deployment issues:
1. Check CloudWatch logs
2. Verify S3 environment files
3. Review ECS service events
4. Check GitHub Actions workflow logs

### 🛠️ Infrastructure Management Scripts

The infrastructure includes several utility scripts:

#### Available Scripts

```bash
cd infrastructure

# Deploy infrastructure
./scripts/deploy.sh                    # Deploy all stacks
./scripts/deploy.sh iTripStagingStack  # Deploy specific stack

# Clean up resources
./scripts/cleanup.sh                   # Interactive cleanup

# Import existing resources
./scripts/import-resources.sh          # Import existing AWS resources
```

#### Environment File Management

```bash
cd backend

# Upload environment files to S3
./scripts/upload-env.sh staging        # Upload staging environment
./scripts/upload-env.sh production     # Upload production environment
```

#### NPM Scripts (Infrastructure)

```bash
cd infrastructure

# Deployment commands
npm run deploy:shared                  # Deploy shared resources (ECR)
npm run deploy:staging                 # Deploy staging environment
npm run deploy:production              # Deploy production environment
npm run deploy:all                     # Deploy all stacks

# Destroy commands (use with caution)
npm run destroy:staging                # Destroy staging environment
npm run destroy:production             # Destroy production environment
npm run destroy:all                    # Destroy all stacks

# Utility commands
npm run diff:staging                   # Show staging stack differences
npm run diff:production                # Show production stack differences
npm run synth                          # Synthesize CloudFormation templates
npm run list                           # List all stacks
```
