# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
.data
# Expo
.expo/
dist/
web-build/
expo-env.d.ts
credentials.plist
mobile/ios/
mobile/android/


# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example
ios
android

venv
*.env

# AWS credentials and sensitive deployment files
.aws/
aws-credentials.json
credentials.json

# CDK outputs and infrastructure
infrastructure/cdk.out/
infrastructure/cdk.context.json
infrastructure/node_modules/

# Terraform state files (if using Terraform)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Local parameter files with real values
parameters-local.json
secrets.json
config-local.json
infrastructure/parameters/*-local.json

# Environment-specific files
.env.staging
.env.production
backend/.env.staging
backend/.env.production

# Docker build context
.dockerignore.local