/* eslint-disable */
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';

/**
 * Creates a mock Mongoose model with common methods
 */
export const createMockMongooseModel = (mockData: any = {}) => {
  const mockModel: any = jest.fn().mockImplementation(() => ({
    save: jest.fn().mockResolvedValue(mockData),
  }));

  // Add static methods - let tests configure their own chaining
  mockModel.find = jest.fn();
  mockModel.findOne = jest.fn();
  mockModel.findById = jest.fn();
  mockModel.findByIdAndUpdate = jest.fn();
  mockModel.findByIdAndDelete = jest.fn();
  mockModel.findOneAndUpdate = jest.fn();
  mockModel.create = jest.fn();
  mockModel.updateOne = jest.fn();
  mockModel.updateMany = jest.fn();
  mockModel.deleteOne = jest.fn();
  mockModel.deleteMany = jest.fn();
  mockModel.countDocuments = jest.fn();
  mockModel.aggregate = jest.fn();
  mockModel.populate = jest.fn();
  mockModel.exec = jest.fn();
  mockModel.lean = jest.fn();
  mockModel.sort = jest.fn();
  mockModel.limit = jest.fn();
  mockModel.skip = jest.fn();
  mockModel.select = jest.fn();

  return mockModel;
};

/**
 * Creates a mock ConfigService with default test configuration
 */
export const createMockConfigService = (
  overrides: Record<string, any> = {},
) => ({
  get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
    const config = {
      'database.uri': 'mongodb://localhost:27017/itrip-test',
      'jwt.secret': 'test-jwt-secret',
      'jwt.expiresIn': '1d',
      'ai.openaiApiKey': 'test-openai-key',
      'ai.groqApiKey': 'test-groq-key',
      'ai.deepseekApiKey': 'test-deepseek-key',
      'maps.googleApiKey': 'test-google-maps-key',
      'maps.orsApiKey': 'test-ors-key',
      'auth.google.clientId': 'test-google-client-id',
      'auth.google.clientSecret': 'test-google-client-secret',
      'payments.apple.bundleId': 'com.test.app',
      'payments.google.packageName': 'com.test.app',
      ...overrides,
    };
    return config[key] ?? defaultValue;
  }),
  getOpenRouteServiceConfig: jest.fn(() => ({
    apiUrl: 'https://api.openrouteservice.org',
    apiKey: 'test-ors-key',
  })),
  getGoogleMapsApiKey: jest.fn(() => 'test-google-maps-key'),
  getOpenCageApiKey: jest.fn(() => 'test-opencage-key'),
  getPaymentConfig: jest.fn(() => ({
    allowMockPurchases: false,
    appleAppSharedSecret: 'test-secret',
    googlePlayServiceAccount: 'test-account',
  })),
  getGoogleOAuthConfig: jest.fn(() => ({
    clientID: 'test-google-client-id',
    clientSecret: 'test-google-client-secret',
  })),
  getInitialTokenBalance: jest.fn(() => 1000),
  getGoogleClientConfig: jest.fn(() => ({
    clientId: 'test-google-client-id',
    clientSecret: 'test-google-client-secret',
    androidClientId: 'test-android-client-id',
    iosClientId: 'test-ios-client-id',
  })),
  getJwtConfig: jest.fn(() => ({
    secret: 'test-jwt-secret',
    expiresIn: '1h',
    signOptions: {
      expiresIn: '1h',
    },
    verifyOptions: {
      ignoreExpiration: false,
    },
  })),
  getOrThrow: jest.fn().mockImplementation((key: string) => {
    const config = {
      'database.uri': 'mongodb://localhost:27017/itrip-test',
      'jwt.secret': 'test-jwt-secret',
      ...overrides,
    };
    if (config[key] === undefined) {
      throw new Error(`Configuration key "${key}" not found`);
    }
    return config[key];
  }),
});

/**
 * Creates a mock JwtService
 */
export const createMockJwtService = () => ({
  sign: jest.fn().mockReturnValue('mock-jwt-token'),
  verify: jest
    .fn()
    .mockReturnValue({ sub: 'user-id', email: '<EMAIL>' }),
  decode: jest
    .fn()
    .mockReturnValue({ sub: 'user-id', email: '<EMAIL>' }),
});

/**
 * Creates a mock Logger
 */
export const createMockLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
});

/**
 * Creates a mock HTTP service (Axios)
 */
export const createMockHttpService = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  head: jest.fn(),
  options: jest.fn(),
  axiosRef: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    head: jest.fn(),
    options: jest.fn(),
  },
});

/**
 * Creates a mock Socket.IO server
 */
export const createMockSocketServer = () => ({
  emit: jest.fn(),
  to: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  on: jest.fn(),
  use: jest.fn(),
  sockets: {
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
  },
});

/**
 * Creates a mock Socket.IO client
 */
export const createMockSocket = () => ({
  id: 'mock-socket-id',
  emit: jest.fn(),
  on: jest.fn(),
  join: jest.fn(),
  leave: jest.fn(),
  disconnect: jest.fn(),
  handshake: {
    auth: {},
    headers: {},
  },
  data: {},
});

/**
 * Creates a testing module with common providers mocked
 */
export const createTestingModule = async (
  providers: any[] = [],
  imports: any[] = [],
  overrides: Record<string, any> = {},
) => {
  const moduleBuilder = Test.createTestingModule({
    imports,
    providers: [
      ...providers,
      {
        provide: ConfigService,
        useValue: createMockConfigService(overrides),
      },
      {
        provide: JwtService,
        useValue: createMockJwtService(),
      },
    ],
  });

  return moduleBuilder.compile();
};

/**
 * Creates a mock user object
 */
export const createMockUser = (overrides: Partial<any> = {}) => ({
  _id: 'mock-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  provider: 'local',
  emailVerified: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  tokenBalanceId: 'mock-token-balance-id',
  ...overrides,
});

/**
 * Creates a mock trip object
 */
export const createMockTrip = (overrides: Partial<any> = {}) => ({
  _id: 'mock-trip-id',
  userId: 'mock-user-id',
  name: 'Test Trip',
  destination: 'Test Destination',
  startDate: new Date(),
  endDate: new Date(),
  budget: 1000,
  travelers: 2,
  status: 'draft',
  isArchived: false,
  isFavorite: false,
  itinerary: [
    {
      day: 1,
      activities: [
        { id: 'activity-1', name: 'Test Activity 1', completed: false },
      ],
    },
    {
      day: 2,
      activities: [
        { id: 'activity-2', name: 'Test Activity 2', completed: false },
      ],
    },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

/**
 * Creates a mock token balance object
 */
export const createMockTokenBalance = (overrides: Partial<any> = {}) => ({
  _id: 'mock-token-balance-id',
  userId: 'mock-user-id',
  balance: 1000,
  totalTokensUsed: 0,
  totalTokensAdded: 1000,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

/**
 * Helper to mock Mongoose model providers
 */
export const mockMongooseProviders = (models: string[]) => {
  return models.map((model) => ({
    provide: getModelToken(model),
    useValue: createMockMongooseModel(),
  }));
};

/**
 * Creates a mock API response
 */
export const createMockApiResponse = (data: any, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});

/**
 * Creates a mock OpenAI response
 */
export const createMockOpenAIResponse = (
  content: string = 'Mock AI response',
) => ({
  choices: [
    {
      message: {
        content,
        role: 'assistant',
      },
      finish_reason: 'stop',
      index: 0,
    },
  ],
  usage: {
    prompt_tokens: 10,
    completion_tokens: 20,
    total_tokens: 30,
  },
  model: 'gpt-4',
  id: 'mock-completion-id',
  object: 'chat.completion',
  created: Date.now(),
});
