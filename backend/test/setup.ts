/* eslint-disable */
// @ts-nocheck
import { jest } from '@jest/globals';

// Global test setup
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.MONGODB_URI = 'mongodb://localhost:27017/itrip-test';
  process.env.OPENAI_API_KEY = 'test-openai-key';
  process.env.GROQ_API_KEY = 'test-groq-key';
  process.env.DEEPSEEK_API_KEY = 'test-deepseek-key';
  process.env.GOOGLE_MAPS_API_KEY = 'test-google-maps-key';
  process.env.ORS_API_KEY = 'test-ors-key';
});

// Global mocks
const mockTypes = {
  ObjectId: jest.fn().mockImplementation((id) => id || 'mock-object-id'),
  Mixed: jest.fn(),
};

const mockSchema = jest.fn().mockImplementation(() => ({
  index: jest.fn(),
  Types: mockTypes,
}));

mockSchema.Types = mockTypes;

jest.mock('mongoose', () => ({
  connect: jest.fn(),
  connection: {
    readyState: 1,
    on: jest.fn(),
    once: jest.fn(),
  },
  Schema: mockSchema,
  model: jest.fn(),
  Document: class MockDocument {},
  Types: mockTypes,
}));

jest.mock('openai', () => ({
  default: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn(),
      },
    },
    assistants: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      del: jest.fn(),
    },
    threads: {
      create: jest.fn(),
      retrieve: jest.fn(),
      messages: {
        create: jest.fn(),
        list: jest.fn(),
      },
      runs: {
        create: jest.fn(),
        retrieve: jest.fn(),
        list: jest.fn(),
      },
    },
  })),
}));

jest.mock('axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    create: jest.fn(() => ({
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    })),
  },
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

// Mock socket.io
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn(() => ({
      emit: jest.fn(),
    })),
    use: jest.fn(),
  })),
}));

// Mock passport strategies
jest.mock('passport-google-oauth20', () => ({
  Strategy: jest.fn(),
}));

jest.mock('passport-jwt', () => ({
  Strategy: jest.fn(),
  ExtractJwt: {
    fromAuthHeaderAsBearerToken: jest.fn(),
  },
}));

jest.mock('passport-local', () => ({
  Strategy: jest.fn(),
}));

// Mock external APIs
jest.mock('google-auth-library', () => ({
  OAuth2Client: jest.fn().mockImplementation(() => ({
    verifyIdToken: jest.fn(),
  })),
}));

// Global test utilities
(global as any).createMockModel = (data: any = {}) => {
  const mockData = { _id: 'mock-id', ...data };
  return {
    save: jest.fn().mockResolvedValue(mockData),
    findById: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    findOneAndUpdate: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    deleteOne: jest.fn(),
    deleteMany: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
    populate: jest.fn(),
    exec: jest.fn(),
    lean: jest.fn(),
    sort: jest.fn(),
    limit: jest.fn(),
    skip: jest.fn(),
    select: jest.fn(),
    ...data,
  };
};

(global as any).createMockRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
    getMany: jest.fn(),
    execute: jest.fn(),
  })),
});

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
});
