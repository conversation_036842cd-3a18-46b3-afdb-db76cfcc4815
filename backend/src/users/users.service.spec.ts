/* eslint-disable */
import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';

import { UsersService } from './users.service';
import { User } from './schemas/user.schema';
import { createMockMongooseModel } from '../../test/test-utils';

describe('UsersService', () => {
  let service: UsersService;
  let userModel: any;

  const mockUser = {
    _id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    provider: 'local',
    emailVerified: true,
    verificationCode: null,
    verificationCodeExpiresAt: null,
    verificationCodeSentAt: null,
    tokenBalanceId: 'mock-token-balance-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUserModel = createMockMongooseModel(mockUser);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get(getModelToken(User.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const createUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        provider: 'local' as const,
        emailVerified: false,
      };

      userModel.create.mockResolvedValue(mockUser);

      const result = await service.create(createUserDto);

      expect(result).toEqual(mockUser);
      expect(userModel.create).toHaveBeenCalledWith(createUserDto);
    });

    it('should handle creation errors', async () => {
      const createUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        provider: 'local' as const,
        emailVerified: false,
      };

      userModel.create.mockRejectedValue(new Error('Database error'));

      await expect(service.create(createUserDto)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findOneByEmail', () => {
    it('should find user by email', async () => {
      const email = '<EMAIL>';

      userModel.findOne.mockResolvedValue(mockUser);

      const result = await service.findOneByEmail(email);

      expect(result).toEqual(mockUser);
      expect(userModel.findOne).toHaveBeenCalledWith({ email });
    });

    it('should return null if user not found', async () => {
      const email = '<EMAIL>';

      userModel.findOne.mockResolvedValue(null);

      const result = await service.findOneByEmail(email);

      expect(result).toBeNull();
    });
  });

  describe('findOne', () => {
    it('should find user by id', async () => {
      const userId = 'mock-user-id';

      userModel.findById.mockResolvedValue(mockUser);

      const result = await service.findOne(userId);

      expect(result).toEqual(mockUser);
      expect(userModel.findById).toHaveBeenCalledWith(userId);
    });

    it('should return null if user not found', async () => {
      const userId = 'nonexistent-id';

      userModel.findById.mockResolvedValue(null);

      const result = await service.findOne(userId);

      expect(result).toBeNull();
    });
  });

  describe('sendVerificationCode', () => {
    it('should generate and store verification code for existing user', async () => {
      const email = '<EMAIL>';
      const mockExistingUser = {
        ...mockUser,
        save: jest.fn().mockResolvedValue(mockUser),
      };

      userModel.findOne.mockResolvedValue(mockExistingUser);

      await service.sendVerificationCode(email);

      expect(userModel.findOne).toHaveBeenCalledWith({ email });
      expect(mockExistingUser.save).toHaveBeenCalled();
      expect(mockExistingUser.verificationCode).toBeDefined();
      expect(mockExistingUser.verificationCodeSentAt).toBeDefined();
      expect(mockExistingUser.verificationCodeExpiresAt).toBeDefined();
    });

    it('should create new user and send verification code if user not found', async () => {
      const email = '<EMAIL>';
      const mockNewUser = {
        ...mockUser,
        email,
        name: 'newuser',
        save: jest.fn().mockResolvedValue(mockUser),
      };

      userModel.findOne.mockResolvedValue(null);
      userModel.create.mockResolvedValue(mockNewUser);

      await service.sendVerificationCode(email);

      expect(userModel.findOne).toHaveBeenCalledWith({ email });
      expect(userModel.create).toHaveBeenCalledWith({
        email,
        name: 'newuser',
      });
      expect(mockNewUser.save).toHaveBeenCalled();
    });
  });

  describe('verifyCode', () => {
    it('should verify valid code successfully', async () => {
      const email = '<EMAIL>';
      const code = '123456';
      const userWithCode = {
        ...mockUser,
        verificationCode: code,
        verificationCodeExpiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
        save: jest.fn().mockResolvedValue(true),
      };

      userModel.findOne.mockResolvedValue(userWithCode);

      const result = await service.verifyCode(email, code);

      expect(result).toBe(true);
      expect(userWithCode.save).toHaveBeenCalled();
    });

    it('should reject invalid code', async () => {
      const email = '<EMAIL>';
      const code = '123456';
      const userWithCode = {
        ...mockUser,
        verificationCode: '654321', // Different code
        verificationCodeExpiresAt: new Date(Date.now() + 10 * 60 * 1000),
      };

      userModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(userWithCode),
      });

      const result = await service.verifyCode(email, code);

      expect(result).toBe(false);
    });

    it('should reject expired code', async () => {
      const email = '<EMAIL>';
      const code = '123456';
      const userWithCode = {
        ...mockUser,
        verificationCode: code,
        verificationCodeExpiresAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      };

      userModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(userWithCode),
      });

      const result = await service.verifyCode(email, code);

      expect(result).toBe(false);
    });

    it('should reject if user not found', async () => {
      const email = '<EMAIL>';
      const code = '123456';

      userModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.verifyCode(email, code);

      expect(result).toBe(false);
    });

    it('should reject if no verification code set', async () => {
      const email = '<EMAIL>';
      const code = '123456';
      const userWithoutCode = {
        ...mockUser,
        verificationCode: undefined,
        verificationCodeExpiresAt: undefined,
      };

      userModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(userWithoutCode),
      });

      const result = await service.verifyCode(email, code);

      expect(result).toBe(false);
    });
  });

  describe('generateVerificationCode', () => {
    it('should generate a 6-digit verification code', async () => {
      const email = '<EMAIL>';

      const result = await service.generateVerificationCode(email);

      expect(result).toMatch(/^\d{6}$/);
      expect(result.length).toBe(6);
    });
  });
});
