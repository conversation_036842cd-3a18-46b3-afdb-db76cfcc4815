/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateUserDto } from './dto/create.dto';
import { User } from './schemas/user.schema';
import { EmailService } from '../common/email.service';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    private emailService: EmailService,
  ) { }

  async findOne(id: string): Promise<User | null> {
    return this.userModel.findById(id);
  }

  async findOneByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email });
  }

  async create(user: CreateUserDto): Promise<User> {
    return this.userModel.create(user);
  }

  async generateVerificationCode(email: string): Promise<string> {
    // Special <NAME_EMAIL> - always return 000000
    if (email === '<EMAIL>') {
      return '000000';
    }

    const code = Math.floor(100000 + Math.random() * 900000).toString();
    return code;
  }

  async verifyCode(email: string, code: string): Promise<boolean> {
    const user = await this.userModel.findOne({ email });
    if (!user) {
      return false;
    }

    if (
      user.verificationCodeExpiresAt &&
      user.verificationCodeExpiresAt < new Date()
    ) {
      return false;
    }

    if (user.verificationCode === code) {
      const wasNotVerified = !user.emailVerified;
      user.emailVerified = true;
      await user.save();

      // Send welcome email for new users
      if (wasNotVerified) {
        try {
          await this.sendWelcomeEmail(email, user.name);
        } catch (error) {
          this.logger.warn(`Failed to send welcome email to ${email}: ${error.message}`);
          // Don't fail verification if welcome email fails
        }
      }

      return true;
    }

    return false;
  }

  async sendVerificationCode(email: string): Promise<void> {
    const code = await this.generateVerificationCode(email);
    let user = await this.userModel.findOne({ email });

    if (!user) {
      user = await this.userModel.create({ email, name: email.split('@')[0] });
    }
    user.verificationCode = code;
    user.verificationCodeSentAt = new Date();
    const verificationCodeExpiresAt = new Date();
    verificationCodeExpiresAt.setMinutes(
      verificationCodeExpiresAt.getMinutes() + 10,
    );
    user.verificationCodeExpiresAt = verificationCodeExpiresAt;

    await user.save();

    // Send the verification code via email
    try {
      const emailResult = await this.emailService.sendVerificationCode(email, code);
      if (emailResult.success) {
        this.logger.log(`Verification code sent successfully to ${email}`);
      } else {
        this.logger.error(`Failed to send verification code to ${email}: ${emailResult.error}`);
        throw new Error('Failed to send verification email');
      }
    } catch (error) {
      this.logger.error(`Error sending verification code to ${email}: ${error.message}`);
      throw new Error('Failed to send verification email');
    }
  }

  /**
   * Send welcome email to a user
   * @param email User's email
   * @param name User's name
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      const emailResult = await this.emailService.sendWelcomeEmail(email, name);
      if (emailResult.success) {
        this.logger.log(`Welcome email sent successfully to ${email}`);
      } else {
        this.logger.error(`Failed to send welcome email to ${email}: ${emailResult.error}`);
        throw new Error('Failed to send welcome email');
      }
    } catch (error) {
      this.logger.error(`Error sending welcome email to ${email}: ${error.message}`);
      throw new Error('Failed to send welcome email');
    }
  }
}
