import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserPreferences } from '../schemas/user-preferences.schema';
import { NotificationPreferencesDto } from '../dto/notification-preferences.dto';
import { User } from '../schemas/user.schema';

@Injectable()
export class UserPreferencesService {
  private readonly logger = new Logger(UserPreferencesService.name);

  constructor(
    @InjectModel(UserPreferences.name)
    private userPreferencesModel: Model<UserPreferences>,
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  /**
   * Get user preferences by user ID
   * @param userId User ID
   * @returns User preferences
   */
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    const preferences = await this.userPreferencesModel.findOne({ userId });

    if (!preferences) {
      // Create default preferences if none exist
      return this.initializeUserPreferences(userId);
    }

    return preferences;
  }

  /**
   * Initialize user preferences with default values
   * @param userId User ID
   * @returns Newly created user preferences
   */
  async initializeUserPreferences(userId: string): Promise<UserPreferences> {
    // Check if user exists
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if preferences already exist
    const existingPreferences = await this.userPreferencesModel.findOne({
      userId,
    });
    if (existingPreferences) {
      return existingPreferences;
    }

    // Create default preferences
    const newPreferences = new this.userPreferencesModel({
      userId,
      notifications: {
        trip_updates: true,
        new_features: true,
        credit_balance: true,
        special_offers: false,
        travel_tips: true,
      },
    });

    const savedPreferences = await newPreferences.save();

    // Update user with reference to preferences
    await this.userModel.findByIdAndUpdate(userId, {
      preferencesId: savedPreferences._id,
    });

    return savedPreferences;
  }

  /**
   * Update notification preferences
   * @param userId User ID
   * @param notificationPreferences Notification preferences to update
   * @returns Updated user preferences
   */
  async updateNotificationPreferences(
    userId: string,
    notificationPreferences: NotificationPreferencesDto,
  ): Promise<UserPreferences> {
    const preferences = await this.getUserPreferences(userId);

    // Update only the provided fields
    const updatedNotifications = {
      ...preferences.notifications,
      ...notificationPreferences,
    };

    // Update the preferences
    preferences.notifications = updatedNotifications;
    return preferences.save();
  }

  /**
   * Check if a specific notification type is enabled for a user
   * @param userId User ID
   * @param notificationType Notification type to check
   * @returns Boolean indicating if the notification type is enabled
   */
  async isNotificationEnabled(
    userId: string,
    notificationType: string,
  ): Promise<boolean> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.notifications[notificationType] === true;
  }
}
