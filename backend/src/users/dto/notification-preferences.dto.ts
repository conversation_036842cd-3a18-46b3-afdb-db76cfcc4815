import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class NotificationPreferencesDto {
  @ApiProperty({
    description: 'Receive trip update notifications',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  trip_updates?: boolean;

  @ApiProperty({
    description: 'Receive new features notifications',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  new_features?: boolean;

  @ApiProperty({
    description: 'Receive credit balance notifications',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  credit_balance?: boolean;

  @ApiProperty({
    description: 'Receive special offers notifications',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  special_offers?: boolean;

  @ApiProperty({
    description: 'Receive travel tips notifications',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  travel_tips?: boolean;
}
