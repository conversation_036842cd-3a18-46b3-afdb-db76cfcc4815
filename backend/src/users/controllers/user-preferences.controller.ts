import {
  Controller,
  Get,
  Patch,
  Body,
  UseGuards,
  Request,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserPreferencesService } from '../services/user-preferences.service';
import { UserPreferences } from '../schemas/user-preferences.schema';
import { NotificationPreferencesDto } from '../dto/notification-preferences.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt.guard';

// Define request type with user property
interface RequestWithUser {
  user: {
    sub: string;
    email: string;
    [key: string]: any;
  };
}

@ApiTags('user-preferences')
@Controller('user-preferences')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserPreferencesController {
  private readonly logger = new Logger(UserPreferencesController.name);

  constructor(
    private readonly userPreferencesService: UserPreferencesService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get user preferences' })
  @ApiResponse({ status: 200, description: 'Returns the user preferences' })
  async getUserPreferences(
    @Request() req: RequestWithUser,
  ): Promise<UserPreferences> {
    return this.userPreferencesService.getUserPreferences(req.user.sub);
  }

  @Patch('notifications')
  @ApiOperation({ summary: 'Update notification preferences' })
  @ApiResponse({
    status: 200,
    description: 'Notification preferences updated successfully',
  })
  async updateNotificationPreferences(
    @Request() req: RequestWithUser,
    @Body() notificationPreferencesDto: NotificationPreferencesDto,
  ): Promise<UserPreferences> {
    this.logger.log(
      `Updating notification preferences for user ${req.user.sub}`,
    );
    return this.userPreferencesService.updateNotificationPreferences(
      req.user.sub,
      notificationPreferencesDto,
    );
  }
}
