import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum TransactionType {
  DEBIT = 'debit',
  CREDIT = 'credit',
}

export enum TransactionSource {
  TRIP_GENERATION = 'trip_generation',
  SUBSCRIPTION_RENEWAL = 'subscription_renewal',
  DAY_PACK_PURCHASE = 'day_pack_purchase',
  INITIAL_GRANT = 'initial_grant',
  MANUAL_ADDITION = 'manual_addition',
}

@Schema({ timestamps: true })
export class DayTransaction extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: string;

  @Prop({ required: true, enum: TransactionType })
  type: TransactionType;

  @Prop({ required: true })
  amount: number;

  @Prop({ required: true })
  balanceAfter: number;

  @Prop({ required: true, enum: TransactionSource })
  source: TransactionSource;

  @Prop({ type: Types.ObjectId, ref: 'Trip', required: false })
  tripId?: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ type: Object, required: false })
  metadata?: Record<string, any>;

  @Prop({ required: false })
  expiresAt?: Date; // For day packs that expire at end of subscription cycle
}

export const DayTransactionSchema =
  SchemaFactory.createForClass(DayTransaction);
