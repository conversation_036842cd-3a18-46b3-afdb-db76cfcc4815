import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum SubscriptionPlan {
  BASIC = 'free',
  RECOMMENDED = 'pro',
  PREMIUM = 'premium',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  PENDING = 'pending',
}

@Schema({ timestamps: true })
export class DayBalance extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, unique: true })
  userId: string;

  @Prop({ required: true, default: 0 })
  subscriptionDays: number; // Days from current subscription

  @Prop({ required: true, default: 0 })
  packDays: number; // Days from purchased packs

  @Prop({ required: true, default: 0 })
  totalDaysUsed: number;

  @Prop({ required: true, default: 0 })
  totalDaysAdded: number;

  @Prop({ required: true, enum: SubscriptionPlan, default: SubscriptionPlan.BASIC })
  currentPlan: SubscriptionPlan;

  @Prop({ required: true, enum: SubscriptionStatus, default: SubscriptionStatus.PENDING })
  subscriptionStatus: SubscriptionStatus;

  @Prop({ required: false })
  subscriptionStartDate?: Date;

  @Prop({ required: false })
  subscriptionEndDate?: Date;

  @Prop({ required: false })
  nextBillingDate?: Date;

  @Prop({ required: false })
  lastResetDate?: Date; // Last time subscription days were reset

  @Prop({ type: Object, required: false })
  pendingPlanChange?: {
    newPlan: SubscriptionPlan;
    oldPlan: SubscriptionPlan;
    changeDate: Date;
    effectiveDate: Date;
  };

  @Prop({ type: Object, required: false })
  scheduledCancellation?: {
    cancelDate: Date;
    reason: string;
    scheduledAt: Date;
  };

  @Prop({ type: Object, required: false })
  gracePeriod?: {
    startDate: Date;
    endDate: Date;
    retriesRemaining: number;
  };
}

export const DayBalanceSchema = SchemaFactory.createForClass(DayBalance);
