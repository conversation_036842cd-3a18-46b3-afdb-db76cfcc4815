import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
class NotificationPreferences {
  @Prop({ required: true, default: true })
  trip_updates: boolean;

  @Prop({ required: true, default: true })
  new_features: boolean;

  @Prop({ required: true, default: true })
  credit_balance: boolean;

  @Prop({ required: true, default: false })
  special_offers: boolean;

  @Prop({ required: true, default: true })
  travel_tips: boolean;
}

@Schema({ timestamps: true })
export class UserPreferences extends Document {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  })
  userId: string;

  @Prop({ type: NotificationPreferences, default: () => ({}) })
  notifications: NotificationPreferences;
}

export const UserPreferencesSchema =
  SchemaFactory.createForClass(UserPreferences);
