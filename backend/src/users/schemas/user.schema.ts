import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class User extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: false, default: null })
  provider: string;

  @Prop({ required: false, default: null })
  providerId: string;

  @Prop({ required: false, default: false })
  emailVerified: boolean;

  @Prop({ required: false, default: null })
  verificationCode: string;

  @Prop({ required: false, default: null })
  verificationCodeExpiresAt: Date;

  @Prop({ required: false, default: null })
  verificationCodeSentAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'DayBalance' })
  dayBalanceId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'UserPreferences' })
  preferencesId: Types.ObjectId;
}

export const UserSchema = SchemaFactory.createForClass(User);
