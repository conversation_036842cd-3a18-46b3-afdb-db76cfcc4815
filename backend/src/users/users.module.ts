import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './schemas/user.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from './schemas/user.schema';
import {
  UserPreferences,
  UserPreferencesSchema,
} from './schemas/user-preferences.schema';
import { UserPreferencesService } from './services/user-preferences.service';
import { UserPreferencesController } from './controllers/user-preferences.controller';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { CommonModule } from '../common/common.module';

@Module({
  controllers: [UserPreferencesController],
  providers: [UsersService, UserPreferencesService],
  exports: [UsersService, UserPreferencesService],
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserPreferences.name, schema: UserPreferencesSchema },
    ]),
    ConfigModule,
    CommonModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
  ],
})
export class UsersModule { }
