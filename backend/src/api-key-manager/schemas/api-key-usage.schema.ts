import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProvider } from '../../config/api-keys';

@Schema({ timestamps: true })
export class Api<PERSON>eyUsage extends Document {
  @Prop({ required: true, index: true })
  keyId: string;

  @Prop({ required: true, enum: ApiProvider })
  provider: ApiProvider;

  @Prop({ required: true })
  key: string;

  @Prop({ required: true, default: 0 })
  totalRequests: number;

  @Prop({ required: true, default: 0 })
  totalTokensUsed: number;

  @Prop({ required: true, default: 0 })
  successfulRequests: number;

  @Prop({ required: true, default: 0 })
  failedRequests: number;

  @Prop({ required: true, default: 0 })
  rateLimitErrors: number;

  @Prop({ required: true, default: 0 })
  authErrors: number;

  @Prop({ required: true, default: 0 })
  serverErrors: number;

  @Prop({ required: true, default: 0 })
  otherErrors: number;

  @Prop({ required: true, default: 0 })
  consecutiveErrors: number;

  @Prop({ required: true, default: true })
  isActive: boolean;

  @Prop({ type: Date, default: null })
  lastErrorAt: Date;

  @Prop({ type: Date, default: null })
  lastSuccessAt: Date;

  @Prop({ type: Date, default: null })
  disabledAt: Date;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  // Current usage window tracking
  @Prop({ required: true, default: 0 })
  requestsInCurrentWindow: number;

  @Prop({ required: true, default: 0 })
  tokensInCurrentWindow: number;

  @Prop({ type: Date, default: Date.now })
  currentWindowStartedAt: Date;
}

export const ApiKeyUsageSchema = SchemaFactory.createForClass(ApiKeyUsage);

// Create indexes for efficient querying
ApiKeyUsageSchema.index({ provider: 1, isActive: 1 });
ApiKeyUsageSchema.index({ lastErrorAt: 1 });
ApiKeyUsageSchema.index({ lastSuccessAt: 1 });
