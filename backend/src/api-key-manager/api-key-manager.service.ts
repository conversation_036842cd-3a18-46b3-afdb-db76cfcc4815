/* eslint-disable */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { createHash } from 'crypto';
import { Model } from 'mongoose';
import {
  ApiKeyConfig,
  ApiProvider,
  apiKeyManagerConfig,
} from '../config/api-keys';
import { ConfigService } from '../config/config.service';
import { ApiKeyUsage } from './schemas/api-key-usage.schema';

@Injectable()
export class ApiKeyManagerService implements OnModuleInit {
  private readonly logger = new Logger(ApiKeyManagerService.name);
  private apiKeys: Map<string, ApiKeyConfig & { id: string }> = new Map();
  private keysByProvider: Map<ApiProvider, string[]> = new Map();
  private keyUsage: Map<
    string,
    {
      requests: number;
      tokens: number;
      lastUsed: Date;
      consecutiveErrors: number;
      lastError: Date | null;
    }
  > = new Map();

  constructor(
    @InjectModel(ApiKeyUsage.name) private apiKeyUsageModel: Model<ApiKeyUsage>,
    private configService: ConfigService,
  ) { }

  async onModuleInit() {
    await this.initializeApiKeys();
    this.startUsageResetInterval();
  }

  /**
   * Generate a consistent key ID based on provider and key value
   * This ensures the same key gets the same ID across application restarts
   */
  private generateKeyId(provider: ApiProvider, key: string): string {
    return createHash('sha256')
      .update(`${provider}:${key}`)
      .digest('hex')
      .substring(0, 36); // Use first 36 chars to match UUID length
  }

  /**
   * Load API keys from configuration and initialize the key manager
   */
  private async initializeApiKeys() {
    this.logger.log('Initializing API key manager');

    // Initialize maps for each provider
    Object.values(ApiProvider).forEach((provider) => {
      this.keysByProvider.set(provider, []);
    });

    // Clean up any duplicate records in the database
    await this.cleanupDuplicateRecords();

    // Load keys from configuration service
    const apiKeys: ApiKeyConfig[] = [
      // OpenAI keys
      {
        key: this.configService.get('ai.openaiApiKey', ''),
        provider: ApiProvider.OPENAI,
        weight: 10,
        rateLimit: {
          requestsPerMinute: 60,
          tokensPerMinute: 90000,
        },
        isActive: true,
        description: 'Primary OpenAI key',
      },
      {
        key: this.configService.get('ai.openaiApiKeyBackup', ''),
        provider: ApiProvider.OPENAI,
        weight: 5,
        rateLimit: {
          requestsPerMinute: 60,
          tokensPerMinute: 90000,
        },
        isActive: true,
        description: 'Backup OpenAI key',
      },
      // Groq keys
      {
        key: this.configService.get('ai.groqApiKey', ''),
        provider: ApiProvider.GROQ,
        weight: 10,
        rateLimit: {
          requestsPerMinute: 100,
        },
        isActive: true,
        description: 'Primary Groq key',
      },
      {
        key: this.configService.get('ai.groqApiKeyBackup', ''),
        provider: ApiProvider.GROQ,
        weight: 5,
        rateLimit: {
          requestsPerMinute: 100,
        },
        isActive: true,
        description: 'Backup Groq key',
      },
      // DeepSeek keys
      {
        key: this.configService.get('ai.deepseekApiKey', ''),
        provider: ApiProvider.DEEPSEEK,
        weight: 10,
        rateLimit: {
          requestsPerMinute: 50,
        },
        isActive: true,
        description: 'Primary DeepSeek key',
      },
      {
        key: this.configService.get('ai.deepseekApiKeyBackup', ''),
        provider: ApiProvider.DEEPSEEK,
        weight: 5,
        rateLimit: {
          requestsPerMinute: 50,
        },
        isActive: true,
        description: 'Backup DeepSeek key',
      },
    ];

    // Process each API key
    for (const keyConfig of apiKeys) {
      if (!keyConfig.key) {
        this.logger.warn(
          `Skipping empty API key for provider ${keyConfig.provider}`,
        );
        continue;
      }

      // Generate a consistent key ID based on provider and key value
      const keyId = this.generateKeyId(keyConfig.provider, keyConfig.key);
      const keyWithId = { ...keyConfig, id: keyId };

      this.apiKeys.set(keyId, keyWithId);

      const providerKeys = this.keysByProvider.get(keyConfig.provider) || [];
      providerKeys.push(keyId);
      this.keysByProvider.set(keyConfig.provider, providerKeys);

      this.keyUsage.set(keyId, {
        requests: 0,
        tokens: 0,
        lastUsed: new Date(),
        consecutiveErrors: 0,
        lastError: null,
      });

      // Create or update usage record in database
      await this.createOrUpdateKeyUsage(keyWithId);
    }

    this.logger.log(
      `Initialized ${this.apiKeys.size} API keys across ${this.keysByProvider.size} providers`,
    );

    // Log the number of keys per provider
    this.keysByProvider.forEach((keys, provider) => {
      this.logger.log(`Provider ${provider}: ${keys.length} keys`);
    });
  }

  /**
   * Clean up duplicate records in the database
   * This helps resolve the issue of multiple records for the same API key
   */
  private async cleanupDuplicateRecords() {
    try {
      // Find all unique provider/key combinations
      const uniqueKeys = await this.apiKeyUsageModel.aggregate([
        {
          $group: {
            _id: { provider: '$provider', key: '$key' },
            keyIds: { $push: '$keyId' },
            docIds: { $push: '$_id' },
            count: { $sum: 1 },
          },
        },
        {
          $match: {
            count: { $gt: 1 }, // Only get combinations with more than one record
          },
        },
      ]);

      // Process each set of duplicates
      for (const keyGroup of uniqueKeys) {
        this.logger.warn(
          `Found ${keyGroup.count} duplicate records for ${keyGroup._id.provider} key`,
        );

        // Keep the most recently updated record and delete the rest
        const records = await this.apiKeyUsageModel
          .find({
            provider: keyGroup._id.provider,
            key: keyGroup._id.key,
          })
          .sort({ updatedAt: -1 });

        // Keep the first record (most recently updated) and delete the rest
        if (records.length > 1) {
          const idsToDelete = records.slice(1).map((r) => r._id);
          await this.apiKeyUsageModel.deleteMany({ _id: { $in: idsToDelete } });
          this.logger.log(
            `Deleted ${idsToDelete.length} duplicate records for ${keyGroup._id.provider} key`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Error cleaning up duplicate records: ${error.message}`,
      );
    }
  }

  /**
   * Create or update a key usage record in the database
   * This method ensures we don't create duplicate records for the same API key
   */
  private async createOrUpdateKeyUsage(
    keyConfig: ApiKeyConfig & { id: string },
  ) {
    try {
      // First check if a record already exists for this key by provider and key value
      // This is more reliable than using keyId which might have changed in previous versions
      const existingRecord = await this.apiKeyUsageModel.findOne({
        key: keyConfig.key,
        provider: keyConfig.provider,
      });

      if (existingRecord) {
        // Update the existing record with the new keyId to ensure consistency
        existingRecord.keyId = keyConfig.id;
        existingRecord.isActive = keyConfig.isActive;
        existingRecord.metadata = {
          weight: keyConfig.weight,
          rateLimit: keyConfig.rateLimit,
          description: keyConfig.description,
        };
        await existingRecord.save();
        this.logger.debug(
          `Updated existing API key record for ${keyConfig.provider}`,
        );
      } else {
        // Create a new record
        const newKeyUsage = new this.apiKeyUsageModel({
          keyId: keyConfig.id,
          key: keyConfig.key,
          provider: keyConfig.provider,
          isActive: keyConfig.isActive,
          metadata: {
            weight: keyConfig.weight,
            rateLimit: keyConfig.rateLimit,
            description: keyConfig.description,
          },
        });
        await newKeyUsage.save();
        this.logger.debug(
          `Created new API key record for ${keyConfig.provider}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error creating/updating key usage record: ${error.message}`,
      );
    }
  }

  private startUsageResetInterval() {
    setInterval(() => {
      this.resetUsageCounters();
    }, apiKeyManagerConfig.usageResetInterval);
  }

  private resetUsageCounters() {
    this.logger.debug('Resetting API key usage counters');

    // Reset in-memory counters
    for (const keyId of this.apiKeys.keys()) {
      const usage = this.keyUsage.get(keyId);
      if (usage) {
        usage.requests = 0;
        usage.tokens = 0;
      }
    }

    // Reset database counters
    this.apiKeyUsageModel
      .updateMany(
        {},
        {
          $set: {
            requestsInCurrentWindow: 0,
            tokensInCurrentWindow: 0,
            currentWindowStartedAt: new Date(),
          },
        },
      )
      .catch((error) => {
        this.logger.error(
          `Error resetting usage counters in database: ${error.message}`,
        );
      });
  }

  /**
   * Get an API key for the specified provider
   * Uses a weighted selection algorithm that considers:
   * - Key weight
   * - Current usage
   * - Error history
   */
  async getApiKey(provider: ApiProvider): Promise<string> {
    const providerKeys = this.keysByProvider.get(provider);

    if (!providerKeys || providerKeys.length === 0) {
      throw new Error(`No API keys available for provider ${provider}`);
    }

    // Get active keys and their configurations
    const activeKeyIds = providerKeys.filter((keyId) => {
      const key = this.apiKeys.get(keyId);
      return key && key.isActive;
    });

    if (activeKeyIds.length === 0) {
      throw new Error(`No active API keys available for provider ${provider}`);
    }

    // Calculate scores for each key based on weight, usage, and error history
    const keyScores = activeKeyIds.map((keyId) => {
      const key = this.apiKeys.get(keyId);
      const usage = this.keyUsage.get(keyId);

      if (!key || !usage) {
        return { keyId, score: 0 };
      }

      // Base score is the key's weight
      let score = key.weight;

      // Reduce score based on current usage relative to rate limits
      const usageRatio = usage.requests / key.rateLimit.requestsPerMinute;
      score *= 1 - usageRatio;

      // Reduce score based on consecutive errors
      if (usage.consecutiveErrors > 0) {
        score /= usage.consecutiveErrors + 1;
      }

      // If the key had a recent error, reduce its score further
      if (usage.lastError) {
        const timeSinceError = Date.now() - usage.lastError.getTime();
        if (timeSinceError < apiKeyManagerConfig.problematicKeyRetryDelay) {
          score *=
            timeSinceError / apiKeyManagerConfig.problematicKeyRetryDelay;
        }
      }

      return { keyId, score: Math.max(score, 0.1) }; // Ensure minimum score
    });

    // Sort by score (highest first)
    keyScores.sort((a, b) => b.score - a.score);

    // Get the key with the highest score
    const selectedKeyId = keyScores[0].keyId;
    const selectedKey = this.apiKeys.get(selectedKeyId);

    if (!selectedKey) {
      throw new Error(`Selected key ${selectedKeyId} not found`);
    }

    // Update usage statistics
    this.recordKeyUsage(selectedKeyId);

    return selectedKey.key;
  }

  /**
   * Record that a key was used
   * This method updates both in-memory counters and database records
   */
  private async recordKeyUsage(keyId: string, tokens: number = 0) {
    const usage = this.keyUsage.get(keyId);
    const key = this.apiKeys.get(keyId);

    if (!usage || !key) {
      this.logger.warn(
        `Attempted to record usage for unknown key ID: ${keyId}`,
      );
      return;
    }

    // Update in-memory counters
    usage.requests += 1;
    usage.tokens += tokens;
    usage.lastUsed = new Date();

    // Update database
    try {
      // First check if the record exists
      const record = await this.apiKeyUsageModel.findOne({ keyId });

      if (record) {
        // Update existing record
        await this.apiKeyUsageModel.updateOne(
          { keyId },
          {
            $inc: {
              totalRequests: 1,
              totalTokensUsed: tokens,
              requestsInCurrentWindow: 1,
              tokensInCurrentWindow: tokens,
            },
            $set: {
              lastSuccessAt: new Date(),
            },
          },
        );
      } else {
        // If record doesn't exist (which shouldn't happen but just in case),
        // try to find by provider and key value
        const recordByKey = await this.apiKeyUsageModel.findOne({
          provider: key.provider,
          key: key.key,
        });

        if (recordByKey) {
          // Update the record and set the correct keyId
          recordByKey.keyId = keyId;
          recordByKey.totalRequests += 1;
          recordByKey.totalTokensUsed += tokens;
          recordByKey.requestsInCurrentWindow += 1;
          recordByKey.tokensInCurrentWindow += tokens;
          recordByKey.lastSuccessAt = new Date();
          await recordByKey.save();
        } else {
          // This should never happen if initialization was successful
          this.logger.error(`No database record found for key ID ${keyId}`);

          // Create a new record as a fallback
          await this.createOrUpdateKeyUsage(key);
        }
      }
    } catch (error) {
      this.logger.error(`Error updating key usage: ${error.message}`);
    }
  }

  /**
   * Record a successful API call
   * This method updates both in-memory counters and database records
   */
  async recordSuccess(
    provider: ApiProvider,
    apiKey: string,
    tokens: number = 0,
  ) {
    if (!apiKey) {
      this.logger.warn(
        `Attempted to record success for empty API key for provider ${provider}`,
      );
      return;
    }

    const keyId = this.findKeyIdByValue(apiKey);
    if (!keyId) {
      this.logger.warn(`Unknown API key used for provider ${provider}`);
      return;
    }

    const usage = this.keyUsage.get(keyId);
    if (usage) {
      // Update in-memory counters
      usage.requests += 1;
      usage.tokens += tokens;
      usage.lastUsed = new Date();

      // Reset consecutive errors
      usage.consecutiveErrors = 0;
      usage.lastError = null;
    }

    // Update database
    try {
      // First check if the record exists
      const record = await this.apiKeyUsageModel.findOne({ keyId });

      if (record) {
        // Update existing record
        await this.apiKeyUsageModel.updateOne(
          { keyId },
          {
            $inc: {
              totalRequests: 1,
              successfulRequests: 1,
              totalTokensUsed: tokens,
              requestsInCurrentWindow: 1,
              tokensInCurrentWindow: tokens,
            },
            $set: {
              lastSuccessAt: new Date(),
              consecutiveErrors: 0,
            },
          },
        );
      } else {
        // If record doesn't exist, try to find by provider and key value
        const recordByKey = await this.apiKeyUsageModel.findOne({
          provider,
          key: apiKey,
        });

        if (recordByKey) {
          // Update the record and set the correct keyId
          recordByKey.keyId = keyId;
          recordByKey.totalRequests += 1;
          recordByKey.successfulRequests += 1;
          recordByKey.totalTokensUsed += tokens;
          recordByKey.requestsInCurrentWindow += 1;
          recordByKey.tokensInCurrentWindow += tokens;
          recordByKey.lastSuccessAt = new Date();
          recordByKey.consecutiveErrors = 0;
          await recordByKey.save();
        } else {
          // This should never happen if initialization was successful
          this.logger.error(`No database record found for key ID ${keyId}`);

          // Create a new record as a fallback
          const key = this.apiKeys.get(keyId);
          if (key) {
            await this.createOrUpdateKeyUsage(key);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error recording success: ${error.message}`);
    }
  }

  /**
   * Record a failed API call
   * This method updates both in-memory counters and database records
   */
  async recordError(
    provider: ApiProvider,
    apiKey: string,
    errorType: 'rate_limit' | 'auth' | 'server' | 'other',
  ) {
    if (!apiKey) {
      this.logger.warn(
        `Attempted to record error for empty API key for provider ${provider}`,
      );
      return;
    }

    const keyId = this.findKeyIdByValue(apiKey);
    if (!keyId) {
      this.logger.warn(`Unknown API key used for provider ${provider}`);
      return;
    }

    const usage = this.keyUsage.get(keyId);
    const key = this.apiKeys.get(keyId);

    if (!usage || !key) {
      this.logger.warn(`No usage or key data found for key ID ${keyId}`);
      return;
    }

    // Update in-memory counters
    usage.requests += 1;
    usage.lastUsed = new Date();
    usage.consecutiveErrors += 1;
    usage.lastError = new Date();

    // Check if we should disable the key
    if (
      usage.consecutiveErrors >= apiKeyManagerConfig.maxConsecutiveErrors &&
      apiKeyManagerConfig.autoDisableProblematicKeys
    ) {
      key.isActive = false;
      this.logger.warn(
        `Automatically disabled API key ${keyId} for provider ${provider} due to ${usage.consecutiveErrors} consecutive errors`,
      );
    }

    // Update database
    const updateQuery: any = {
      $inc: {
        totalRequests: 1,
        failedRequests: 1,
        consecutiveErrors: 1,
        requestsInCurrentWindow: 1,
      },
      $set: {
        lastErrorAt: new Date(),
      },
    };

    // Increment the specific error counter
    switch (errorType) {
      case 'rate_limit':
        updateQuery.$inc.rateLimitErrors = 1;
        break;
      case 'auth':
        updateQuery.$inc.authErrors = 1;
        break;
      case 'server':
        updateQuery.$inc.serverErrors = 1;
        break;
      case 'other':
        updateQuery.$inc.otherErrors = 1;
        break;
    }

    // If the key is being disabled, update that too
    if (!key.isActive) {
      updateQuery.$set.isActive = false;
      updateQuery.$set.disabledAt = new Date();
    }

    try {
      // First check if the record exists
      const record = await this.apiKeyUsageModel.findOne({ keyId });

      if (record) {
        // Update existing record
        await this.apiKeyUsageModel.updateOne({ keyId }, updateQuery);
      } else {
        // If record doesn't exist, try to find by provider and key value
        const recordByKey = await this.apiKeyUsageModel.findOne({
          provider,
          key: apiKey,
        });

        if (recordByKey) {
          // Update the record and set the correct keyId
          recordByKey.keyId = keyId;
          recordByKey.totalRequests = (recordByKey.totalRequests || 0) + 1;
          recordByKey.failedRequests = (recordByKey.failedRequests || 0) + 1;
          recordByKey.consecutiveErrors =
            (recordByKey.consecutiveErrors || 0) + 1;
          recordByKey.requestsInCurrentWindow =
            (recordByKey.requestsInCurrentWindow || 0) + 1;
          recordByKey.lastErrorAt = new Date();

          // Update the specific error counter
          switch (errorType) {
            case 'rate_limit':
              recordByKey.rateLimitErrors =
                (recordByKey.rateLimitErrors || 0) + 1;
              break;
            case 'auth':
              recordByKey.authErrors = (recordByKey.authErrors || 0) + 1;
              break;
            case 'server':
              recordByKey.serverErrors = (recordByKey.serverErrors || 0) + 1;
              break;
            case 'other':
              recordByKey.otherErrors = (recordByKey.otherErrors || 0) + 1;
              break;
          }

          // If the key is being disabled, update that too
          if (!key.isActive) {
            recordByKey.isActive = false;
            recordByKey.disabledAt = new Date();
          }

          await recordByKey.save();
        } else {
          // This should never happen if initialization was successful
          this.logger.error(`No database record found for key ID ${keyId}`);

          // Create a new record as a fallback
          await this.createOrUpdateKeyUsage(key);

          // Try the update again
          await this.apiKeyUsageModel.updateOne({ keyId }, updateQuery);
        }
      }
    } catch (error) {
      this.logger.error(`Error recording error: ${error.message}`);
    }
  }

  /**
   * Find a key ID by its value
   * This method is optimized to find the key ID without iterating through all keys
   */
  private findKeyIdByValue(apiKey: string): string | null {
    // First try to find the provider for this key
    let provider: ApiProvider | null = null;

    // Check if this is an OpenAI key
    if (
      apiKey === this.configService.get('ai.openaiApiKey', '') ||
      apiKey === this.configService.get('ai.openaiApiKeyBackup', '')
    ) {
      provider = ApiProvider.OPENAI;
    }
    // Check if this is a Groq key
    else if (
      apiKey === this.configService.get('ai.groqApiKey', '') ||
      apiKey === this.configService.get('ai.groqApiKeyBackup', '')
    ) {
      provider = ApiProvider.GROQ;
    }
    // Check if this is a DeepSeek key
    else if (apiKey === this.configService.get('ai.deepseekApiKey', '')) {
      provider = ApiProvider.DEEPSEEK;
    }

    // If we found the provider, generate the key ID directly
    if (provider) {
      return this.generateKeyId(provider, apiKey);
    }

    // Fallback to the old method if we couldn't determine the provider
    for (const [keyId, keyConfig] of this.apiKeys.entries()) {
      if (keyConfig.key === apiKey) {
        return keyId;
      }
    }

    return null;
  }

  /**
   * Log usage statistics periodically
   */
  // @Cron(CronExpression.EVERY_5_MINUTES)
  async logUsageStatistics() {
    try {
      if (!apiKeyManagerConfig.logUsageStatistics) {
        return;
      }

      this.logger.log('API Key Usage Statistics:');

      for (const provider of Object.values(ApiProvider)) {
        try {
          const stats = await this.apiKeyUsageModel.aggregate([
            { $match: { provider } },
            {
              $group: {
                _id: null,
                totalKeys: { $sum: 1 },
                activeKeys: {
                  $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] },
                },
                totalRequests: { $sum: '$totalRequests' },
                successfulRequests: { $sum: '$successfulRequests' },
                failedRequests: { $sum: '$failedRequests' },
                rateLimitErrors: { $sum: '$rateLimitErrors' },
                authErrors: { $sum: '$authErrors' },
                serverErrors: { $sum: '$serverErrors' },
                otherErrors: { $sum: '$otherErrors' },
              },
            },
          ]);

          if (stats.length > 0) {
            const providerStats = stats[0];
            this.logger.log(`Provider ${provider}:`);
            this.logger.log(
              `  Keys: ${providerStats.activeKeys}/${providerStats.totalKeys} active`,
            );
            this.logger.log(
              `  Requests: ${providerStats.totalRequests} total, ${providerStats.successfulRequests} successful, ${providerStats.failedRequests} failed`,
            );
            this.logger.log(
              `  Errors: ${providerStats.rateLimitErrors} rate limit, ${providerStats.authErrors} auth, ${providerStats.serverErrors} server, ${providerStats.otherErrors} other`,
            );
          } else {
            this.logger.log(`Provider ${provider}: No data`);
          }
        } catch (providerError) {
          this.logger.error(`Error getting statistics for provider ${provider}: ${providerError.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error in API key usage statistics cron job: ${error.message}`, error.stack);
    }
  }
}
