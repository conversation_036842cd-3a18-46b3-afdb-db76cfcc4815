/* eslint-disable */

import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  createMockConfigService,
  createMockMongooseModel,
} from '../../test/test-utils';
import { ApiProvider } from '../config/api-keys';
import { ConfigService } from '../config/config.service';
import { ApiKeyManagerService } from './api-key-manager.service';
import { ApiKeyUsage } from './schemas/api-key-usage.schema';

describe('ApiKeyManagerService', () => {
  let service: ApiKeyManagerService;
  let apiKeyUsageModel: any;
  let configService: jest.Mocked<ConfigService>;

  const mockApiKeyUsage = {
    _id: 'mock-usage-id',
    keyId: 'openai-key-1',
    provider: ApiProvider.OPENAI,
    requestCount: 10,
    tokenCount: 1000,
    errorCount: 0,
    lastUsed: new Date(),
    createdAt: new Date(),
  };

  beforeEach(async () => {
    const mockApiKeyUsageModel = createMockMongooseModel(mockApiKeyUsage);
    const mockConfig = createMockConfigService({
      'ai.openaiApiKey': 'test-openai-key',
      'ai.groqApiKey': 'test-groq-key',
      'ai.deepseekApiKey': 'test-deepseek-key',
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApiKeyManagerService,
        {
          provide: getModelToken(ApiKeyUsage.name),
          useValue: mockApiKeyUsageModel,
        },
        {
          provide: ConfigService,
          useValue: mockConfig,
        },
      ],
    }).compile();

    service = module.get<ApiKeyManagerService>(ApiKeyManagerService);
    apiKeyUsageModel = module.get(getModelToken(ApiKeyUsage.name));
    configService = module.get(ConfigService);

    // Initialize the service
    await service.onModuleInit();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should initialize API keys on module init', async () => {
      // Service should be initialized with API keys from config
      expect(configService.get).toHaveBeenCalledWith('ai.openaiApiKey', '');
      expect(configService.get).toHaveBeenCalledWith('ai.groqApiKey', '');
      expect(configService.get).toHaveBeenCalledWith('ai.deepseekApiKey', '');
    });
  });

  describe('getApiKey', () => {
    it('should return an API key for valid provider', async () => {
      const provider = ApiProvider.OPENAI;

      const result = await service.getApiKey(provider);

      expect(result).toBe('test-openai-key');
    });

    it('should throw error for provider with no keys', async () => {
      // Mock a provider with no configured keys
      const invalidProvider = 'INVALID_PROVIDER' as ApiProvider;

      await expect(service.getApiKey(invalidProvider)).rejects.toThrow(
        `No API keys available for provider ${invalidProvider}`,
      );
    });
  });

  describe('recordSuccess', () => {
    it('should record successful API call', async () => {
      const provider = ApiProvider.OPENAI;
      const apiKey = 'test-openai-key';
      const tokens = 100;

      apiKeyUsageModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockApiKeyUsage),
      });

      apiKeyUsageModel.updateOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
      });

      await service.recordSuccess(provider, apiKey, tokens);

      expect(apiKeyUsageModel.findOne).toHaveBeenCalledWith({
        keyId: expect.any(String),
      });
    });

    it('should handle success recording errors gracefully', async () => {
      const provider = ApiProvider.OPENAI;
      const apiKey = 'test-openai-key';
      const tokens = 100;

      apiKeyUsageModel.findOne.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      // Should not throw error, just log it
      await expect(
        service.recordSuccess(provider, apiKey, tokens),
      ).resolves.not.toThrow();
    });
  });

  describe('recordError', () => {
    it('should record API key error', async () => {
      const provider = ApiProvider.OPENAI;
      const apiKey = 'test-openai-key';
      const errorType = 'server';

      apiKeyUsageModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockApiKeyUsage),
      });

      apiKeyUsageModel.updateOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
      });

      await service.recordError(provider, apiKey, errorType);

      expect(apiKeyUsageModel.findOne).toHaveBeenCalledWith({
        keyId: expect.any(String),
      });
    });

    it('should handle error recording failures gracefully', async () => {
      const provider = ApiProvider.OPENAI;
      const apiKey = 'test-openai-key';
      const errorType = 'server';

      apiKeyUsageModel.findOne.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      // Should not throw error, just log it
      await expect(
        service.recordError(provider, apiKey, errorType),
      ).resolves.not.toThrow();
    });
  });

  describe('logUsageStatistics', () => {
    it('should log usage statistics', async () => {
      const mockStats = [
        {
          _id: null,
          totalKeys: 2,
          activeKeys: 2,
          totalRequests: 100,
          successfulRequests: 90,
          failedRequests: 10,
          rateLimitErrors: 5,
          authErrors: 2,
          serverErrors: 2,
          otherErrors: 1,
        },
      ];

      apiKeyUsageModel.aggregate.mockResolvedValue(mockStats);

      await service.logUsageStatistics();

      expect(apiKeyUsageModel.aggregate).toHaveBeenCalled();
    });
  });
});
