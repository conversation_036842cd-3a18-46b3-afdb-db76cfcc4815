import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ApiKeyManagerService } from './api-key-manager.service';
import { ApiKeyUsage, ApiKeyUsageSchema } from './schemas/api-key-usage.schema';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '../config/config.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ApiKeyUsage.name, schema: ApiKeyUsageSchema },
    ]),
    ScheduleModule.forRoot(),
    ConfigModule,
  ],
  providers: [ApiKeyManagerService],
  exports: [ApiKeyManagerService],
})
export class ApiKeyManagerModule {}
