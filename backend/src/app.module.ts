import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from './auth/auth.module';
import { WebSocketModule } from './gateways/websocket.module';
import { HealthController } from './health/health.controller';
import { HealthModule } from './health/health.module';
import { LocationsModule } from './locations/locations.module';
import { TripsModule } from './trips/trips.module';
import { UsersModule } from './users/users.module';
import { AuthMiddleware } from './middleware/auth.middleware';
import { JwtModule } from '@nestjs/jwt';
import { DaysBalanceModule } from './days-balance/days-balance.module';
import { PaymentsModule } from './payments/payments.module';
import { StreamingModule } from './streaming/streaming.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';

@Module({
  imports: [
    ConfigModule.forRoot(),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getThrottlerConfig(),
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        uri: configService.getMongoDbUri(),
      }),
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
    AuthModule,
    WebSocketModule,
    TripsModule,
    LocationsModule,
    TerminusModule.forRoot(),
    HealthModule,
    UsersModule,
    DaysBalanceModule,
    PaymentsModule,
    StreamingModule,
    ServeStaticModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => [
        configService.getStaticFilesConfig(),
      ],
    }),
  ],
  controllers: [HealthController],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .exclude('health', 'auth/*path', 'locations/*path') // Exclude health checks and auth routes
      .forRoutes('*');
  }
}
