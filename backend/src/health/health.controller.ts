import { Controller, Dependencies, Get } from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  MongooseHealthIndicator,
} from '@nestjs/terminus';
import { Public } from 'src/auth/guards/jwt.guard';
import { ConfigService } from '../config/config.service';
@Controller('health')
@Dependencies(
  HealthCheckService,
  HttpHealthIndicator,
  DiskHealthIndicator,
  MongooseHealthIndicator,
  ConfigService,
)
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: MongooseHealthIndicator,
    private readonly configService: ConfigService,
  ) {}

  @Public()
  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([() => this.db.pingCheck('mongodb')]);
  }
}
