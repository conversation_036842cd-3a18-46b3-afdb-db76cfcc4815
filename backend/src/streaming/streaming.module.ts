import { Module } from '@nestjs/common';
import { StreamingService } from '../trips/services/streaming.service';
import { ConfigModule } from '@nestjs/config';
import { WebSocketModule } from '../gateways/websocket.module';
import { UsersModule } from '../users/users.module';
import { ConfigService } from 'src/config/config.service';

@Module({
  imports: [ConfigModule, WebSocketModule, UsersModule],
  providers: [StreamingService, ConfigService],
  exports: [StreamingService],
})
export class StreamingModule {}
