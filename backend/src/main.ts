/* eslint-disable */

import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from './config/config.service';
import { AllExceptionsFilter } from './middleware/error.middleware';

/**
 * Environment variable management
 * Uses environment variables for all environments
 */
async function loadEnvironmentConfiguration(): Promise<void> {
  const nodeEnv = process.env.NODE_ENV || 'development';

  console.log(`🔧 Environment: ${nodeEnv}`);

  // Development mode - uses .env file (loaded by NestJS ConfigModule)
  if (nodeEnv === 'development') {
    console.log('📁 Using .env file for configuration (development mode)');
  }
  // Staging/production - uses environment variables
  else if (['staging', 'production'].includes(nodeEnv)) {
    console.log(`🌍 Using environment variables for ${nodeEnv} environment`);
  }
  // Other environments (test, etc.)
  else {
    console.log(`🔧 Using environment variables for ${nodeEnv} environment`);
  }

  // Validate critical parameters are available
  await validateCriticalParameters();
}

/**
 * Validate that critical parameters are available
 */
async function validateCriticalParameters(): Promise<void> {
  const criticalParams = ['MONGODB_URI', 'JWT_SECRET'];

  const missingParams = criticalParams.filter((param) => !process.env[param]);

  if (missingParams.length > 0) {
    console.error(
      `❌ Missing critical parameters: ${missingParams.join(', ')}`,
    );
    throw new Error(
      `Critical configuration parameters missing: ${missingParams.join(', ')}`,
    );
  }

  console.log('✅ All critical parameters validated');
}

async function bootstrap() {
  // Load environment configuration before creating the app
  await loadEnvironmentConfiguration();

  const app = await NestFactory.create(AppModule);

  // Global exception filter
  app.useGlobalFilters(new AllExceptionsFilter());

  // Security middleware
  app.use(helmet());

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('iTrip API')
    .setDescription('The iTrip API description')
    .setVersion('1.0')
    .addTag('trips')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, documentFactory);

  // Get configuration service
  const configService = app.get(ConfigService);

  // Add CORS configuration
  app.enableCors(configService.getCorsConfig());

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  await app.listen(configService.get<number>('app.port'));
}
bootstrap();
