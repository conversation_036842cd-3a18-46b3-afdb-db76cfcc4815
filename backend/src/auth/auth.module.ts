import { Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { UsersModule } from '../users/users.module';
import { DaysBalanceModule } from '../days-balance/days-balance.module';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { AuthController } from './auth.controller';
import { GoogleOAuth20Strategy } from './strategies/google-oauth20.strategy';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
    forwardRef(() => DaysBalanceModule),
  ],
  providers: [AuthService, JwtStrategy, GoogleOAuth20Strategy],
  exports: [AuthService],
  controllers: [AuthController],
})
export class AuthModule { }
