/* eslint-disable */

import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Profile } from 'passport';
import { Strategy } from 'passport-google-oauth20';
import { ConfigService } from 'src/config/config.service';

@Injectable()
export class GoogleOAuth20Strategy extends PassportStrategy(
  Strategy,
  'google',
) {
  constructor(private configService: ConfigService) {
    super(configService.getGoogleOAuthConfig());
  }

  async validate(accessToken: string, refreshToken: string, profile: Profile) {
    return { accessToken, refreshToken, profile };
  }
}
