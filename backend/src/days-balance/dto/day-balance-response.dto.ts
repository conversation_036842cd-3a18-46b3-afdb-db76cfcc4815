import { SubscriptionPlan, SubscriptionStatus } from '../../users/schemas/token-balance.schema';

export class DayBalanceResponseDto {
  subscriptionDays: number;
  packDays: number;
  totalDays: number; // subscriptionDays + packDays
  totalDaysUsed: number;
  totalDaysAdded: number;
  currentPlan: SubscriptionPlan;
  subscriptionStatus: SubscriptionStatus;
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  nextBillingDate?: Date;
  lastResetDate?: Date;
  pendingPlanChange?: {
    newPlan: SubscriptionPlan;
    oldPlan: SubscriptionPlan;
    changeDate: Date;
    effectiveDate: Date;
  };
}

export class DayTransactionResponseDto {
  id: string;
  userId: string;
  type: 'debit' | 'credit';
  amount: number;
  balanceAfter: number;
  source: string;
  tripId?: string;
  description?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export class PaginatedDayTransactionResponseDto {
  data: DayTransactionResponseDto[];
  pagination: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
