import { IsEnum, IsOptional, IsNumber, IsString } from 'class-validator';
import { SubscriptionPlan } from '../../users/schemas/token-balance.schema';

export class SubscriptionPlanDto {
  @IsEnum(SubscriptionPlan)
  plan: SubscriptionPlan;

  @IsOptional()
  @IsString()
  description?: string;
}

export class DayPackPurchaseDto {
  @IsNumber()
  quantity: number; // Number of 10-day packs to purchase

  @IsOptional()
  @IsString()
  paymentMethodId?: string;
}

export class SubscriptionPlanInfo {
  plan: SubscriptionPlan;
  daysPerMonth: number;
  displayName: string;
  description: string;
  isRecommended?: boolean;
}

export const SUBSCRIPTION_PLANS: Record<SubscriptionPlan, SubscriptionPlanInfo> = {
  [SubscriptionPlan.BASIC]: {
    plan: SubscriptionPlan.BASIC,
    daysPerMonth: 5,
    displayName: 'Free',
    description: '5 days per month',
  },
  [SubscriptionPlan.RECOMMENDED]: {
    plan: SubscriptionPlan.RECOMMENDED,
    daysPerMonth: 50,
    displayName: 'Pro',
    description: '50 days per month',
    isRecommended: true,
  },
  [SubscriptionPlan.PREMIUM]: {
    plan: SubscriptionPlan.PREMIUM,
    daysPerMonth: 100,
    displayName: 'Premium',
    description: '100 days per month',
  },
};

export const DAY_PACK_PRICE = 1.99; // USD for 10 days
export const DAY_PACK_SIZE = 10; // Days per pack
