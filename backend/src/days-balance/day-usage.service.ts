import { Injectable, Logger } from '@nestjs/common';
import { DaysBalanceService } from './days-balance.service';
import { TransactionSource } from '../users/schemas/token-transaction.schema';

export class InsufficientDaysException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InsufficientDaysException';
  }
}

@Injectable()
export class DayUsageService {
  private readonly logger = new Logger(DayUsageService.name);

  constructor(private readonly daysBalanceService: DaysBalanceService) {}

  /**
   * Track day usage for trip generation and deduct from user's balance
   */
  async trackTripGenerationDay(
    userId: string,
    tripId: string,
    description: string = 'Day used for trip generation',
    metadata?: Record<string, any>,
  ): Promise<{
    dayUsage: { daysUsed: number };
    updatedBalance: { totalDays: number };
  }> {
    try {
      this.logger.log(`Deducting 1 day from user ${userId} for trip ${tripId}`);

      const updatedDayBalance = await this.daysBalanceService.deductDays(
        userId,
        1,
        TransactionSource.TRIP_GENERATION,
        description,
        tripId,
        metadata,
      );

      const totalDays = updatedDayBalance.subscriptionDays + updatedDayBalance.packDays;

      this.logger.log(
        `Successfully deducted 1 day. Remaining balance: ${totalDays}`,
      );

      return {
        dayUsage: { daysUsed: 1 },
        updatedBalance: { totalDays },
      };
    } catch (error) {
      this.logger.error(
        `Error tracking day usage for trip ${tripId}: ${error.message}`,
      );
      this.logger.error(`Error stack: ${error.stack}`);

      // If deduction fails, we should still try to get the current balance
      try {
        const currentBalance = await this.daysBalanceService.getTotalAvailableDays(userId);

        this.logger.error(
          `CRITICAL: Failed to deduct day after trip generation for trip ${tripId}. This may indicate a billing issue.`,
        );

        return {
          dayUsage: { daysUsed: 0 },
          updatedBalance: { totalDays: currentBalance },
        };
      } catch (balanceError) {
        this.logger.error(
          `Failed to get balance after day tracking error: ${balanceError.message}`,
        );
        throw error; // Re-throw the original error
      }
    }
  }

  /**
   * Check if a user has enough days for trip generation
   */
  async hasSufficientDaysForTrip(userId: string): Promise<boolean> {
    return this.daysBalanceService.hasSufficientDays(userId, 1);
  }

  /**
   * Check if user has sufficient days and throw exception if not
   */
  async checkSufficientDays(
    userId: string,
    tripId: string,
    daysRequired: number = 1,
  ): Promise<void> {
    const hasSufficientDays = await this.daysBalanceService.hasSufficientDays(
      userId,
      daysRequired,
    );

    if (!hasSufficientDays) {
      const currentDays = await this.daysBalanceService.getTotalAvailableDays(userId);
      
      this.logger.warn(
        `User ${userId} doesn't have enough days for trip ${tripId}. Required: ${daysRequired}, Available: ${currentDays}`,
      );

      throw new InsufficientDaysException(
        `Insufficient days. Required: ${daysRequired}, Available: ${currentDays}`,
      );
    }
  }

  /**
   * Validate that user has sufficient days after generation
   * This is a safety check to ensure consistency
   */
  async validateSufficientDaysAfterGeneration(
    userId: string,
    tripId: string,
    daysUsed: number = 1,
  ): Promise<void> {
    const currentDays = await this.daysBalanceService.getTotalAvailableDays(userId);

    // This is just a validation - we don't expect this to fail since we already deducted
    this.logger.log(
      `Post-generation validation: User ${userId} has ${currentDays} days remaining after using ${daysUsed} day(s) for trip ${tripId}`,
    );
  }

  /**
   * Get current day balance for a user
   */
  async getCurrentDayBalance(userId: string): Promise<number> {
    return this.daysBalanceService.getTotalAvailableDays(userId);
  }

  /**
   * Track day usage for a single day generation
   */
  async trackSingleDayGeneration(
    userId: string,
    tripId: string,
    day: number,
    metadata?: Record<string, any>,
  ): Promise<{
    dayUsage: { daysUsed: number };
    updatedBalance: { totalDays: number };
  }> {
    return this.trackTripGenerationDay(
      userId,
      tripId,
      `Day used for generating day ${day} of trip`,
      { ...metadata, dayNumber: day },
    );
  }

  /**
   * Check if user has sufficient days and send notification if not
   * Note: This method signature is kept for compatibility but notifications
   * should be handled at the controller/gateway level in the new system
   */
  async checkDaysAndNotify(
    userId: string,
    tripId: string,
    requiredDays: number = 1,
  ): Promise<boolean> {
    const hasSufficientDays = await this.daysBalanceService.hasSufficientDays(
      userId,
      requiredDays,
    );

    if (!hasSufficientDays) {
      this.logger.warn(
        `User ${userId} doesn't have enough days for trip ${tripId}`,
      );
      
      const currentDays = await this.daysBalanceService.getTotalAvailableDays(userId);
      
      // In the new system, insufficient days notifications should be handled
      // by the calling service or controller, not here
      this.logger.warn(
        `Insufficient days notification should be sent: User ${userId}, Trip ${tripId}, Available: ${currentDays}, Required: ${requiredDays}`,
      );
    }

    return hasSufficientDays;
  }
}
