import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '../config/config.module';
import { DayBalance, DayBalanceSchema } from '../users/schemas/token-balance.schema';
import { DayTransaction, DayTransactionSchema } from '../users/schemas/token-transaction.schema';
import { User, UserSchema } from '../users/schemas/user.schema';
import { DaysBalanceService } from './days-balance.service';
import { DaysBalanceController } from './days-balance.controller';
import { DayUsageService } from './day-usage.service';
import { SubscriptionResetService } from './subscription-reset.service';
import { ProductConfigService } from '../payments/services/product-config.service';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: DayBalance.name, schema: DayBalanceSchema },
      { name: DayTransaction.name, schema: DayTransactionSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  providers: [DaysBalanceService, DayUsageService, SubscriptionResetService, ProductConfigService],
  controllers: [DaysBalanceController],
  exports: [DaysBalanceService, DayUsageService, SubscriptionResetService],
})
export class DaysBalanceModule { }
