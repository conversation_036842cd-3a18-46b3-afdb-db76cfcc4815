import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { ConfigService } from '../../config/config.service';

@Injectable()
export class WebhookVerificationService {
  private readonly logger = new Logger(WebhookVerificationService.name);
  private applePublicKeys: Map<string, string> = new Map();
  private lastAppleKeysFetch: Date | null = null;
  private readonly APPLE_KEYS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  constructor(private readonly configService: ConfigService) {}

  /**
   * Verify Apple App Store Server Notification JWT
   */
  async verifyAppleWebhook(signedPayload: string): Promise<{ isValid: boolean; payload?: any; error?: string }> {
    try {
      // Decode JWT header to get key ID
      const decodedHeader = jwt.decode(signedPayload, { complete: true });
      if (!decodedHeader || !decodedHeader.header.kid) {
        return { isValid: false, error: 'Invalid JWT header or missing key ID' };
      }

      const keyId = decodedHeader.header.kid;
      
      // Get Apple's public key for verification
      const publicKey = await this.getApplePublicKey(keyId);
      if (!publicKey) {
        return { isValid: false, error: 'Unable to fetch Apple public key' };
      }

      // Verify JWT signature
      const payload = jwt.verify(signedPayload, publicKey, {
        algorithms: ['ES256'],
        issuer: 'https://appleid.apple.com',
      });

      return { isValid: true, payload };
    } catch (error) {
      this.logger.error(`Apple webhook verification failed: ${error.message}`);
      return { isValid: false, error: error.message };
    }
  }

  /**
   * Verify Google Play Pub/Sub message signature
   */
  verifyGoogleWebhook(message: any, signature?: string): { isValid: boolean; error?: string } {
    try {
      // For Google Play, the message comes through Pub/Sub which handles authentication
      // The message itself is base64 encoded and doesn't require additional signature verification
      // However, you should verify that the request comes from Google's Pub/Sub service
      
      // Basic validation - ensure message has required fields
      if (!message.data || !message.messageId || !message.publishTime) {
        return { isValid: false, error: 'Invalid Pub/Sub message format' };
      }

      // In production, you should also verify:
      // 1. The request comes from Google's IP ranges
      // 2. The Pub/Sub subscription is configured correctly
      // 3. The message is not too old (replay attack prevention)
      
      const publishTime = new Date(message.publishTime);
      const now = new Date();
      const messageAge = now.getTime() - publishTime.getTime();
      const MAX_MESSAGE_AGE = 10 * 60 * 1000; // 10 minutes

      if (messageAge > MAX_MESSAGE_AGE) {
        return { isValid: false, error: 'Message too old, possible replay attack' };
      }

      return { isValid: true };
    } catch (error) {
      this.logger.error(`Google webhook verification failed: ${error.message}`);
      return { isValid: false, error: error.message };
    }
  }

  /**
   * Get Apple's public key for JWT verification
   */
  private async getApplePublicKey(keyId: string): Promise<string | null> {
    try {
      // Check if we have cached keys and they're still valid
      const now = new Date();
      if (
        this.lastAppleKeysFetch &&
        now.getTime() - this.lastAppleKeysFetch.getTime() < this.APPLE_KEYS_CACHE_DURATION &&
        this.applePublicKeys.has(keyId)
      ) {
        return this.applePublicKeys.get(keyId) || null;
      }

      // Fetch Apple's public keys
      await this.fetchApplePublicKeys();
      return this.applePublicKeys.get(keyId) || null;
    } catch (error) {
      this.logger.error(`Error fetching Apple public key: ${error.message}`);
      return null;
    }
  }

  /**
   * Fetch Apple's public keys from their endpoint
   */
  private async fetchApplePublicKeys(): Promise<void> {
    try {
      const response = await fetch('https://appleid.apple.com/auth/keys');
      if (!response.ok) {
        throw new Error(`Failed to fetch Apple keys: ${response.status}`);
      }

      const data = await response.json();
      
      // Clear existing keys
      this.applePublicKeys.clear();

      // Process each key
      for (const key of data.keys) {
        if (key.kid && key.x5c && key.x5c.length > 0) {
          // Convert X.509 certificate to PEM format
          const cert = `-----BEGIN CERTIFICATE-----\n${key.x5c[0]}\n-----END CERTIFICATE-----`;
          this.applePublicKeys.set(key.kid, cert);
        }
      }

      this.lastAppleKeysFetch = new Date();
      this.logger.log(`Fetched ${this.applePublicKeys.size} Apple public keys`);
    } catch (error) {
      this.logger.error(`Error fetching Apple public keys: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify request comes from allowed IP ranges (for additional security)
   */
  verifyRequestOrigin(clientIp: string, platform: 'apple' | 'google'): boolean {
    try {
      // Apple's IP ranges (you should get the current ranges from Apple)
      const appleIpRanges = [
        '********/8',
        // Add more Apple IP ranges as needed
      ];

      // Google's IP ranges (you should get the current ranges from Google)
      const googleIpRanges = [
        '**********/16',
        '**********/16',
        // Add more Google IP ranges as needed
      ];

      const ipRanges = platform === 'apple' ? appleIpRanges : googleIpRanges;
      
      // Simple IP range check (you might want to use a more robust library)
      for (const range of ipRanges) {
        if (this.isIpInRange(clientIp, range)) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error(`Error verifying request origin: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if IP is in CIDR range (basic implementation)
   */
  private isIpInRange(ip: string, cidr: string): boolean {
    try {
      const [range, bits] = cidr.split('/');
      const mask = ~(2 ** (32 - parseInt(bits)) - 1);
      
      const ipNum = this.ipToNumber(ip);
      const rangeNum = this.ipToNumber(range);
      
      return (ipNum & mask) === (rangeNum & mask);
    } catch (error) {
      return false;
    }
  }

  /**
   * Convert IP address to number
   */
  private ipToNumber(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
  }

  /**
   * Generate HMAC signature for testing
   */
  generateTestSignature(payload: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
  }

  /**
   * Verify HMAC signature
   */
  verifyHmacSignature(payload: string, signature: string, secret: string): boolean {
    try {
      const expectedSignature = this.generateTestSignature(payload, secret);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      this.logger.error(`HMAC verification failed: ${error.message}`);
      return false;
    }
  }
}
