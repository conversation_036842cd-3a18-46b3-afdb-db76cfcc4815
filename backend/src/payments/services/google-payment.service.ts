/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '../../config/config.service';
import {
  IPaymentService,
  IReceiptValidationResult,
} from '../interfaces/payment-service.interface';
import { PaymentReceipt } from '../schemas/payment-receipt.schema';

@Injectable()
export class GooglePaymentService implements IPaymentService {
  private readonly logger = new Logger(GooglePaymentService.name);
  private readonly serviceAccount: any;
  private accessToken: string | null = null;
  private accessTokenExpiry: Date | null = null;

  constructor(
    private readonly configService: ConfigService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
    private readonly jwtService: JwtService,
  ) {
    const serviceAccountJson =
      this.configService.getPaymentConfig().googlePlayServiceAccount;
    if (serviceAccountJson) {
      try {
        this.serviceAccount = JSON.parse(serviceAccountJson);
      } catch (error) {
        this.logger.error(
          'Failed to parse Google Play service account JSON',
          error.stack,
        );
      }
    }
  }

  async verifyReceiptLocally(receipt: string, productId: string, userId: string, metadata?: Record<string, any>): Promise<any> {
    return Promise.resolve({
      isValid: true,
      transactionId: 'mock_transaction_id',
      productId,
      purchaseDate: new Date(),
      validationResponse: {},
    });
  }

  /**
   * Validate a Google Play receipt
   * @param receipt Receipt data from Google Play (purchase token)
   * @param productId Product ID from Google Play
   * @param userId User ID
   * @param metadata Additional metadata
   * @returns Validation result
   */
  async validateReceipt(
    receipt: string,
    productId: string,
    userId: string,
    metadata?: Record<string, any>,
  ): Promise<IReceiptValidationResult> {
    try {
      this.logger.log(
        `Validating Google Play receipt for product ${productId} and user ${userId}`,
      );

      // If we don't have a service account, we can't validate
      if (!this.serviceAccount) {
        return {
          isValid: false,
          transactionId: '',
          productId,
          error: 'Google Play service account not configured',
        };
      }

      // Get an access token if we don't have one or it's expired
      if (
        !this.accessToken ||
        !this.accessTokenExpiry ||
        new Date() > this.accessTokenExpiry
      ) {
        await this.getAccessToken();
      }

      // Extract package name and product ID
      // Format: com.example.app:product_id
      const [packageName, productSku] = productId.split(':');

      if (!packageName || !productSku) {
        return {
          isValid: false,
          transactionId: '',
          productId,
          error:
            'Invalid product ID format. Expected format: packageName:productSku',
        };
      }

      // Validate the purchase with Google Play
      const validationResponse = await this.verifyWithGooglePlay(
        packageName,
        productSku,
        receipt,
      );

      // Extract transaction details
      const transactionId = validationResponse.orderId;
      const purchaseState = validationResponse.purchaseState;
      const purchaseTime = new Date(validationResponse.purchaseTimeMillis);

      // Check if the purchase is valid (purchaseState: 0 = purchased)
      const isValid = purchaseState === 0;

      return {
        isValid,
        transactionId,
        productId,
        purchaseDate: purchaseTime,
        validationResponse,
        error: !isValid
          ? `Purchase state is not valid: ${purchaseState}`
          : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Error validating Google Play receipt: ${error.message}`,
        error.stack,
      );
      return {
        isValid: false,
        transactionId: '',
        productId,
        error: `Error validating receipt: ${error.message}`,
      };
    }
  }

  /**
   * Check if a transaction has already been processed
   * @param transactionId Transaction ID to check
   * @returns True if the transaction has been processed
   */
  async isTransactionProcessed(transactionId: string): Promise<boolean> {
    const existingReceipt = await this.paymentReceiptModel.findOne({
      transactionId,
    });
    return !!existingReceipt;
  }

  /**
   * Get an access token for the Google Play API
   */
  private async getAccessToken(): Promise<void> {
    try {
      const jwtToken = await this.createJWT();

      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          assertion: jwtToken,
        }),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to get access token: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();
      this.accessToken = data.access_token;

      // Set expiry to 5 minutes before actual expiry to be safe
      const expiresIn = data.expires_in || 3600;
      this.accessTokenExpiry = new Date(Date.now() + (expiresIn - 300) * 1000);

      this.logger.log('Successfully obtained Google Play API access token');
    } catch (error) {
      this.logger.error(
        'Failed to get Google Play API access token',
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a JWT token for Google API authentication
   * @returns JWT token
   */
  private async createJWT(): Promise<string> {
    // 1. Create a JWT with the required claims
    const payload = {
      iss: this.serviceAccount.client_email,
      scope: 'https://www.googleapis.com/auth/androidpublisher',
      aud: 'https://oauth2.googleapis.com/token',
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour expiration
    };

    // 2. Sign it with the private key from the service account
    const signedJwt = this.jwtService.sign(payload, {
      privateKey: this.serviceAccount.private_key,
      algorithm: 'RS256',
    });

    // 3. Return the signed JWT
    return signedJwt;
  }

  /**
   * Verify a purchase with the Google Play API
   * @param packageName App package name
   * @param productId Product ID
   * @param purchaseToken Purchase token
   * @returns Validation response from Google Play
   */
  private async verifyWithGooglePlay(
    packageName: string,
    productId: string,
    purchaseToken: string,
  ): Promise<any> {
    const url = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${packageName}/purchases/products/${productId}/tokens/${purchaseToken}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Google Play verification failed with status ${response.status}`,
      );
    }

    return await response.json();
  }
}
