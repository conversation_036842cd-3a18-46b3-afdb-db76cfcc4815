import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '../../config/config.service';

export interface ProductConfig {
  id: string;
  name: string;
  description: string;
  type: 'subscription' | 'consumable';
  platform: 'ios' | 'android' | 'both';
  productId: {
    ios?: string;
    android?: string;
  };
  subscriptionGroup?: string; // For iOS subscriptions
  basePlan?: string; // For Android subscriptions
  daysPerMonth?: number; // For subscription plans
  days?: number; // For day refill packages
  price: {
    usd: number;
    currency: string;
  };
  popular?: boolean;
}

const prices = (days: number) => {
  switch (days) {
    case 5: return 2.49;
    case 10: return 4.99;
    case 15: return 7.49;
    case 20: return 9.95;
    case 25: return 12.49;
    case 30: return 14.99;
    default: return 0;
  }
}

@Injectable()
export class ProductConfigService {
  private readonly logger = new Logger(ProductConfigService.name);
  private readonly products: ProductConfig[] = [
    {
      id: 'free',
      name: 'Free Plan',
      description: '5 days per month - Perfect for occasional travelers',
      type: 'subscription',
      platform: 'both',
      productId: {
        ios: 'com.aiplanmytrip.subscription.free_sub',
        android: 'com.aiplanmytrip.subscription.free_sub'
      },
      subscriptionGroup: 'itrip_subscriptions', // iOS subscription group
      basePlan: 'free-monthly', // Android base plan
      daysPerMonth: 5,
      price: {
        usd: 0,
        currency: 'USD'
      }
    },
    {
      id: 'pro',
      name: 'Pro Plan',
      description: '30 days per month - Great for frequent travelers',
      type: 'subscription',
      platform: 'both',
      productId: {
        ios: 'com.aiplanmytrip.subscription.pro_sub',
        android: 'com.aiplanmytrip.subscription.pro_sub'
      },
      subscriptionGroup: 'itrip_subscriptions',
      basePlan: 'pro-monthly',
      daysPerMonth: 30,
      price: {
        usd: 9.99,
        currency: 'USD'
      },
      popular: true
    },
    {
      id: 'premium',
      name: 'Premium Plan',
      description: '90 days per month - Unlimited travel planning',
      type: 'subscription',
      platform: 'both',
      productId: {
        ios: 'com.aiplanmytrip.subscription.premium_sub',
        android: 'com.aiplanmytrip.subscription.premium_sub'
      },
      subscriptionGroup: 'itrip_subscriptions',
      basePlan: 'premium-monthly',
      daysPerMonth: 90,
      price: {
        usd: 24.99,
        currency: 'USD'
      }
    },
    // Day Refill Packages
    ...new Array(6).fill(null).map<ProductConfig>((_, i) => ({
      id: `refill_${(i + 1) * 5}`,
      name: `${(i + 1) * 5} Days Pack`,
      description: `Add ${(i + 1) * 5} extra days to your account`,
      type: 'consumable',
      platform: 'both',
      productId: {
        ios: `com.aiplanmytrip.days.refill_${(i + 1) * 5}_packs`,
        android: `com.aiplanmytrip.days.refill_${(i + 1) * 5}_packs`
      },
      days: (i + 1) * 5,
      price: {
        usd: prices((i + 1) * 5),
        currency: 'USD'
      }
    }))
  ];

  constructor(private readonly configService: ConfigService) { }

  /**
   * Get all products
   */
  getAllProducts(): ProductConfig[] {
    return this.products;
  }

  /**
   * Get products by platform
   */
  getProductsByPlatform(platform: 'ios' | 'android'): ProductConfig[] {
    return this.products.filter(product =>
      product.platform === 'both' || product.platform === platform
    );
  }

  /**
   * Get subscription plans only
   */
  getSubscriptionPlans(): ProductConfig[] {
    return this.products.filter(product => product.type === 'subscription');
  }

  /**
   * Get day refill packages only
   */
  getDayRefillPackages(): ProductConfig[] {
    return this.products.filter(product => product.type === 'consumable');
  }

  /**
   * Get product by ID
   */
  getProductById(id: string): ProductConfig | null {
    return this.products.find(product => product.id === id) || null;
  }

  /**
   * Get product by store product ID
   */
  getProductByStoreId(storeProductId: string, platform: 'ios' | 'android'): ProductConfig | null {
    return this.products.find(product =>
      product.productId[platform] === storeProductId
    ) || null;
  }

  /**
   * Get product IDs for a specific platform
   */
  getProductIds(platform: 'ios' | 'android'): string[] {
    return this.products
      .filter(product => product.platform === 'both' || product.platform === platform)
      .map(product => product.productId[platform])
      .filter(Boolean) as string[];
  }

  /**
   * Get subscription product IDs for a specific platform
   */
  getSubscriptionProductIds(platform: 'ios' | 'android'): string[] {
    return this.products
      .filter(product =>
        product.type === 'subscription' &&
        (product.platform === 'both' || product.platform === platform)
      )
      .map(product => product.productId[platform])
      .filter(Boolean) as string[];
  }

  /**
   * Get consumable product IDs for a specific platform
   */
  getConsumableProductIds(platform: 'ios' | 'android'): string[] {
    return this.products
      .filter(product =>
        product.type === 'consumable' &&
        (product.platform === 'both' || product.platform === platform)
      )
      .map(product => product.productId[platform])
      .filter(Boolean) as string[];
  }

  /**
   * Validate product configuration
   */
  validateProductConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const product of this.products) {
      // Check required fields
      if (!product.id || !product.name || !product.type) {
        errors.push(`Product ${product.id || 'unknown'} missing required fields`);
      }

      // Check product IDs
      if (product.platform === 'both' || product.platform === 'ios') {
        if (!product.productId.ios) {
          errors.push(`Product ${product.id} missing iOS product ID`);
        }
      }

      if (product.platform === 'both' || product.platform === 'android') {
        if (!product.productId.android) {
          errors.push(`Product ${product.id} missing Android product ID`);
        }
      }

      // Check subscription-specific fields
      if (product.type === 'subscription') {
        if (!product.daysPerMonth) {
          errors.push(`Subscription ${product.id} missing daysPerMonth`);
        }
        if (!product.subscriptionGroup) {
          errors.push(`Subscription ${product.id} missing subscriptionGroup`);
        }
      }

      // Check consumable-specific fields
      if (product.type === 'consumable') {
        if (!product.days) {
          errors.push(`Consumable ${product.id} missing days`);
        }
      }

      // Check price
      if (!product.price || product.price.usd < 0) {
        errors.push(`Product ${product.id} has invalid price`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get App Store Connect configuration guide
   */
  getAppStoreConnectGuide(): string {
    return `
App Store Connect Configuration:

1. Create Subscription Group:
   - Name: iTrip Subscriptions
   - Reference Name: itrip_subscriptions

2. Create Subscriptions:
   ${this.getSubscriptionPlans().map(plan => `
   - Product ID: ${plan.productId.ios}
   - Reference Name: ${plan.name}
   - Subscription Group: itrip_subscriptions
   - Price: $${plan.price.usd}
   - Subscription Duration: 1 Month (Auto-Renewable)`).join('')}

3. Create In-App Purchases (Consumable):
   ${this.getDayRefillPackages().map(pack => `
   - Product ID: ${pack.productId.ios}
   - Reference Name: ${pack.name}
   - Type: Consumable
   - Price: $${pack.price.usd}`).join('')}

4. Configure Server-to-Server Notifications:
   - URL: https://your-domain.com/webhooks/payments/apple
   - Version: Version 2
   - Include Subscription Offer Details: Yes
   - Include App Information: Yes
    `;
  }

  /**
   * Get Google Play Console configuration guide
   */
  getGooglePlayConsoleGuide(): string {
    return `
Google Play Console Configuration:

1. Create Subscription Products:
   ${this.getSubscriptionPlans().map(plan => `
   - Product ID: ${plan.productId.android}
   - Name: ${plan.name}
   - Description: ${plan.description}
   - Base Plan: ${plan.basePlan}
   - Billing Period: 1 Month
   - Price: $${plan.price.usd} USD`).join('')}

2. Create In-App Products (Managed):
   ${this.getDayRefillPackages().map(pack => `
   - Product ID: ${pack.productId.android}
   - Name: ${pack.name}
   - Description: ${pack.description}
   - Price: $${pack.price.usd} USD`).join('')}

3. Configure Real-time Developer Notifications:
   - Topic Name: itrip-payments
   - Endpoint URL: https://your-domain.com/webhooks/payments/google
   - Enable for: Subscriptions and One-time products

4. Set up Google Cloud Pub/Sub:
   - Create topic: itrip-payments
   - Create subscription: itrip-payments-sub
   - Configure push endpoint to your webhook URL
    `;
  }
}
