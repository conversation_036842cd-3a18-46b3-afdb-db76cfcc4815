import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DaysBalanceService } from '../../days-balance/days-balance.service';
import { PaymentReceipt, PaymentPlatform } from '../schemas/payment-receipt.schema';
import { SubscriptionPlan, SubscriptionStatus } from '../../users/schemas/token-balance.schema';

export interface SubscriptionChangeRequest {
  userId: string;
  newPlan: SubscriptionPlan;
  effectiveDate?: Date;
  prorationMode?: 'immediate' | 'next_billing_cycle';
}

export interface CancellationRequest {
  userId: string;
  reason?: string;
  effectiveDate?: Date;
  refundRequested?: boolean;
}

export interface RefundRequest {
  userId: string;
  transactionId: string;
  reason: string;
  amount?: number;
}

export interface GracePeriodConfig {
  enabled: boolean;
  durationDays: number;
  maxRetries: number;
}

@Injectable()
export class SubscriptionLifecycleService {
  private readonly logger = new Logger(SubscriptionLifecycleService.name);

  private readonly gracePeriodConfig: GracePeriodConfig = {
    enabled: true,
    durationDays: 3,
    maxRetries: 3,
  };

  constructor(
    private readonly daysBalanceService: DaysBalanceService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
  ) { }

  /**
   * Upgrade or downgrade subscription
   */
  async changeSubscription(request: SubscriptionChangeRequest): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, newPlan, effectiveDate, prorationMode = 'immediate' } = request;

      this.logger.log(`Processing subscription change for user ${userId} to ${newPlan}`);

      // Get current subscription
      const currentBalance = await this.daysBalanceService.getBalance(userId);
      if (!currentBalance) {
        throw new NotFoundException('User balance not found');
      }

      const currentPlan = currentBalance.currentPlan;

      // Validate the change
      if (currentPlan === newPlan) {
        throw new BadRequestException('User is already on the requested plan');
      }

      // Handle immediate vs next billing cycle
      if (prorationMode === 'immediate') {
        await this.processImmediateSubscriptionChange(userId, currentPlan, newPlan);
      } else {
        await this.scheduleSubscriptionChange(userId, newPlan, effectiveDate);
      }

      return {
        success: true,
        message: `Subscription ${prorationMode === 'immediate' ? 'changed' : 'scheduled to change'} to ${newPlan}`,
      };
    } catch (error) {
      this.logger.error(`Error changing subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(request: CancellationRequest): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, reason, effectiveDate, refundRequested = false } = request;

      this.logger.log(`Processing subscription cancellation for user ${userId}`);

      // Get current subscription
      const currentBalance = await this.daysBalanceService.getBalance(userId);
      if (!currentBalance) {
        throw new NotFoundException('User balance not found');
      }

      if (currentBalance.subscriptionStatus === SubscriptionStatus.CANCELLED) {
        throw new BadRequestException('Subscription is already cancelled');
      }

      // Handle immediate vs end of billing period cancellation
      const cancelDate = effectiveDate || new Date();
      const endOfBillingPeriod = currentBalance.subscriptionEndDate || new Date();

      if (cancelDate <= new Date()) {
        // Immediate cancellation
        await this.processImmediateCancellation(userId, reason, refundRequested);
      } else {
        // Schedule cancellation for end of billing period
        await this.scheduleCancellation(userId, endOfBillingPeriod, reason);
      }

      return {
        success: true,
        message: refundRequested
          ? 'Subscription cancelled with refund request submitted'
          : 'Subscription cancelled successfully',
      };
    } catch (error) {
      this.logger.error(`Error cancelling subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process refund request
   */
  async processRefund(request: RefundRequest): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, transactionId, reason, amount } = request;

      this.logger.log(`Processing refund request for user ${userId}, transaction ${transactionId}`);

      // Find the payment receipt
      const receipt = await this.paymentReceiptModel.findOne({
        userId,
        transactionId,
      });

      if (!receipt) {
        throw new NotFoundException('Payment receipt not found');
      }

      // Create refund record (you would integrate with actual payment processors here)
      const refundAmount = amount || receipt.amount;

      // Update receipt to mark as refunded
      receipt.metadata = {
        ...receipt.metadata,
        refunded: true,
        refundAmount,
        refundReason: reason,
        refundDate: new Date(),
      };
      await receipt.save();

      // Adjust user's balance if needed
      if (receipt.packageType === 'subscription') {
        // Downgrade to free plan
        await this.daysBalanceService.updateSubscriptionPlan(userId, SubscriptionPlan.BASIC);
      } else if (receipt.packageType === 'day_refill' && receipt.days) {
        // Deduct the refunded days
        try {
          await this.daysBalanceService.deductDays(userId, receipt.days, 'REFUND' as any, 'Refund processed');
        } catch (error) {
          this.logger.warn(`Could not deduct days for refund: ${error.message}`);
        }
      }

      return {
        success: true,
        message: `Refund of $${refundAmount} processed successfully`,
      };
    } catch (error) {
      this.logger.error(`Error processing refund: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle failed payment and grace period
   */
  async handleFailedPayment(userId: string, transactionId: string): Promise<{ success: boolean; gracePeriod: boolean }> {
    try {
      this.logger.log(`Handling failed payment for user ${userId}`);

      const balance = await this.daysBalanceService.getBalance(userId);
      if (!balance) {
        throw new NotFoundException('User balance not found');
      }

      // Check if grace period is enabled and user is eligible
      if (this.gracePeriodConfig.enabled && this.isEligibleForGracePeriod(balance)) {
        await this.startGracePeriod(userId);
        return { success: true, gracePeriod: true };
      } else {
        // Immediately downgrade to free plan
        await this.daysBalanceService.updateSubscriptionPlan(userId, SubscriptionPlan.BASIC);
        return { success: true, gracePeriod: false };
      }
    } catch (error) {
      this.logger.error(`Error handling failed payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Reactivate subscription after successful payment
   */
  async reactivateSubscription(userId: string, newPlan: SubscriptionPlan): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`Reactivating subscription for user ${userId} to ${newPlan}`);

      await this.daysBalanceService.updateSubscriptionPlan(userId, newPlan);

      return {
        success: true,
        message: 'Subscription reactivated successfully',
      };
    } catch (error) {
      this.logger.error(`Error reactivating subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process immediate subscription change
   */
  private async processImmediateSubscriptionChange(
    userId: string,
    currentPlan: SubscriptionPlan,
    newPlan: SubscriptionPlan
  ): Promise<void> {
    // Calculate proration if upgrading/downgrading mid-cycle
    const prorationCredit = await this.calculateProration(userId, currentPlan, newPlan);

    // Update to new plan
    await this.daysBalanceService.updateSubscriptionPlan(userId, newPlan);

    // Apply proration credit if applicable
    if (prorationCredit > 0) {
      const creditDays = Math.floor(prorationCredit / 10); // Convert credit to days
      if (creditDays > 0) {
        await this.daysBalanceService.purchaseDayPack(userId, creditDays, {
          transactionId: `proration-${Date.now()}`,
          packageId: 'proration_credit',
        });
      }
    }
  }

  /**
   * Schedule subscription change for next billing cycle
   */
  private async scheduleSubscriptionChange(userId: string, newPlan: SubscriptionPlan, effectiveDate?: Date): Promise<void> {
    const balance = await this.daysBalanceService.getBalance(userId);
    if (!balance) {
      throw new NotFoundException('User balance not found');
    }

    // Update pending plan change
    const changeDate = effectiveDate || balance.nextBillingDate || new Date();

    // This would typically be stored in a separate field or table
    // For now, we'll use the metadata field
    balance['pendingPlanChange'] = {
      newPlan,
      oldPlan: balance.currentPlan,
      effectiveDate: changeDate,
      changeDate: new Date(),
    };

    await balance.save();
  }

  /**
   * Process immediate cancellation
   */
  private async processImmediateCancellation(userId: string, reason?: string, refundRequested = false): Promise<void> {
    // Downgrade to free plan immediately
    await this.daysBalanceService.updateSubscriptionPlan(userId, SubscriptionPlan.BASIC);

    // Log cancellation reason
    this.logger.log(`Subscription cancelled for user ${userId}. Reason: ${reason || 'Not provided'}`);

    if (refundRequested) {
      // This would trigger a refund process
      this.logger.log(`Refund requested for user ${userId}`);
    }
  }

  /**
   * Schedule cancellation for end of billing period
   */
  private async scheduleCancellation(userId: string, cancelDate: Date, reason?: string): Promise<void> {
    const balance = await this.daysBalanceService.getBalance(userId);
    if (!balance) {
      throw new NotFoundException('User balance not found');
    }

    // Mark as cancelled but keep active until end of period
    balance['scheduledCancellation'] = {
      cancelDate,
      reason: reason || 'Not provided',
      scheduledAt: new Date(),
    };

    await balance.save();
  }

  /**
   * Start grace period for failed payment
   */
  private async startGracePeriod(userId: string): Promise<void> {
    const balance = await this.daysBalanceService.getBalance(userId);
    if (!balance) {
      throw new NotFoundException('User balance not found');
    }

    const gracePeriodEnd = new Date();
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + this.gracePeriodConfig.durationDays);

    balance['gracePeriod'] = {
      startDate: new Date(),
      endDate: gracePeriodEnd,
      retriesRemaining: this.gracePeriodConfig.maxRetries,
    };

    await balance.save();
    this.logger.log(`Grace period started for user ${userId} until ${gracePeriodEnd}`);
  }

  /**
   * Check if user is eligible for grace period
   */
  private isEligibleForGracePeriod(balance: any): boolean {
    // Check if user is not already in grace period
    const gracePeriod = balance.metadata?.gracePeriod;
    if (gracePeriod && new Date() < new Date(gracePeriod.endDate)) {
      return false;
    }

    // Check if user has retries remaining
    if (gracePeriod && gracePeriod.retriesRemaining <= 0) {
      return false;
    }

    return true;
  }

  /**
   * Calculate proration credit/charge for plan changes
   */
  private async calculateProration(userId: string, currentPlan: SubscriptionPlan, newPlan: SubscriptionPlan): Promise<number> {
    // This is a simplified calculation
    // In practice, you'd need to consider the exact billing cycle, days remaining, etc.

    const planPrices = {
      [SubscriptionPlan.BASIC]: 0,
      [SubscriptionPlan.RECOMMENDED]: 9.99,
      [SubscriptionPlan.PREMIUM]: 19.99,
    };

    const currentPrice = planPrices[currentPlan] || 0;
    const newPrice = planPrices[newPlan] || 0;

    // Simple proration based on price difference
    // In reality, this would be much more complex
    return Math.max(0, currentPrice - newPrice);
  }
}
