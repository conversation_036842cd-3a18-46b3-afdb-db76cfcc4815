import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { PaymentsController } from './payments.controller';
import { WebhooksController } from './webhooks.controller';
import { PaymentsService } from './payments.service';
import { ApplePaymentService } from './services/apple-payment.service';
import { GooglePaymentService } from './services/google-payment.service';
import { AppleWebhookService } from './services/apple-webhook.service';
import { GoogleWebhookService } from './services/google-webhook.service';
import { WebhookVerificationService } from './services/webhook-verification.service';
import { ProductConfigService } from './services/product-config.service';
import { SubscriptionLifecycleService } from './services/subscription-lifecycle.service';
import {
  PaymentReceipt,
  PaymentReceiptSchema,
} from './schemas/payment-receipt.schema';
import { DaysBalanceModule } from '../days-balance/days-balance.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PaymentReceipt.name, schema: PaymentReceiptSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
    ConfigModule,
    DaysBalanceModule,
  ],
  controllers: [PaymentsController, WebhooksController],
  providers: [
    PaymentsService,
    ApplePaymentService,
    GooglePaymentService,
    AppleWebhookService,
    GoogleWebhookService,
    WebhookVerificationService,
    ProductConfigService,
    SubscriptionLifecycleService
  ],
  exports: [PaymentsService],
})
export class PaymentsModule { }
