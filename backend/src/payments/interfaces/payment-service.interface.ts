/**
 * Interface for payment validation services
 */
export interface IPaymentService {
  /**
   * Validate a purchase receipt
   * @param receipt Receipt data from the store
   * @param productId Product ID from the store
   * @param userId User ID
   * @param metadata Additional metadata
   * @returns Validation result
   */
  validateReceipt(
    receipt: string,
    productId: string,
    userId: string,
    metadata?: Record<string, any>,
  ): Promise<IReceiptValidationResult>;

  /**
   * Verify if a transaction ID has already been processed
   * @param transactionId Transaction ID to check
   * @returns True if the transaction has been processed
   */
  isTransactionProcessed(transactionId: string): Promise<boolean>;

  /**
   * Validate receipt local mock response for storekit/playkit
   * @param receipt Receipt data from the store
   * @param productId Product ID from the store
   * @param userId User ID
   * @param metadata Additional metadata
   * @returns Validation result
   */
  verifyReceiptLocally(
    receipt: string,
    productId: string,
    userId: string,
    metadata?: Record<string, any>,
  ): Promise<IAppleVerificationReceiptResponse>;
}

/**
 * Interface for receipt validation results
 */
export interface IReceiptValidationResult {
  /**
   * Whether the receipt is valid
   */
  isValid: boolean;

  /**
   * Transaction ID from the store
   */
  transactionId: string;

  /**
   * Original transaction ID (for subscriptions or restores)
   */
  originalTransactionId?: string;

  /**
   * Product ID from the store
   */
  productId: string;

  /**
   * Purchase date
   */
  purchaseDate?: Date;

  /**
   * Expiry date (for subscriptions)
   */
  expiryDate?: Date;

  /**
   * Error message if validation failed
   */
  error?: string;

  /**
   * Raw validation response from the store
   */
  validationResponse?: Record<string, any>;
}

/**
 * interface Validation response from Apple
 */
export interface IAppleVerificationReceiptResponse {
  status: number;
  environment: string;
  receipt: IAppleUnifiedReceipt;
  latest_receipt_info: IAppleLatestReceiptInfo[];
}


interface IAppleLatestReceiptInfo {
  transaction_id: string;
  original_transaction_id: string;
  product_id: string;
  purchase_date_ms: string;
  expires_date_ms: string;
}

interface IAppleUnifiedReceipt {
  in_app: IAppleLatestReceiptInfo[];
}
