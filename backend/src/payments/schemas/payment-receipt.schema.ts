import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum PaymentPlatform {
  IOS = 'ios',
  ANDROID = 'android',
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

@Schema({ timestamps: true })
export class PaymentReceipt extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: string;

  @Prop({ required: true })
  productId: string;

  @Prop({ required: true })
  packageId: string;

  @Prop({ required: true })
  transactionId: string;

  @Prop({ required: true })
  receipt: string;

  @Prop({ required: true, enum: PaymentPlatform })
  platform: PaymentPlatform;

  @Prop({ required: true, enum: PaymentStatus, default: PaymentStatus.PENDING })
  status: PaymentStatus;

  @Prop({ required: true })
  amount: number;

  @Prop({ required: false, enum: ['subscription', 'day_refill'] })
  packageType?: string;

  @Prop({ required: false })
  days?: number; // For day refill packages

  @Prop({ required: false })
  daysPerMonth?: number; // For subscription plans

  @Prop({ required: false })
  originalTransactionId?: string;

  @Prop({ required: false })
  purchaseDate?: Date;

  @Prop({ required: false })
  expiryDate?: Date;

  @Prop({ required: false, default: false })
  isRestored: boolean;

  @Prop({ required: false, default: false })
  isMockPurchase: boolean;

  @Prop({ type: Object, required: false })
  metadata?: Record<string, any>;

  @Prop({ type: Object, required: false })
  validationResponse?: Record<string, any>;
}

export const PaymentReceiptSchema =
  SchemaFactory.createForClass(PaymentReceipt);

// Add indexes for common queries
PaymentReceiptSchema.index({ userId: 1 });
PaymentReceiptSchema.index({ transactionId: 1 }, { unique: true });
PaymentReceiptSchema.index({ originalTransactionId: 1 });
PaymentReceiptSchema.index({ productId: 1 });
PaymentReceiptSchema.index({ status: 1 });
