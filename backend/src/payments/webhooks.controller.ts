import {
  Controller,
  Post,
  Body,
  Headers,
  Logger,
  BadRequestException,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiHeader,
} from '@nestjs/swagger';
import { AppleWebhookService } from './services/apple-webhook.service';
import { GoogleWebhookService } from './services/google-webhook.service';
import { AppleWebhookDto } from './dto/apple-webhook.dto';
import { GoogleWebhookDto } from './dto/google-webhook.dto';

@ApiTags('payment-webhooks')
@Controller('webhooks/payments')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(
    private readonly appleWebhookService: AppleWebhookService,
    private readonly googleWebhookService: GoogleWebhookService,
  ) {}

  @Post('apple')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle Apple App Store Server Notifications',
    description: 'Endpoint for receiving Apple App Store server-to-server notifications for subscription events',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid webhook data or signature' })
  @ApiHeader({
    name: 'x-apple-receipt-signature',
    description: 'Apple webhook signature for verification',
    required: false,
  })
  async handleAppleWebhook(
    @Body() webhookDto: AppleWebhookDto,
    @Headers('x-apple-receipt-signature') signature?: string,
  ) {
    try {
      this.logger.log('Received Apple webhook notification');
      
      // Log the signature for debugging (remove in production)
      if (signature) {
        this.logger.debug(`Apple webhook signature: ${signature.substring(0, 20)}...`);
      }

      // Validate webhook data
      if (!webhookDto.signedPayload) {
        throw new BadRequestException('Missing signed payload');
      }

      // Process the webhook
      const result = await this.appleWebhookService.processWebhook(webhookDto);
      
      this.logger.log('Apple webhook processed successfully');
      return result;
    } catch (error) {
      this.logger.error(`Error processing Apple webhook: ${error.message}`, error.stack);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new BadRequestException('Failed to process Apple webhook');
    }
  }

  @Post('google')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle Google Play Real-time Developer Notifications',
    description: 'Endpoint for receiving Google Play Pub/Sub notifications for subscription and purchase events',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid webhook data' })
  async handleGoogleWebhook(
    @Body() webhookDto: GoogleWebhookDto,
  ) {
    try {
      this.logger.log('Received Google Play webhook notification');
      
      // Validate webhook data
      if (!webhookDto.message || !webhookDto.message.data) {
        throw new BadRequestException('Missing message data');
      }

      // Process the webhook
      const result = await this.googleWebhookService.processWebhook(webhookDto);
      
      this.logger.log('Google Play webhook processed successfully');
      return result;
    } catch (error) {
      this.logger.error(`Error processing Google Play webhook: ${error.message}`, error.stack);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new BadRequestException('Failed to process Google Play webhook');
    }
  }

  @Post('apple/test')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Test Apple webhook endpoint',
    description: 'Test endpoint for Apple webhook integration during development',
  })
  async testAppleWebhook(@Body() testData: any) {
    this.logger.log('Received Apple webhook test');
    return {
      success: true,
      message: 'Apple webhook test endpoint working',
      receivedData: testData,
    };
  }

  @Post('google/test')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Test Google Play webhook endpoint',
    description: 'Test endpoint for Google Play webhook integration during development',
  })
  async testGoogleWebhook(@Body() testData: any) {
    this.logger.log('Received Google Play webhook test');
    return {
      success: true,
      message: 'Google Play webhook test endpoint working',
      receivedData: testData,
    };
  }
}
