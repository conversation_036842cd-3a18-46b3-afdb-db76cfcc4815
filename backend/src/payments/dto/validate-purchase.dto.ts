import {
  <PERSON>String,
  IsNotEmpty,
  IsEnum,
  IsNumber,
  IsOptional,
  IsBoolean,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentPlatform } from '../schemas/payment-receipt.schema';

export class ValidatePurchaseDto {
  @ApiProperty({ description: 'Receipt data from the store' })
  @IsString()
  @IsNotEmpty()
  receipt: string;

  @ApiProperty({
    description: 'Product ID from the store',
    example: 'com.itrip.subscription.basic',
  })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Package ID in our system', example: 'basic' })
  @IsString()
  @IsNotEmpty()
  packageId: string;

  @ApiProperty({
    description: 'Platform (ios or android)',
    enum: PaymentPlatform,
  })
  @IsEnum(PaymentPlatform)
  platform: PaymentPlatform;

  @ApiProperty({
    description: 'Package type (subscription or day_refill)',
    enum: ['subscription', 'day_refill']
  })
  @IsEnum(['subscription', 'day_refill'])
  packageType: 'subscription' | 'day_refill';

  @ApiProperty({
    description: 'Original transaction ID (for subscriptions or restores)'
  })
  @IsOptional()
  @IsString()
  originalTransactionId?: string;


  @ApiProperty({
    description: 'Transaction ID from the store'
  })
  @IsString()
  transactionId: string;

  @ApiProperty({
    description: 'Transaction date from the store'
  })
  @IsNumber()
  transactionDate: number;

  @ApiPropertyOptional({
    description: 'Number of days for day refill packages'
  })
  @IsOptional()
  @IsNumber()
  days?: number;

  @ApiPropertyOptional({
    description: 'Days per month for subscription plans'
  })
  @IsOptional()
  @IsNumber()
  daysPerMonth?: number;
}
