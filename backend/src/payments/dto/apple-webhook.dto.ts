import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class AppleNotificationPayloadDto {
  @IsString()
  notificationType: string;

  @IsString()
  subtype?: string;

  @IsString()
  notificationUUID: string;

  @IsString()
  version: string;

  @IsObject()
  data: any;

  @IsOptional()
  @IsObject()
  summary?: any;
}

export class AppleWebhookDto {
  @IsString()
  signedPayload: string;
}

export class AppleTransactionInfo {
  @IsString()
  originalTransactionId: string;

  @IsString()
  transactionId: string;

  @IsString()
  webOrderLineItemId: string;

  @IsString()
  bundleId: string;

  @IsString()
  productId: string;

  @IsString()
  subscriptionGroupIdentifier: string;

  @IsNumber()
  purchaseDate: number;

  @IsNumber()
  originalPurchaseDate: number;

  @IsOptional()
  @IsNumber()
  expiresDate?: number;

  @IsOptional()
  @IsNumber()
  revocationDate?: number;

  @IsOptional()
  @IsString()
  revocationReason?: string;

  @IsString()
  type: string;

  @IsString()
  environment: string;

  @IsString()
  storefront: string;

  @IsString()
  storefrontId: string;

  @IsString()
  transactionReason: string;
}

export class AppleRenewalInfo {
  @IsString()
  originalTransactionId: string;

  @IsString()
  autoRenewProductId: string;

  @IsString()
  productId: string;

  @IsString()
  autoRenewStatus: string;

  @IsOptional()
  @IsNumber()
  expirationIntent?: number;

  @IsOptional()
  @IsNumber()
  gracePeriodExpiresDate?: number;

  @IsOptional()
  @IsString()
  priceIncreaseStatus?: string;

  @IsString()
  environment: string;
}

// Apple notification types
export enum AppleNotificationType {
  CONSUMPTION_REQUEST = 'CONSUMPTION_REQUEST',
  DID_CHANGE_RENEWAL_PREF = 'DID_CHANGE_RENEWAL_PREF',
  DID_CHANGE_RENEWAL_STATUS = 'DID_CHANGE_RENEWAL_STATUS',
  DID_FAIL_TO_RENEW = 'DID_FAIL_TO_RENEW',
  DID_RENEW = 'DID_RENEW',
  EXPIRED = 'EXPIRED',
  GRACE_PERIOD_EXPIRED = 'GRACE_PERIOD_EXPIRED',
  OFFER_REDEEMED = 'OFFER_REDEEMED',
  PRICE_INCREASE = 'PRICE_INCREASE',
  REFUND = 'REFUND',
  RENEWAL_EXTENDED = 'RENEWAL_EXTENDED',
  REVOKE = 'REVOKE',
  SUBSCRIBED = 'SUBSCRIBED',
  TEST = 'TEST',
}
