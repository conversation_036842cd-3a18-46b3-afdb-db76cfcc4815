import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class GooglePubSubMessage {
  @IsString()
  data: string; // Base64 encoded JSON

  @IsString()
  messageId: string;

  @IsString()
  publishTime: string;

  @IsOptional()
  @IsObject()
  attributes?: Record<string, string>;
}

export class GoogleWebhookDto {
  @ValidateNested()
  @Type(() => GooglePubSubMessage)
  message: GooglePubSubMessage;

  @IsString()
  subscription: string;
}

export class GoogleSubscriptionNotification {
  @IsString()
  version: string;

  @IsNumber()
  notificationType: number;

  @IsString()
  purchaseToken: string;

  @IsString()
  subscriptionId: string;

  @IsOptional()
  @IsNumber()
  eventTimeMillis?: number;
}

export class GoogleOneTimeProductNotification {
  @IsString()
  version: string;

  @IsNumber()
  notificationType: number;

  @IsString()
  purchaseToken: string;

  @IsString()
  sku: string;

  @IsOptional()
  @IsNumber()
  eventTimeMillis?: number;
}

export class GoogleDeveloperNotification {
  @IsString()
  version: string;

  @IsString()
  packageName: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => GoogleSubscriptionNotification)
  subscriptionNotification?: GoogleSubscriptionNotification;

  @IsOptional()
  @ValidateNested()
  @Type(() => GoogleOneTimeProductNotification)
  oneTimeProductNotification?: GoogleOneTimeProductNotification;

  @IsOptional()
  @IsObject()
  testNotification?: {
    version: string;
  };
}

// Google Play notification types
export enum GoogleSubscriptionNotificationType {
  SUBSCRIPTION_RECOVERED = 1,
  SUBSCRIPTION_RENEWED = 2,
  SUBSCRIPTION_CANCELED = 3,
  SUBSCRIPTION_PURCHASED = 4,
  SUBSCRIPTION_ON_HOLD = 5,
  SUBSCRIPTION_IN_GRACE_PERIOD = 6,
  SUBSCRIPTION_RESTARTED = 7,
  SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8,
  SUBSCRIPTION_DEFERRED = 9,
  SUBSCRIPTION_PAUSED = 10,
  SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11,
  SUBSCRIPTION_REVOKED = 12,
  SUBSCRIPTION_EXPIRED = 13,
}

export enum GoogleOneTimeProductNotificationType {
  ONE_TIME_PRODUCT_PURCHASED = 1,
  ONE_TIME_PRODUCT_CANCELED = 2,
}
