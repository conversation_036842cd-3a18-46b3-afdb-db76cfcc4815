import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentPlatform } from '../schemas/payment-receipt.schema';

export class PurchaseInfo {
  @ApiProperty({
    description: 'Product ID from the store',
    example: 'com.itrip.credits.basic',
  })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Transaction ID from the store' })
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiProperty({ description: 'Transaction date from the store' })
  @IsString()
  @IsNotEmpty()
  transactionDate: string;

  @ApiProperty({ description: 'Receipt data from the store' })
  @IsString()
  @IsNotEmpty()
  receipt: string;
}

export class RestorePurchasesDto {
  @ApiProperty({
    description: 'Array of purchase information',
    type: [PurchaseInfo],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PurchaseInfo)
  purchases: PurchaseInfo[];

  @ApiProperty({
    description: 'Platform (ios or android)',
    enum: PaymentPlatform,
  })
  @IsEnum(PaymentPlatform)
  platform: PaymentPlatform;
}

export class RestorePurchaseResult {
  @ApiProperty({
    description: 'Whether the purchase was restored successfully',
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message if the purchase failed to restore',
    required: false,
  })
  error?: string;

  @ApiProperty({ description: 'Transaction ID from the store' })
  transactionId: string;

  @ApiProperty({ description: 'Message from the restore operation' })
  message: string;
}

export class RestorePurchasesResponse {
  @ApiProperty({ description: 'Number of purchases restored' })
  restoredCount: number;

  @ApiProperty({
    description: 'Results for each purchase',
    type: [RestorePurchaseResult],
  })
  results: RestorePurchaseResult[];

  @ApiProperty({ description: 'Whether the restore was successful' })
  success: boolean;

  @ApiProperty({ description: 'Message from the restore operation' })
  message: string;
}
