import { Logger, InternalServerErrorException, BadRequestException, ConflictException } from '@nestjs/common';
import { MongoError } from 'mongodb';
import { Error as MongooseError } from 'mongoose';

/**
 * Utility class for handling database errors consistently across the application
 */
export class DatabaseErrorHandler {
  private static readonly logger = new Logger(DatabaseErrorHandler.name);

  /**
   * Check if an error is a database connection error
   * @param error The error to check
   * @returns True if it's a database connection error
   */
  static isDatabaseConnectionError(error: any): boolean {
    if (!error) return false;

    // MongoDB connection errors
    if (error.name === 'MongoNetworkError' || 
        error.name === 'MongoServerSelectionError' ||
        error.name === 'MongoTimeoutError') {
      return true;
    }

    // Mongoose connection errors
    if (error.name === 'MongooseServerSelectionError') {
      return true;
    }

    // Check error messages for connection-related issues
    const errorMessage = error.message?.toLowerCase() || '';
    return errorMessage.includes('connection') ||
           errorMessage.includes('network') ||
           errorMessage.includes('timeout') ||
           errorMessage.includes('server selection');
  }

  /**
   * Check if an error is a duplicate key error
   * @param error The error to check
   * @returns True if it's a duplicate key error
   */
  static isDuplicateKeyError(error: any): boolean {
    return error instanceof MongoError && error.code === 11000;
  }

  /**
   * Check if an error is a validation error
   * @param error The error to check
   * @returns True if it's a validation error
   */
  static isValidationError(error: any): boolean {
    return error instanceof MongooseError.ValidationError ||
           error.name === 'ValidationError';
  }

  /**
   * Check if an error is a cast error (invalid ObjectId, etc.)
   * @param error The error to check
   * @returns True if it's a cast error
   */
  static isCastError(error: any): boolean {
    return error instanceof MongooseError.CastError ||
           error.name === 'CastError';
  }

  /**
   * Handle database errors and throw appropriate HTTP exceptions
   * @param error The database error
   * @param operation Description of the operation that failed
   * @param context Additional context for logging
   * @throws Appropriate HTTP exception
   */
  static handleDatabaseError(
    error: any,
    operation: string,
    context: Record<string, any> = {}
  ): never {
    const contextStr = Object.keys(context).length > 0 ? JSON.stringify(context) : '';
    
    // Log the error with context
    this.logger.error(
      `Database error during ${operation}${contextStr ? ` - Context: ${contextStr}` : ''}`,
      error.stack
    );

    // Handle specific error types
    if (this.isDuplicateKeyError(error)) {
      const duplicateField = this.extractDuplicateField(error);
      throw new ConflictException(
        `Duplicate entry${duplicateField ? ` for ${duplicateField}` : ''}`
      );
    }

    if (this.isValidationError(error)) {
      const validationMessage = this.extractValidationMessage(error);
      throw new BadRequestException(
        validationMessage || 'Invalid data provided'
      );
    }

    if (this.isCastError(error)) {
      throw new BadRequestException('Invalid data format provided');
    }

    if (this.isDatabaseConnectionError(error)) {
      this.logger.error('Database connection error detected', error.stack);
      throw new InternalServerErrorException('Database connection error');
    }

    // For unknown database errors, throw a generic internal server error
    throw new InternalServerErrorException(`Failed to ${operation}`);
  }

  /**
   * Extract the duplicate field name from a duplicate key error
   * @param error The duplicate key error
   * @returns The field name or null
   */
  private static extractDuplicateField(error: MongoError): string | null {
    try {
      if (error.code === 11000 && error.message) {
        // Try to extract field name from error message
        const match = error.message.match(/index: (\w+)_/);
        if (match) {
          return match[1];
        }
        
        // Alternative pattern
        const match2 = error.message.match(/dup key: { (\w+):/);
        if (match2) {
          return match2[1];
        }
      }
    } catch (extractError) {
      this.logger.warn('Failed to extract duplicate field name', extractError);
    }
    return null;
  }

  /**
   * Extract validation error message
   * @param error The validation error
   * @returns The validation message
   */
  private static extractValidationMessage(error: any): string | null {
    try {
      if (error.errors) {
        const messages = Object.values(error.errors).map((err: any) => err.message);
        return messages.join(', ');
      }
      return error.message || null;
    } catch (extractError) {
      this.logger.warn('Failed to extract validation message', extractError);
      return null;
    }
  }

  /**
   * Safely execute a database operation with error handling
   * @param operation The database operation to execute
   * @param operationName Description of the operation
   * @param context Additional context for logging
   * @returns The result of the operation
   */
  static async safeExecute<T>(
    operation: () => Promise<T>,
    operationName: string,
    context: Record<string, any> = {}
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.handleDatabaseError(error, operationName, context);
    }
  }

  /**
   * Safely execute a database operation with error handling and return null on error
   * @param operation The database operation to execute
   * @param operationName Description of the operation
   * @param context Additional context for logging
   * @returns The result of the operation or null on error
   */
  static async safeExecuteWithNull<T>(
    operation: () => Promise<T>,
    operationName: string,
    context: Record<string, any> = {}
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      const contextStr = Object.keys(context).length > 0 ? JSON.stringify(context) : '';
      this.logger.error(
        `Database error during ${operationName}${contextStr ? ` - Context: ${contextStr}` : ''}`,
        error.stack
      );
      return null;
    }
  }
}
