/**
 * Error message mapping for user-friendly error messages
 *
 * This file maps internal error codes and technical messages to user-friendly messages
 * that can be displayed in the UI.
 */

export enum ErrorCode {
  // Authentication errors
  UNAUTHORIZED = 'unauthorized',
  INVALID_CREDENTIALS = 'invalid_credentials',
  SESSION_EXPIRED = 'session_expired',

  // Credit/token errors
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  CREDIT_LIMIT_REACHED = 'credit_limit_reached',

  // Trip generation errors
  GENERATION_FAILED = 'generation_failed',
  INVALID_TRIP_PARAMETERS = 'invalid_trip_parameters',
  LOCATION_NOT_FOUND = 'location_not_found',
  INVALID_DATE_RANGE = 'invalid_date_range',

  // API errors
  API_ERROR = 'api_error',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  SERVICE_UNAVAILABLE = 'service_unavailable',

  // General errors
  VALIDATION_ERROR = 'validation_error',
  NOT_FOUND = 'not_found',
  INTERNAL_ERROR = 'internal_error',
  NETWORK_ERROR = 'network_error',
  TIMEOUT_ERROR = 'timeout_error',
}

interface ErrorMessageConfig {
  userMessage: string;
  developerMessage?: string;
  suggestedAction?: string;
  statusCode?: number;
}

// Map error codes to user-friendly messages
export const errorMessages: Record<ErrorCode, ErrorMessageConfig> = {
  // Authentication errors
  [ErrorCode.UNAUTHORIZED]: {
    userMessage: 'You need to be logged in to access this feature',
    developerMessage: 'User is not authenticated',
    suggestedAction: 'Please log in to continue',
    statusCode: 401,
  },
  [ErrorCode.INVALID_CREDENTIALS]: {
    userMessage: 'The email or password you entered is incorrect',
    developerMessage: 'Invalid login credentials provided',
    suggestedAction: 'Please check your email and password and try again',
    statusCode: 401,
  },
  [ErrorCode.SESSION_EXPIRED]: {
    userMessage: 'Your session has expired',
    developerMessage: 'User session token has expired',
    suggestedAction: 'Please log in again to continue',
    statusCode: 401,
  },

  // Credit/token errors
  [ErrorCode.INSUFFICIENT_CREDITS]: {
    userMessage: "You don't have enough credits to perform this action",
    developerMessage: 'User has insufficient token balance',
    suggestedAction: 'Please purchase more credits to continue',
    statusCode: 402,
  },
  [ErrorCode.CREDIT_LIMIT_REACHED]: {
    userMessage: "You've reached your credit limit for this feature",
    developerMessage: 'User has reached the maximum allowed credit usage',
    suggestedAction:
      'Please purchase more credits or wait until your limit resets',
    statusCode: 402,
  },

  // Trip generation errors
  [ErrorCode.GENERATION_FAILED]: {
    userMessage: "We're having trouble creating your trip right now",
    developerMessage: 'Trip generation process failed',
    suggestedAction: 'Please try again or modify your trip details',
    statusCode: 500,
  },
  [ErrorCode.INVALID_TRIP_PARAMETERS]: {
    userMessage: 'Some of your trip details need adjustment',
    developerMessage: 'Invalid parameters provided for trip generation',
    suggestedAction: 'Please check your trip details and try again',
    statusCode: 400,
  },
  [ErrorCode.LOCATION_NOT_FOUND]: {
    userMessage: "We couldn't find the location you specified",
    developerMessage: 'Specified location could not be found or geocoded',
    suggestedAction: 'Please check the spelling or try a different location',
    statusCode: 400,
  },
  [ErrorCode.INVALID_DATE_RANGE]: {
    userMessage: "The dates you selected aren't valid for trip planning",
    developerMessage: 'Invalid date range provided',
    suggestedAction: 'Please select a valid date range for your trip',
    statusCode: 400,
  },

  // API errors
  [ErrorCode.API_ERROR]: {
    userMessage: "We're experiencing some technical difficulties",
    developerMessage: 'External API returned an error',
    suggestedAction: 'Please try again later',
    statusCode: 500,
  },
  [ErrorCode.RATE_LIMIT_EXCEEDED]: {
    userMessage: 'Our service is currently very busy',
    developerMessage: 'Rate limit exceeded on external API',
    suggestedAction: 'Please try again in a few minutes',
    statusCode: 429,
  },
  [ErrorCode.SERVICE_UNAVAILABLE]: {
    userMessage: 'This service is temporarily unavailable',
    developerMessage: 'External service is unavailable',
    suggestedAction: 'Please try again later',
    statusCode: 503,
  },

  // General errors
  [ErrorCode.VALIDATION_ERROR]: {
    userMessage: "Some of the information you provided isn't valid",
    developerMessage: 'Input validation failed',
    suggestedAction: 'Please check your input and try again',
    statusCode: 400,
  },
  [ErrorCode.NOT_FOUND]: {
    userMessage: "The requested information couldn't be found",
    developerMessage: 'Requested resource not found',
    suggestedAction: 'Please check your request and try again',
    statusCode: 404,
  },
  [ErrorCode.INTERNAL_ERROR]: {
    userMessage: 'Something went wrong on our end',
    developerMessage: 'Internal server error occurred',
    suggestedAction: 'Please try again later',
    statusCode: 500,
  },
  [ErrorCode.NETWORK_ERROR]: {
    userMessage: "We're having trouble connecting to our services",
    developerMessage: 'Network error occurred',
    suggestedAction: 'Please check your internet connection and try again',
    statusCode: 500,
  },
  [ErrorCode.TIMEOUT_ERROR]: {
    userMessage: 'The request took too long to complete',
    developerMessage: 'Request timed out',
    suggestedAction: 'Please try again or simplify your request',
    statusCode: 504,
  },
};

/**
 * Get a user-friendly error message for a given error code
 * @param code Error code
 * @param isDevelopment Whether the application is running in development mode
 * @returns User-friendly error message
 */
export function getUserFriendlyErrorMessage(
  code: ErrorCode,
  isDevelopment: boolean = false,
): string {
  const errorConfig =
    errorMessages[code] || errorMessages[ErrorCode.INTERNAL_ERROR];

  if (isDevelopment && errorConfig.developerMessage) {
    return `${errorConfig.userMessage} (${errorConfig.developerMessage})`;
  }

  return errorConfig.userMessage;
}

/**
 * Create a standardized error response object
 * @param code Error code
 * @param isDevelopment Whether the application is running in development mode
 * @param additionalInfo Additional information to include in the response
 * @returns Standardized error response object
 */
export function createErrorResponse(
  code: ErrorCode,
  isDevelopment: boolean = false,
  additionalInfo: Record<string, any> = {},
) {
  const errorConfig =
    errorMessages[code] || errorMessages[ErrorCode.INTERNAL_ERROR];

  return {
    error: {
      code,
      message: errorConfig.userMessage,
      ...(isDevelopment && { developerMessage: errorConfig.developerMessage }),
      ...(errorConfig.suggestedAction && {
        suggestedAction: errorConfig.suggestedAction,
      }),
      ...additionalInfo,
    },
    success: false,
    statusCode: errorConfig.statusCode || 500,
  };
}
