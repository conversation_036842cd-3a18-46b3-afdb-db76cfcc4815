import { Injectable, Logger } from '@nestjs/common';
import { Resend } from 'resend';
import { ConfigService } from '../config/config.service';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  cc?: string | string[];
  bcc?: string | string[];
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

const emailFrom = 'Trip Itinerary Planner <<EMAIL>>';

/**
 * Email service using Resend as the email provider
 * Provides a centralized way to send emails throughout the application
 */
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private resend: Resend;

  constructor(private configService: ConfigService) {
    const resendConfig = this.configService.getResendConfig();

    if (!resendConfig.apiKey) {
      this.logger.warn('Resend API key not configured. Email functionality will be disabled.');
      return;
    }

    this.resend = new Resend(resendConfig.apiKey);

    this.logger.log('Email service initialized with Resend');
  }

  /**
   * Send a single email
   * @param options Email options
   * @returns Promise with email response
   */
  async sendEmail(options: EmailOptions): Promise<EmailResponse> {
    if (!this.resend) {
      this.logger.error('Email service not initialized. Check Resend API key configuration.');
      return {
        success: false,
        error: 'Email service not configured',
      };
    }

    try {
      // Ensure we have either html or text content
      if (!options.html && !options.text) {
        throw new Error('Email must have either HTML or text content');
      }

      const emailData: any = {
        from: options.from || emailFrom,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
      };

      // Add content - Resend requires at least text or html
      if (options.html) {
        emailData.html = options.html;
      }
      if (options.text) {
        emailData.text = options.text;
      }

      // Add optional fields only if they exist
      if (options.cc) {
        emailData.cc = Array.isArray(options.cc) ? options.cc : [options.cc];
      }
      if (options.bcc) {
        emailData.bcc = Array.isArray(options.bcc) ? options.bcc : [options.bcc];
      }
      if (options.replyTo) {
        emailData.reply_to = options.replyTo;
      }
      if (options.attachments && options.attachments.length > 0) {
        emailData.attachments = options.attachments.map(att => ({
          filename: att.filename,
          content: att.content,
          content_type: att.contentType,
        }));
      }

      this.logger.debug(`Sending email to: ${options.to}, subject: ${options.subject}`);

      const result = await this.resend.emails.send(emailData);

      if (result.error) {
        this.logger.error(`Failed to send email: ${result.error.message}`, result.error);
        return {
          success: false,
          error: result.error.message,
        };
      }

      this.logger.log(`Email sent successfully. Message ID: ${result.data?.id}`);
      return {
        success: true,
        messageId: result.data?.id,
      };
    } catch (error) {
      this.logger.error(`Error sending email: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send verification code email
   * @param email Recipient email
   * @param code Verification code
   * @returns Promise with email response
   */
  async sendVerificationCode(email: string, code: string): Promise<EmailResponse> {
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification - Trip Itinerary Planner</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header with Logo -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0;">
            <img src="https://www.aiplanmytrip.com/_next/image?url=%2Flogo.png&w=256&q=75" alt="Trip Itinerary Planner" style="width: 60px; height: 60px; margin-bottom: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: 700; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
              Trip Itinerary Planner
            </h1>
            <p style="color: #e2e8f0; margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">
              Your AI-Powered Travel Companion
            </p>
          </div>

          <!-- Main Content -->
          <div style="padding: 40px 30px; text-align: center;">
            <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 28px; font-weight: 600;">
              📧 Email Verification
            </h2>
            <p style="color: #4a5568; margin-bottom: 30px; font-size: 16px; line-height: 1.6;">
              Please use the following verification code to complete your registration and start planning amazing trips:
            </p>

            <!-- Verification Code -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 40px; border-radius: 12px; font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 30px 0; display: inline-block; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
              ${code}
            </div>

            <p style="color: #718096; font-size: 14px; margin-top: 30px; line-height: 1.5;">
              ⏰ This code will expire in <strong>10 minutes</strong><br>
              If you didn't request this code, please ignore this email.
            </p>
          </div>

          <!-- Footer -->
          <div style="background-color: #f7fafc; padding: 25px; text-align: center; border-top: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
            <p style="color: #a0aec0; margin: 0; font-size: 14px;">
              © 2024 Trip Itinerary Planner. All rights reserved.
            </p>
            <table width="100%" cellpadding="5" cellspacing="0" style="margin-top: 15px;">
              <tr>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com" style="color: #667eea; text-decoration: none; font-size: 12px;">Visit Website</a>
                </td>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com/help" style="color: #667eea; text-decoration: none; font-size: 12px;">Help Center</a>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Trip Itinerary Planner - Email Verification

      Your verification code is: ${code}

      This code will expire in 10 minutes.
      If you didn't request this code, please ignore this email.

      Visit us at: https://www.aiplanmytrip.com
      Need help? https://www.aiplanmytrip.com/help

      © 2024 Trip Itinerary Planner. All rights reserved.
    `;

    return this.sendEmail({
      to: email,
      subject: 'Trip Itinerary Planner - Email Verification Code 📧',
      html,
      text,
    });
  }

  /**
   * Send welcome email to new users
   * @param email Recipient email
   * @param name User's name
   * @returns Promise with email response
   */
  async sendWelcomeEmail(email: string, name: string): Promise<EmailResponse> {
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Trip Itinerary Planner</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header with Logo -->
          <div style="background-color: #667eea; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; border-radius: 8px 8px 0 0;">
            <img src="https://www.aiplanmytrip.com/_next/image?url=%2Flogo.png&w=256&q=75" alt="Trip Itinerary Planner" style="width: 80px; height: 80px; margin-bottom: 20px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);">
            <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 700; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
              Welcome to Trip Itinerary Planner!
            </h1>
            <p style="color: #e2e8f0; margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
              Your AI-Powered Travel Companion
            </p>
          </div>

          <!-- Main Content -->
          <div style="padding: 40px 30px;">
            <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">
              Hello ${name}! 👋
            </h2>

            <p style="color: #4a5568; line-height: 1.7; font-size: 16px; margin-bottom: 25px;">
              Welcome to <strong>Trip Itinerary Planner</strong>! We're thrilled to have you join our community of smart travelers.
              Get ready to experience travel planning like never before with our AI-powered assistant.
            </p>

            <!-- Features Section -->
            <div style="background-color: #f7fafc; border-radius: 12px; padding: 25px; margin: 30px 0;">
              <h3 style="color: #2d3748; margin: 0 0 25px 0; font-size: 20px; font-weight: 600; text-align: center;">
                What You Can Do
              </h3>

              <!-- Feature 1 -->
              <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 20px;">
                <tr>
                  <td width="40" style="vertical-align: top; padding-right: 15px;">
                    <div style="background-color: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 32px; font-size: 16px;">✈</div>
                  </td>
                  <td style="vertical-align: top;">
                    <strong style="color: #2d3748; font-size: 16px; display: block; margin-bottom: 5px;">Smart Itinerary Generation</strong>
                    <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">Create personalized day-by-day travel plans tailored to your preferences and budget</p>
                  </td>
                </tr>
              </table>

              <!-- Feature 2 -->
              <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 20px;">
                <tr>
                  <td width="40" style="vertical-align: top; padding-right: 15px;">
                    <div style="background-color: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 32px; font-size: 16px;">🗺</div>
                  </td>
                  <td style="vertical-align: top;">
                    <strong style="color: #2d3748; font-size: 16px; display: block; margin-bottom: 5px;">Interactive Maps & Navigation</strong>
                    <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">Visualize your journey with detailed maps and get directions to every destination</p>
                  </td>
                </tr>
              </table>

              <!-- Feature 3 -->
              <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 20px;">
                <tr>
                  <td width="40" style="vertical-align: top; padding-right: 15px;">
                    <div style="background-color: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 32px; font-size: 16px;">💎</div>
                  </td>
                  <td style="vertical-align: top;">
                    <strong style="color: #2d3748; font-size: 16px; display: block; margin-bottom: 5px;">Hidden Gems Discovery</strong>
                    <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">Uncover local secrets and authentic experiences beyond typical tourist spots</p>
                  </td>
                </tr>
              </table>

              <!-- Feature 4 -->
              <table width="100%" cellpadding="0" cellspacing="0">
                <tr>
                  <td width="40" style="vertical-align: top; padding-right: 15px;">
                    <div style="background-color: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 32px; font-size: 16px;">🤖</div>
                  </td>
                  <td style="vertical-align: top;">
                    <strong style="color: #2d3748; font-size: 16px; display: block; margin-bottom: 5px;">AI-Powered Recommendations</strong>
                    <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">Get intelligent suggestions based on your travel style, interests, and real-time data</p>
                  </td>
                </tr>
              </table>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 35px 0;">
              <a href="https://www.aiplanmytrip.com" style="display: inline-block; background-color: #667eea; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);">
                🚀 Start Planning Your Adventure
              </a>
            </div>

            <p style="color: #4a5568; line-height: 1.7; font-size: 16px; text-align: center; margin-top: 30px;">
              Ready to turn your travel dreams into reality? Open the app and let's create your perfect itinerary!
            </p>
          </div>

          <!-- Useful Links Section -->
          <div style="background-color: #f7fafc; padding: 30px; border-top: 1px solid #e2e8f0;">
            <h3 style="color: #2d3748; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; text-align: center;">
              Quick Links
            </h3>

            <table width="100%" cellpadding="10" cellspacing="0">
              <tr>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com" style="color: #667eea; text-decoration: none; font-weight: 500; font-size: 14px;">
                    🌐 Visit Website
                  </a>
                </td>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com/help" style="color: #667eea; text-decoration: none; font-weight: 500; font-size: 14px;">
                    ❓ Help Center
                  </a>
                </td>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com/contact" style="color: #667eea; text-decoration: none; font-weight: 500; font-size: 14px;">
                    📧 Contact Us
                  </a>
                </td>
              </tr>
            </table>
          </div>

          <!-- Footer -->
          <div style="background-color: #2d3748; padding: 25px; text-align: center; border-radius: 0 0 8px 8px;">
            <p style="color: #a0aec0; margin: 0; font-size: 14px;">
              © 2024 Trip Itinerary Planner. All rights reserved.
            </p>
            <p style="color: #718096; margin: 10px 0 0 0; font-size: 12px;">
              You're receiving this email because you signed up for Trip Itinerary Planner.
            </p>
            <table width="100%" cellpadding="5" cellspacing="0" style="margin-top: 15px;">
              <tr>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com/privacy" style="color: #667eea; text-decoration: none; font-size: 12px;">Privacy Policy</a>
                </td>
                <td style="text-align: center;">
                  <a href="https://www.aiplanmytrip.com/terms" style="color: #667eea; text-decoration: none; font-size: 12px;">Terms of Service</a>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Welcome to Trip Itinerary Planner!

      Hello ${name}!

      Welcome to Trip Itinerary Planner! We're thrilled to have you join our community of smart travelers. Get ready to experience travel planning like never before with our AI-powered assistant.

      What You Can Do:

      ✈️ Smart Itinerary Generation
      Create personalized day-by-day travel plans tailored to your preferences and budget

      🗺️ Interactive Maps & Navigation
      Visualize your journey with detailed maps and get directions to every destination

      💎 Hidden Gems Discovery
      Uncover local secrets and authentic experiences beyond typical tourist spots

      🤖 AI-Powered Recommendations
      Get intelligent suggestions based on your travel style, interests, and real-time data

      Ready to turn your travel dreams into reality? Open the app and let's create your perfect itinerary!

      Quick Links:
      🌐 Website: https://www.aiplanmytrip.com
      ❓ Help Center: https://www.aiplanmytrip.com/help
      📧 Contact Us: https://www.aiplanmytrip.com/contact

      © 2024 Trip Itinerary Planner. All rights reserved.

      Privacy Policy: https://www.aiplanmytrip.com/privacy
      Terms of Service: https://www.aiplanmytrip.com/terms
    `;

    return this.sendEmail({
      to: email,
      subject: 'Welcome to Trip Itinerary Planner - Start Planning Your Next Adventure! ✈️',
      html,
      text,
    });
  }

  /**
   * Check if email service is available
   * @returns boolean indicating if service is configured
   */
  isAvailable(): boolean {
    return !!this.resend;
  }
}
