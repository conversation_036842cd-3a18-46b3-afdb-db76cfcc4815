/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';

export enum RetryErrorType {
  NETWORK = 'network',
  RATE_LIMIT = 'rate_limit',
  AUTHENTICATION = 'authentication',
  SERVER = 'server',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

export interface RetryOptions {
  maxAttempts: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
  retryableErrorTypes: RetryErrorType[];
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number, delay: number) => void;
}

const defaultRetryOptions: RetryOptions = {
  maxAttempts: 3,
  initialDelayMs: 1000,
  maxDelayMs: 30000,
  backoffFactor: 2,
  retryableErrorTypes: [
    RetryErrorType.NETWORK,
    RetryErrorType.RATE_LIMIT,
    RetryErrorType.SERVER,
    RetryErrorType.TIMEOUT,
  ],
};

@Injectable()
export class RetryService {
  private readonly logger = new Logger(RetryService.name);

  /**
   * Execute a function with retry logic
   * @param fn Function to execute
   * @param options Retry options
   * @returns Result of the function
   */
  async executeWithRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions = defaultRetryOptions,
  ): Promise<T> {
    const config = { ...defaultRetryOptions, ...options };
    let attempt = 0;
    let lastError: any;

    while (attempt < config.maxAttempts) {
      try {
        return await fn();
      } catch (error) {
        attempt++;
        lastError = error;

        // Check if we've reached max attempts
        if (attempt >= config.maxAttempts) {
          this.logger.error(
            `Retry failed after ${attempt} attempts: ${error.message}`,
          );
          throw error;
        }

        // Check if this error is retryable
        const errorType = this.categorizeError(error);
        const isRetryable =
          config.retryableErrorTypes.includes(errorType) ||
          (config.retryCondition && config.retryCondition(error));

        if (!isRetryable) {
          this.logger.warn(
            `Non-retryable error (${errorType}): ${error.message}`,
          );
          throw error;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.initialDelayMs * Math.pow(config.backoffFactor, attempt - 1),
          config.maxDelayMs,
        );

        // Add some jitter to prevent thundering herd
        const jitteredDelay = delay * (0.8 + Math.random() * 0.4);

        // Log retry attempt
        this.logger.warn(
          `Retry attempt ${attempt}/${config.maxAttempts} after ${Math.round(
            jitteredDelay,
          )}ms for error: ${error.message}`,
        );

        // Call onRetry callback if provided
        if (config.onRetry) {
          config.onRetry(error, attempt, jitteredDelay);
        }

        // Wait before retrying
        await this.delay(jitteredDelay);
      }
    }

    // This should never happen due to the throw in the loop
    throw lastError;
  }

  /**
   * Categorize an error to determine if it's retryable
   * @param error Error to categorize
   * @returns Error type
   */
  private categorizeError(error: any): RetryErrorType {
    // Network errors
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ENOTFOUND' ||
      error.message?.includes('network') ||
      error.message?.includes('connection')
    ) {
      return RetryErrorType.NETWORK;
    }

    // Rate limit errors
    if (
      error.status === 429 ||
      error.statusCode === 429 ||
      error.message?.includes('rate limit') ||
      error.message?.includes('too many requests')
    ) {
      return RetryErrorType.RATE_LIMIT;
    }

    // Authentication errors
    if (
      error.status === 401 ||
      error.statusCode === 401 ||
      error.status === 403 ||
      error.statusCode === 403 ||
      error.message?.includes('authentication') ||
      error.message?.includes('unauthorized') ||
      error.message?.includes('forbidden')
    ) {
      return RetryErrorType.AUTHENTICATION;
    }

    // Server errors
    if (
      (error.status >= 500 && error.status < 600) ||
      (error.statusCode >= 500 && error.statusCode < 600) ||
      error.message?.includes('server error')
    ) {
      return RetryErrorType.SERVER;
    }

    // Timeout errors
    if (
      error.code === 'ETIMEDOUT' ||
      error.message?.includes('timeout') ||
      error.message?.includes('timed out')
    ) {
      return RetryErrorType.TIMEOUT;
    }

    // Default to unknown
    return RetryErrorType.UNKNOWN;
  }

  /**
   * Simple delay function
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after the delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
