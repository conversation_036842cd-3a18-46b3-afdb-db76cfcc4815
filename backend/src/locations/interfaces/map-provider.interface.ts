import { Logger } from '@nestjs/common';
import { LocationCoordinates } from './location-coordinates.interface';

export interface MapProvider {
  logger: Logger;
  apiKey: string;
  baseUrl: string;

  /**
   * Geocode a location using the map provider API
   * @param location Location name to geocode
   * @returns Location details with coordinates or null if not found
   */
  geocode(location: string): Promise<LocationCoordinates | null>;

  //   /**
  //    * Reverse geocode coordinates to get location details
  //    * @param lat Latitude
  //    * @param lng Longitude
  //    * @returns Location details or null if not found
  //    */
  //   reverseGeocode(lat: number, lng: number): Promise<LocationCoordinates | null>;
}
