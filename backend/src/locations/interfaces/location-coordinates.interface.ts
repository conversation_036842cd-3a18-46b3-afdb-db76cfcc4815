export interface LocationCoordinates {
  name: string;
  formatted_address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  place_id: string;
  types: string[];
  source:
    | 'google_maps'
    | 'nominatim'
    | 'ors'
    | 'database'
    | 'opencage'
    | 'geocode';
  confidence?: 'high' | 'medium' | 'low';
  country?: string;
  city?: string;
}

export interface LocationContext {
  city?: string;
  country?: string;
  countryCode?: string;
}
