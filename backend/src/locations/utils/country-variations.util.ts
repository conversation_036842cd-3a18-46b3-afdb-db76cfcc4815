/* eslint-disable */
import * as fs from 'fs';
import * as path from 'path';

/**
 * Utility class for handling country name variations
 */
export class CountryVariationsUtil {
  private static countryVariations: Record<string, string[]> | null = null;

  /**
   * Load country variations from JSON file
   * @returns Country variations mapping
   */
  private static loadCountryVariations(): Record<string, string[]> {
    if (this.countryVariations === null) {
      try {
        const filePath = path.join(
          __dirname,
          '../data/country-variations.json',
        );
        const fileContent = fs.readFileSync(filePath, 'utf8');
        this.countryVariations = JSON.parse(fileContent);
      } catch (error) {
        console.error('Error loading country variations:', error);
        this.countryVariations = {};
      }
    }
    return this.countryVariations as Record<string, string[]>;
  }

  /**
   * Check if two country names match, accounting for variations
   * @param foundCountry Country name found in the result
   * @param expectedCountry Country name expected from context
   * @returns Boolean indicating if countries match
   */
  static isCountryMatch(
    foundCountry: string,
    expectedCountry: string,
  ): boolean {
    if (!foundCountry || !expectedCountry) return false;

    // Normalize country names for comparison
    const normalizeCountry = (name: string): string => {
      return name.toLowerCase().replace(/\s+/g, ' ').trim();
    };

    const normalizedFound = normalizeCountry(foundCountry);
    const normalizedExpected = normalizeCountry(expectedCountry);

    // Direct match
    if (normalizedFound === normalizedExpected) return true;

    // Check if one contains the other
    if (
      normalizedFound.includes(normalizedExpected) ||
      normalizedExpected.includes(normalizedFound)
    )
      return true;

    // Load country variations
    const countryVariations = this.loadCountryVariations();

    // Check for variations
    for (const [standard, variations] of Object.entries(countryVariations)) {
      if (
        normalizedFound === standard ||
        variations.includes(normalizedFound)
      ) {
        return (
          normalizedExpected === standard ||
          variations.includes(normalizedExpected)
        );
      }
    }

    return false;
  }

  /**
   * Get all variations for a given country name
   * @param countryName Country name to get variations for
   * @returns Array of country name variations
   */
  static getCountryVariations(countryName: string): string[] {
    const normalizedName = countryName
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .trim();
    const countryVariations = this.loadCountryVariations();

    // Check if the name is a standard name
    if (countryVariations[normalizedName]) {
      return [normalizedName, ...countryVariations[normalizedName]];
    }

    // Check if the name is a variation of a standard name
    for (const [standard, variations] of Object.entries(countryVariations)) {
      if (variations.includes(normalizedName)) {
        return [standard, ...variations];
      }
    }

    // Return the original name if no variations found
    return [normalizedName];
  }
}
