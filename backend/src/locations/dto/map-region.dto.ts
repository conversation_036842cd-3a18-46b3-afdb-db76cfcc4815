import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

export class RegionDto {
  @ApiProperty({ description: 'Latitude of the center of the region' })
  @IsNumber()
  latitude: number;

  @ApiProperty({ description: 'Longitude of the center of the region' })
  @IsNumber()
  longitude: number;

  @ApiProperty({ description: 'Latitude delta (zoom level)' })
  @IsNumber()
  latitudeDelta: number;

  @ApiProperty({ description: 'Longitude delta (zoom level)' })
  @IsNumber()
  longitudeDelta: number;
}

export class MapRegionDto {
  @ApiProperty({ description: 'Map region data', type: RegionDto })
  @IsObject()
  region: RegionDto;

  @ApiProperty({ description: 'Trip ID', required: false })
  @IsString()
  @IsOptional()
  tripId?: string;

  @ApiProperty({ description: 'Day number', required: false })
  @IsNumber()
  @IsOptional()
  dayNumber?: number;

  @ApiProperty({ description: 'Country name', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({ description: 'City name', required: false })
  @IsString()
  @IsOptional()
  city?: string;
}
