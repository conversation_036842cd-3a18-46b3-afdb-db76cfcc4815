import { ConfigService } from '../../config/config.service';

/**
 * Distance calculation utilities for geographic coordinates
 */
export class DistanceCalculatorHelper {
  /**
   * Calculate the distance between two coordinates using the Haversine formula
   * @param lat1 Latitude of first point
   * @param lng1 Longitude of first point
   * @param lat2 Latitude of second point
   * @param lng2 Longitude of second point
   * @returns Distance in kilometers
   */
  static calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return distance;
  }

  /**
   * Check if a coordinate is within a specified radius of another coordinate
   * @param activityLat Activity latitude
   * @param activityLng Activity longitude
   * @param cityLat City latitude
   * @param cityLng City longitude
   * @param maxDistanceKm Maximum allowed distance in kilometers
   * @returns True if within radius, false otherwise
   */
  static isWithinRadius(
    activityLat: number,
    activityLng: number,
    cityLat: number,
    cityLng: number,
    maxDistanceKm: number,
  ): boolean {
    const distance = this.calculateDistance(
      activityLat,
      activityLng,
      cityLat,
      cityLng,
    );
    
    return distance <= maxDistanceKm;
  }

  /**
   * Convert degrees to radians
   * @param degrees Degrees to convert
   * @returns Radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get appropriate maximum distance based on city size/type
   * This provides different radius limits for different types of locations
   * @param configService Configuration service to get the radius from environment
   * @param cityName City name (optional, for future enhancements)
   * @returns Maximum distance in kilometers
   */
  static getMaxDistanceForCity(configService: ConfigService, _cityName?: string): number {
    // Get radius from configuration (defaults to 100km if not set)
    const config = configService.getLocationValidationConfig();
    const defaultRadius = config.proximityRadiusKm;

    // This can be enhanced in the future to have different limits based on city size
    // For example: major cities could have larger radius, small towns smaller radius, etc.
    return defaultRadius;
  }
}
