/* eslint-disable */

import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt.guard';
import { MapRegionDto } from '../dto/map-region.dto';
import { MapRegionsService } from '../services/map-regions.service';

@ApiTags('map-regions')
@Controller('map-regions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MapRegionsController {
  constructor(private readonly mapRegionsService: MapRegionsService) {}

  @Get('country/:name')
  @ApiOperation({ summary: 'Get map region for a country' })
  @ApiParam({ name: 'name', description: 'Country name' })
  @ApiResponse({
    status: 200,
    description: 'Returns map region for the country',
    type: MapRegionDto,
  })
  async getCountryMapRegion(
    @Param('name') name: string,
  ): Promise<MapRegionDto> {
    try {
      return this.mapRegionsService.getCountryMapRegion(name);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('city/:name')
  @ApiOperation({ summary: 'Get map region for a city' })
  @ApiParam({ name: 'name', description: 'City name' })
  @ApiQuery({ name: 'country', description: 'Country name', required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns map region for the city',
    type: MapRegionDto,
  })
  async getCityMapRegion(
    @Param('name') name: string,
    @Query('country') country?: string,
  ): Promise<MapRegionDto> {
    try {
      return this.mapRegionsService.getCityMapRegion(name, country);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('trip/:id')
  @ApiOperation({ summary: 'Get map region for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns map region for the trip',
    type: MapRegionDto,
  })
  async getTripMapRegion(@Param('id') id: string): Promise<MapRegionDto> {
    try {
      return this.mapRegionsService.getTripMapRegion(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('trip/:id/day/:day')
  @ApiOperation({ summary: 'Get map region for a specific day of a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiParam({ name: 'day', description: 'Day number' })
  @ApiResponse({
    status: 200,
    description: 'Returns map region for the day',
    type: MapRegionDto,
  })
  async getDailyMapRegion(
    @Param('id') id: string,
    @Param('day') day: number,
  ): Promise<MapRegionDto> {
    try {
      return this.mapRegionsService.getDailyMapRegion(id, day);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
