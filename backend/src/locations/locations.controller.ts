import { Controller, Get, Query, BadRequestException, InternalServerErrorException, Logger } from '@nestjs/common';
import { LocationsService } from './locations.service';
import { LocationSuggestion } from './types';
import { CountryRegionsResponse } from './interfaces/country-region.interface';

@Controller('locations')
export class LocationsController {
  private readonly logger = new Logger(LocationsController.name);

  constructor(private readonly locationsService: LocationsService) { }

  @Get('countries')
  async searchCountries(
    @Query('keyword') keyword: string,
  ): Promise<LocationSuggestion[]> {
    try {
      if (!keyword || typeof keyword !== 'string') {
        throw new BadRequestException('Keyword parameter is required and must be a string');
      }

      if (keyword.length < 2) {
        throw new BadRequestException('Keyword must be at least 2 characters long');
      }

      return await this.locationsService.searchCountries(keyword);
    } catch (error) {
      this.logger.error(`Error searching countries with keyword: ${keyword}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to search countries');
    }
  }

  @Get('cities')
  async searchCities(
    @Query('keyword') keyword: string,
    @Query('country') country?: string,
  ): Promise<LocationSuggestion[]> {
    try {
      if (!keyword || typeof keyword !== 'string') {
        throw new BadRequestException('Keyword parameter is required and must be a string');
      }

      if (keyword.length < 2) {
        throw new BadRequestException('Keyword must be at least 2 characters long');
      }

      if (!country || typeof country !== 'string') {
        throw new BadRequestException('Country parameter is required and must be a string');
      }

      return await this.locationsService.searchCities(keyword, country);
    } catch (error) {
      this.logger.error(`Error searching cities with keyword: ${keyword}, country: ${country}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to search cities');
    }
  }

  @Get('validate')
  async validateLocation(
    @Query('name') name: string,
    @Query('type') type: 'country' | 'city',
  ): Promise<LocationSuggestion | null> {
    try {
      if (!name || typeof name !== 'string') {
        throw new BadRequestException('Name parameter is required and must be a string');
      }

      if (!type || !['country', 'city'].includes(type)) {
        throw new BadRequestException('Type parameter is required and must be either "country" or "city"');
      }

      return await this.locationsService.validateLocation(name, type);
    } catch (error) {
      this.logger.error(`Error validating location with name: ${name}, type: ${type}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to validate location');
    }
  }

  /**
   * Get country regions data for map view
   * @returns CountryRegionsResponse object containing regions data
   */
  @Get('country-regions')
  async getCountryRegions(): Promise<CountryRegionsResponse> {
    try {
      return await this.locationsService.getCountryRegions();
    } catch (error) {
      this.logger.error('Error getting country regions', error.stack);
      throw new InternalServerErrorException('Failed to get country regions');
    }
  }
}
