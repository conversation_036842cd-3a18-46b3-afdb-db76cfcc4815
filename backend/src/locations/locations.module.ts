import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { Trip, TripSchema } from '../trips/schemas/trip.schema';
import { MapRegionsController } from './controllers/map-regions.controller';
import { Location, LocationSchema } from './location.schema';
import { LocationsController } from './locations.controller';
import { LocationsService } from './locations.service';
import { GeocodeService } from './providers/geocode.provider';
import { GoogleMapsService } from './providers/google-maps.provider';
import { NominatimService } from './providers/nominatim.provider';
import { OpenCageService } from './providers/opencage.provider';
import { MapRegionsService } from './services/map-regions.service';
import { MapsValidationService } from './services/maps-validation.service';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: Location.name, schema: LocationSchema },
      { name: Trip.name, schema: TripSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
  ],
  controllers: [LocationsController, MapRegionsController],
  providers: [
    LocationsService,
    GoogleMapsService,
    NominatimService,
    OpenCageService,
    GeocodeService,
    MapsValidationService,
    MapRegionsService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
  exports: [
    LocationsService,
    OpenCageService,
    GeocodeService,
    MapsValidationService,
    MapRegionsService,
  ],
})
export class LocationsModule {}
