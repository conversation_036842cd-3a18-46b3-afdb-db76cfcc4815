/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { BaseMapsProvider } from './base-maps.provider';
import { ConfigService } from 'src/config/config.service';
import {
  LocationContext,
  LocationCoordinates,
} from '../interfaces/location-coordinates.interface';

@Injectable()
export class GeocodeService extends BaseMapsProvider {
  constructor(private readonly configService: ConfigService) {
    super();
    this.logger = new Logger(GeocodeService.name);
    const geocodeConfig = this.configService.getGeocodeConfig();
    this.apiKey = geocodeConfig.apiKey;
    this.baseUrl = geocodeConfig.apiUrl;
    if (!this.apiKey || !this.baseUrl) {
      this.logger.warn(
        'Geocode Earth API key or API URL is not set. Geocode Earth geocoding will not work.',
      );
    }
  }

  /**
   * Map Geocode Earth confidence level to our confidence levels
   * @param confidence Geocode Earth confidence (0-1)
   * @returns Our confidence level
   */
  private mapConfidenceLevel(confidence: number): 'high' | 'medium' | 'low' {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * Geocode a location using Geocode Earth API with context
   * @param location Location name to geocode
   * @param context Additional context information
   * @returns Location details with coordinates or null if not found
   */
  override async geocode(
    location: string,
    context?: LocationContext,
  ): Promise<LocationCoordinates | null> {
    try {
      if (!this.apiKey || !this.baseUrl) {
        this.logger.warn('Geocode Earth API key or URL not available');
        return null;
      }

      // Build the search query with context
      let searchQuery = location;

      // Add city and country to the search query if provided, but avoid duplicating city name
      if (context?.city && context?.country) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}, ${context.country}`;
        } else {
          searchQuery = `${location}, ${context.country}`;
        }
      } else if (context?.city) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}`;
        } else {
          searchQuery = location;
        }
      } else if (context?.country) {
        searchQuery = `${location}, ${context.country}`;
      }

      this.logger.log(`Geocode Earth geocoding with context: ${searchQuery}`);

      // Build URL with parameters
      const url = new URL(`${this.baseUrl}/v1/autocomplete`);
      url.searchParams.set('text', searchQuery);
      url.searchParams.set('api_key', this.apiKey);
      url.searchParams.set('size', '5');

      // Add country restriction if country code is provided
      if (context?.countryCode) {
        url.searchParams.set(
          'boundary.country',
          context.countryCode.toUpperCase(),
        );
      }

      const response = await fetch(url.toString());
      const data = await response.json();

      if (!data.features || data.features.length === 0) {
        this.logger.debug(`No Geocode Earth results found for: ${searchQuery}`);
        return null;
      }

      if (!data.features || data.features.length === 0) {
        this.logger.debug(
          `No Geocode Earth results found for: ${searchQuery}`,
        );
        return null;
      }

      const result = data.features[0];

      // Verify coordinates are within expected bounds
      const coordinates = {
        lat: result.geometry.coordinates[1], // Geocode Earth returns [lng, lat]
        lng: result.geometry.coordinates[0],
      };

      if (!this.areCoordinatesValid(coordinates.lat, coordinates.lng)) {
        this.logger.warn(
          `Invalid coordinates from Geocode Earth geocoding: ${coordinates.lat}, ${coordinates.lng}`,
        );
        return null;
      }

      return {
        name: location, // Use the original location name for precise validation
        formatted_address: result.properties.label || result.properties.name,
        coordinates,
        place_id: result.properties.gid || `geocode_${Date.now()}`,
        types: result.properties.layer
          ? [result.properties.layer]
          : ['geocode'],
        source: 'geocode',
        confidence: this.mapConfidenceLevel(
          result.properties.confidence || 0.5,
        ),
        country: result.properties.country,
        city: result.properties.locality || result.properties.region,
      };
    } catch (error) {
      this.logger.error(`Error geocoding with Geocode Earth: ${error.message}`);
      return null;
    }
  }

  /**
   * Reverse geocode coordinates to get location details using Geocode Earth
   * @param lat Latitude
   * @param lng Longitude
   * @returns Location details or null if not found
   */
  async reverseGeocode(
    lat: number,
    lng: number,
  ): Promise<LocationCoordinates | null> {
    try {
      if (!this.apiKey || !this.baseUrl) {
        this.logger.warn('Geocode Earth API key or URL not available');
        return null;
      }

      if (!this.areCoordinatesValid(lat, lng)) {
        this.logger.warn(
          `Invalid coordinates for reverse geocoding: ${lat}, ${lng}`,
        );
        return null;
      }

      this.logger.log(`Geocode Earth reverse geocoding: ${lat}, ${lng}`);

      // Build URL with parameters
      const url = new URL(`${this.baseUrl}/v1/reverse`);
      url.searchParams.set('point.lat', lat.toString());
      url.searchParams.set('point.lon', lng.toString());
      url.searchParams.set('api_key', this.apiKey);
      url.searchParams.set('size', '1');

      const response = await fetch(url.toString());
      const data = await response.json();

      if (!data.features || data.features.length === 0) {
        this.logger.debug(
          `No Geocode Earth reverse geocoding results found for: ${lat}, ${lng}`,
        );
        return null;
      }

      const result = data.features[0];

      return {
        name:
          result.properties.name ||
          result.properties.label?.split(',')[0] ||
          'Unknown',
        formatted_address:
          result.properties.label || result.properties.name || 'Unknown',
        coordinates: {
          lat: result.geometry.coordinates[1], // Geocode Earth returns [lng, lat]
          lng: result.geometry.coordinates[0],
        },
        place_id: result.properties.gid || `geocode_reverse_${Date.now()}`,
        types: result.properties.layer
          ? [result.properties.layer]
          : ['geocode'],
        source: 'geocode',
        confidence: this.mapConfidenceLevel(
          result.properties.confidence || 0.5,
        ),
        country: result.properties.country,
        city: result.properties.locality || result.properties.region,
      };
    } catch (error) {
      this.logger.error(
        `Error reverse geocoding with Geocode Earth: ${error.message}`,
      );
      return null;
    }
  }
}
