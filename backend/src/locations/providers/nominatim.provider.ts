/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { LocationCoordinates } from '../interfaces/location-coordinates.interface';
import { LocationContext } from '../interfaces/location-coordinates.interface';
import { BaseMapsProvider } from './base-maps.provider';

@Injectable()
export class NominatimService extends BaseMapsProvider {
  private readonly userAgent: string;

  constructor() {
    super();
    this.logger = new Logger(NominatimService.name);
    this.baseUrl = 'https://nominatim.openstreetmap.org';
    this.userAgent = 'iTrip_App/1.0';
  }

  /**
   * Validate a location and get its coordinates using Nominatim
   * @param location Location name or address to validate
   * @returns Location details with coordinates or null if not found
   */
  override async geocode(
    location: string,
    _context?: LocationContext,
  ): Promise<LocationCoordinates | null> {
    try {
      try {
        const url = `${this.baseUrl}/search?q=${encodeURIComponent(
          location,
        )}&format=json&limit=1&polygon=0&addressdetails=1`;

        const response = await fetch(url, {
          headers: {
            'User-Agent': this.userAgent,
            'Accept-Language': 'en-US,en;q=0.9',
          },
        });

        const data = await response.json();

        if (!data || data.length === 0) {
          this.logger.debug(`No results found for location: ${location}`);
          return null;
        }

        if (!data || data.length === 0) {
          this.logger.debug(
            `No results found for: ${location}`,
          );
          return null;
        }

        const result = data[0];

        // Verify coordinates are within expected bounds
        const coordinates = {
          lat: parseFloat(result.lat),
          lng: parseFloat(result.lon),
        };

        if (!this.areCoordinatesValid(coordinates.lat, coordinates.lng)) {
          this.logger.warn(
            `Invalid coordinates from Nominatim geocoding: ${coordinates.lat}, ${coordinates.lng}`,
          );
          return null;
        }

        return {
          name: location, // Use the original location name for precise validation
          formatted_address: result.display_name,
          coordinates,
          place_id: result.place_id,
          types: [result.type],
          source: 'nominatim',
          confidence: 'medium', // Lower confidence since this is a fallback
          country: result.address.country,
          city:
            result.address.city ||
            result.address.town ||
            result.address.village,
        };
      } catch (error) {
        this.logger.error(
          `Error validating location with Nominatim: ${error.message}`,
        );
        return null;
      }
    } catch (error) {
      this.logger.error(
        `Error validating location with Nominatim: ${error.message}`,
      );
      return null;
    }
  }
}
