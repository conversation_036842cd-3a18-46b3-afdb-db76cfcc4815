import { Logger } from '@nestjs/common';
import { LocationCoordinates } from '../interfaces/location-coordinates.interface';
import { MapProvider } from '../interfaces/map-provider.interface';

export class BaseMapsProvider implements MapProvider {
  logger: Logger;
  apiKey: string;
  baseUrl: string;

  constructor() {
    this.logger = new Logger(BaseMapsProvider.name);
  }

  geocode(location: string): Promise<LocationCoordinates | null> {
    this.logger.warn('Geocoding not implemented in base class', location);
    throw new Error('Method not implemented.');
  }

  /**
   * Check if coordinates are within valid bounds
   * @param lat Latitude
   * @param lng Longitude
   * @returns True if coordinates are valid
   */
  protected areCoordinatesValid(lat: number, lng: number): boolean {
    return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
  }










}
