/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import {
  LocationContext,
  LocationCoordinates,
} from '../interfaces/location-coordinates.interface';
import { BaseMapsProvider } from './base-maps.provider';

@Injectable()
export class OpenCageService extends BaseMapsProvider {
  constructor(private readonly configService: ConfigService) {
    super();
    this.logger = new Logger(OpenCageService.name);
    const openCageConfig = this.configService.getOpenCageConfig();
    this.apiKey = openCageConfig.apiKey;
    this.baseUrl = openCageConfig.apiUrl;
    if (!this.apiKey || !openCageConfig.apiUrl) {
      this.logger.warn(
        'OpenCage API key or API URL is not set. OpenCage geocoding will not work.',
      );
    }
  }

  override async geocode(
    location: string,
    context?: LocationContext,
  ): Promise<LocationCoordinates | null> {
    try {
      if (!this.apiKey) {
        this.logger.warn('OpenCage API key not available');
        return null;
      }

      // Build the search query with context
      let searchQuery = location;

      // Add city and country to the search query if provided, but avoid duplicating city name
      if (context?.city && context?.country) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}, ${context.country}`;
        } else {
          searchQuery = `${location}, ${context.country}`;
        }
      } else if (context?.city) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}`;
        } else {
          searchQuery = location;
        }
      } else if (context?.country) {
        searchQuery = `${location}, ${context.country}`;
      }

      this.logger.log(`OpenCage geocoding with context: ${searchQuery}`);

      // Build URL with parameters
      const url = new URL(`${this.baseUrl}/geocode/v1/json`);
      url.searchParams.set('q', searchQuery);
      url.searchParams.set('key', this.apiKey);
      url.searchParams.set('limit', '5');
      url.searchParams.set('no_annotations', '1');
      url.searchParams.set('language', 'en');
      url.searchParams.set('no_dedupe', '1');

      // Add country restriction if country code is provided
      if (context?.countryCode) {
        url.searchParams.set('countrycode', context.countryCode.toLowerCase());
      }

      const response = await fetch(url.toString());
      const data = await response.json();

      if (
        data.status?.code !== 200 ||
        !data.results ||
        data.results.length === 0
      ) {
        this.logger.debug(`No OpenCage results found for: ${searchQuery}`);
        return null;
      }

      const result = data.results[0];


      // Verify coordinates are within expected bounds
      const coordinates = {
        lat: result.geometry.lat,
        lng: result.geometry.lng,
      };

      if (!this.areCoordinatesValid(coordinates.lat, coordinates.lng)) {
        this.logger.warn(
          `Invalid coordinates from OpenCage geocoding: ${coordinates.lat}, ${coordinates.lng}`,
        );
        return null;
      }

      return {
        name: location, // Use the original location name for precise validation
        formatted_address: result.formatted,
        coordinates,
        place_id: result.annotations?.OSM?.edit_url || `opencage_${Date.now()}`,
        types: result.components._type
          ? [result.components._type]
          : ['geocode'],
        source: 'opencage',
        confidence: this.mapConfidenceLevel(result.confidence),
        country: result.components.country,
        city:
          result.components.city ||
          result.components.town ||
          result.components.village,
      };
    } catch (error) {
      this.logger.error(`Error geocoding with OpenCage: ${error.message}`);
      return null;
    }
  }

  /**
   * Map OpenCage confidence level to our confidence scale
   * @param confidence OpenCage confidence (0-10)
   * @returns Our confidence level
   */
  private mapConfidenceLevel(confidence: number): 'high' | 'medium' | 'low' {
    if (confidence >= 8) return 'high';
    if (confidence >= 5) return 'medium';
    return 'low';
  }
}
