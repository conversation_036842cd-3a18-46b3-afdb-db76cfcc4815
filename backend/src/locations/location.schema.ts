import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Location {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  country: string;

  @Prop({ required: true })
  type: string;

  @Prop({ type: { lat: Number, lng: Number }, required: true })
  coordinates: {
    lat: number;
    lng: number;
  };

  @Prop()
  population?: number;
}

export type LocationDocument = Location & Document;
export const LocationSchema = SchemaFactory.createForClass(Location);

// Create indexes
LocationSchema.index({ name: 'text' });
LocationSchema.index({ country: 1 });
LocationSchema.index({ type: 1 });
LocationSchema.index({ 'coordinates.lat': 1, 'coordinates.lng': 1 });
