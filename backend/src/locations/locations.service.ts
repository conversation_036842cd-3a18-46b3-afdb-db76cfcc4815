/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { getCountryDataList } from 'countries-list';
import * as fs from 'fs';
import { Model } from 'mongoose';
import * as path from 'path';
import { ConfigService } from '../config/config.service';
import { CountryRegionsResponse } from './interfaces/country-region.interface';
import { Location, LocationDocument } from './location.schema';
import { LocationSuggestion } from './types';

@Injectable()
export class LocationsService {
  private readonly ORS_API_URL: string;
  private readonly ORS_API_KEY: string;
  private readonly logger = new Logger(LocationsService.name);

  constructor(
    private configService: ConfigService,
    @InjectModel(Location.name) private locationModel: Model<LocationDocument>,
  ) {
    const orsConfig = this.configService.getOpenRouteServiceConfig();
    this.ORS_API_URL = orsConfig.apiUrl;
    this.ORS_API_KEY = orsConfig.apiKey;

    if (!this.ORS_API_KEY) {
      this.logger.warn(
        'ORS_API_KEY is not set. OpenRouteService functionality will be limited.',
      );
    }
  }

  private getCountry(keyword: string) {
    const searchTerm = keyword.toLowerCase();

    // Calculate scores for each country based on match quality
    const scoredCountries = getCountryDataList().map((country) => {
      const countryName = country.name.toLowerCase();
      let score = 0;

      // Exact match gets highest score
      if (countryName === searchTerm) {
        score += 100;
      }
      // Starts with search term
      else if (countryName.startsWith(searchTerm)) {
        score += 75;
      }
      // Contains search term as a word
      else if (
        countryName.includes(` ${searchTerm}`) ||
        countryName.includes(`${searchTerm} `)
      ) {
        score += 50;
      }
      // Contains search term anywhere
      else if (countryName.includes(searchTerm)) {
        score += 25;
      }

      return {
        country,
        score,
      };
    });

    // Sort by score descending and get the best match
    const bestMatch = scoredCountries
      .filter((c) => c.score > 0)
      .sort((a, b) => b.score - a.score);

    return bestMatch?.map((c) => c.country);
  }

  async searchInDb(
    keyword: string,
    country: string,
  ): Promise<LocationSuggestion[]> {
    const searchTerm = keyword.toLowerCase();

    if (!country) {
      throw new BadRequestException('Country is required for city search');
    }
    if (!keyword) {
      throw new BadRequestException('Keyword is required for city search');
    }

    const countryMatch = this.getCountry(country);
    if (!countryMatch.length) {
      return [];
    }

    const countryCode = countryMatch[0].iso2.toLowerCase();

    try {
      // Use MongoDB text search with country filter
      const locations = await this.locationModel
        .find({
          $and: [
            { country: { $regex: countryCode, $options: 'i' } },
            { name: { $regex: searchTerm, $options: 'i' } },
          ],
        })
        .sort({ population: -1, name: 1 })
        .limit(10)
        .exec();

      return locations.map((location) => ({
        name: location.name,
        country: countryMatch[0].name,
        type: location.type as 'country' | 'city',
        coordinates: location.coordinates,
        population: location.population,
      }));
    } catch (error) {
      Logger.error('Error searching in database:', error);
      return [];
    }
  }

  public async searchWithORS(
    query: string,
    type: 'country' | 'city',
    countryCode?: string,
  ): Promise<LocationSuggestion[]> {
    try {
      if (!this.ORS_API_KEY) {
        this.logger.warn(
          'ORS_API_KEY is not set. Cannot perform OpenRouteService search.',
        );
        return [];
      }

      const headers = new Headers();
      headers.set('Authorization', this.ORS_API_KEY);
      headers.set('Accept', 'application/json');
      const layers = type === 'city' ? 'locality,county,address' : 'country';
      let url = `${this.ORS_API_URL}/geocode/autocomplete?text=${encodeURIComponent(
        query,
      )}&size=10&layers=${layers}`;

      if (type === 'city' && countryCode) {
        url += `&boundary.country=${countryCode}`;
      }
      const response = await fetch(url,
        {
          headers,
        },
      );
      if (!response.ok) {
        throw new Error(`Geocoding failed: ${response.statusText}`);
      }

      const data = await response.json();
      const suggestions: LocationSuggestion[] = [];

      if (data.features) {
        for (const feature of data.features) {
          const properties = feature.properties;
          const [lng, lat] = feature.geometry.coordinates;
          suggestions.push({
            name: properties.name,
            country: properties.country,
            type: 'city',
            coordinates: { lat, lng },
          });
        }
      }

      return suggestions
        .filter(
          (suggestion, index, self) =>
            index ===
            self.findIndex(
              (s) => s.name === suggestion.name && s.type === 'city',
            ),
        )
    } catch (error) {
      console.error('Error getting location suggestions from ORS:', error);
      return [];
    }
  }

  async searchCountries(keyword: string): Promise<LocationSuggestion[]> {
    if (!keyword || keyword.length < 2) return [];

    const country = this.getCountry(keyword);
    if (country.length > 0) {
      return country.map((c) => ({
        name: c.name,
        type: 'country',
      }));
    }

    return [];
  }

  async searchCities(
    keyword: string,
    country?: string,
  ): Promise<LocationSuggestion[]> {
    if (!country) {
      return [];
    }
    if (!keyword || keyword.length < 2) return [];

    const searchQuery = keyword;

    let countryCode;

    const countryMatch = this.getCountry(country);

    if (countryMatch.length > 0) {
      countryCode = countryMatch[0].iso2.toLowerCase();
    }

    try {
      const orsResults = await this.searchWithORS(searchQuery, 'city', countryCode);

      return orsResults;
    } catch (error) {
      Logger.error('Error searching cities:', error);
      return [];
    }
  }

  async validateLocation(
    name: string,
    type: 'country' | 'city',
  ): Promise<LocationSuggestion | null> {
    if (!name || !type || name.length < 1) return null;

    if (type === 'country') {
      const country = this.getCountry(name);
      if (country.length > 0) {
        return {
          name: country[0].name,
          type: 'country',
        };
      }
      return null;
    }

    const cities = await this.searchCities(name);
    return (
      cities.find(
        (suggestion) => suggestion.name.toLowerCase() === name.toLowerCase(),
      ) || null
    );
  }

  /**
   * Get country regions data from the JSON file
   * @returns CountryRegionsResponse object containing regions data
   */
  async getCountryRegions(): Promise<CountryRegionsResponse> {
    try {
      const filePath = path.join(
        __dirname,
        'locations',
        'data',
        'country-regions.json',
      );
      const fileContent = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(fileContent) as CountryRegionsResponse;
    } catch (error) {
      Logger.error('Error reading country regions file:', error);
      return { regions: [] };
    }
  }
}
