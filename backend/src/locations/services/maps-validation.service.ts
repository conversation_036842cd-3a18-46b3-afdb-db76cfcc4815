/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import {
  LocationContext,
  LocationCoordinates,
} from '../interfaces/location-coordinates.interface';
import { LocationsService } from '../locations.service';
import { OpenCageService } from '../providers/opencage.provider';
import { GeocodeService } from '../providers/geocode.provider';
import { GoogleMapsService } from '../providers/google-maps.provider';
import { NominatimService } from '../providers/nominatim.provider';
import { DistanceCalculatorHelper } from '../helpers/distance-calculator.helper';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Location, LocationDocument } from '../location.schema';
import { ConfigService } from '../../config/config.service';

@Injectable()
export class MapsValidationService {
  private readonly logger = new Logger(MapsValidationService.name);

  constructor(
    private readonly openCageService: OpenCageService,
    private readonly geocodeService: GeocodeService,
    private readonly googleMapsService: GoogleMapsService,
    private readonly nominatimService: NominatimService,
    private readonly locationsService: LocationsService,
    @InjectModel(Location.name) private locationModel: Model<LocationDocument>,
    private readonly configService: ConfigService,
  ) { }

  /**
   * Validate a location with additional context information to resolve naming conflicts
   * Also validates that the coordinates are within reasonable proximity to the specified city
   * @param location Location name to validate
   * @param context Additional context information (city, country, countryCode)
   * @returns Location details with coordinates or null if not found
   */
  async validateLocation(
    location: string,
    context: LocationContext,
  ): Promise<LocationCoordinates | null> {
    this.logger.log(
      `Validating location "${location}" with context: city="${context.city}", country="${context.country}"`,
    );

    // Try Geocode Earth with context as primary service
    const geocodeResult = await this.geocodeService.geocode(location, context);
    if (geocodeResult) {
      const isProximityValid = await this.validateProximity(geocodeResult, context);
      if (isProximityValid) {
        this.logger.log(
          `Location validated with Geocode Earth using context: ${location}`,
        );
        return geocodeResult;
      } else {
        this.logger.warn(
          `Geocode Earth result for ${location} failed proximity validation - too far from ${context.city}`,
        );
      }
    }

    // Try OpenCage with context first (secondary service)
    const openCageResult = await this.openCageService.geocode(
      location,
      context,
    );
    if (openCageResult) {
      const isProximityValid = await this.validateProximity(openCageResult, context);
      if (isProximityValid) {
        this.logger.log(
          `Location validated with OpenCage using context: ${location}`,
        );
        return openCageResult;
      } else {
        this.logger.warn(
          `OpenCage result for ${location} failed proximity validation - too far from ${context.city}`,
        );
      }
    }

    // Try Nominatim with context as last resort
    const nominatimResult = await this.nominatimService.geocode(location);
    if (nominatimResult) {
      const isProximityValid = await this.validateProximity(nominatimResult, context);
      if (isProximityValid) {
        this.logger.log(
          `Location validated with Nominatim using context: ${location}`,
        );
        return nominatimResult;
      } else {
        this.logger.warn(
          `Nominatim result for ${location} failed proximity validation - too far from ${context.city}`,
        );
      }
    }

    // Try ORS service with context
    try {
      // If we have a country, use it to filter ORS results
      const orsResults = await this.locationsService.searchWithORS(
        location,
        'city',
      );

      if (orsResults.length > 0) {
        // If we have country context, filter results by country
        let matchingResult = orsResults[0];

        if (context?.country) {
          const countryName = context.country;
          const countryMatch = orsResults.find(
            (result) =>
              result.country &&
              result.country.toLowerCase() === countryName.toLowerCase(),
          );

          if (countryMatch) {
            matchingResult = countryMatch;
          }
        }

        if (matchingResult.coordinates) {
          const orsLocationResult: LocationCoordinates = {
            name: location, // Use the original location name for precise validation
            formatted_address: `${matchingResult.name}, ${matchingResult.country}`,
            coordinates: matchingResult.coordinates,
            place_id: '',
            types: ['locality'],
            source: 'ors',
            confidence: 'medium',
          };

          const isProximityValid = await this.validateProximity(orsLocationResult, context);
          if (isProximityValid) {
            this.logger.log(
              `Location validated with ORS using context: ${location}`,
            );
            return orsLocationResult;
          } else {
            this.logger.warn(
              `ORS result for ${location} failed proximity validation - too far from ${context.city}`,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Error validating with ORS using context: ${error.message}`,
      );
    }

    // Try Google Maps with context as fallback
    const googleMapsResult = await this.googleMapsService.geocode(
      location,
      context,
    );
    if (googleMapsResult) {
      const isProximityValid = await this.validateProximity(googleMapsResult, context);
      if (isProximityValid) {
        this.logger.log(
          `Location validated with Google Maps using context: ${location}`,
        );
        return googleMapsResult;
      } else {
        this.logger.warn(
          `Google Maps result for ${location} failed proximity validation - too far from ${context.city}`,
        );
      }
    }

    this.logger.warn(`Could not validate location with context: ${location}`);
    return null;
  }

  /**
   * Validate that the coordinates are within reasonable proximity to the specified city
   * @param locationResult The location result with coordinates to validate
   * @param context The location context containing city information
   * @returns True if coordinates are within acceptable proximity, false otherwise
   */
  private async validateProximity(
    locationResult: LocationCoordinates,
    context: LocationContext,
  ): Promise<boolean> {
    // Skip proximity validation if no city context is provided
    if (!context.city || context.city === "Unknown") {
      this.logger.debug(
        `No valid city context provided (city: ${context.city}), skipping proximity validation for ${locationResult.name}`,
      );
      return true;
    }

    try {
      this.logger.debug(
        `Validating proximity for "${locationResult.name}" against city: ${context.city}, country: ${context.country}`,
      );

      // Get city coordinates from database first, then try geocoding providers if not found
      let cityCoordinates = await this.getCityCoordinates(context.city, context.country);

      if (!cityCoordinates) {
        this.logger.debug(
          `City ${context.city} not found in database, attempting to geocode city coordinates`,
        );
        cityCoordinates = await this.geocodeCityCoordinates(context.city, context.country);

        if (cityCoordinates) {
          this.logger.debug(
            `Successfully geocoded city coordinates for ${context.city}: ${cityCoordinates.lat}, ${cityCoordinates.lng}`,
          );
        }
      } else {
        this.logger.debug(
          `Found city coordinates in database for ${context.city}: ${cityCoordinates.lat}, ${cityCoordinates.lng}`,
        );
      }

      if (!cityCoordinates) {
        this.logger.warn(
          `Could not find coordinates for city ${context.city} in country ${context.country}, skipping proximity validation for ${locationResult.name}`,
        );
        return true; // Allow validation to pass if we can't find city coordinates
      }

      // Calculate maximum allowed distance for this city
      const maxDistance = DistanceCalculatorHelper.getMaxDistanceForCity(this.configService, context.city);

      // Check if the location is within the allowed radius
      const isWithinRadius = DistanceCalculatorHelper.isWithinRadius(
        locationResult.coordinates.lat,
        locationResult.coordinates.lng,
        cityCoordinates.lat,
        cityCoordinates.lng,
        maxDistance,
      );

      const actualDistance = DistanceCalculatorHelper.calculateDistance(
        locationResult.coordinates.lat,
        locationResult.coordinates.lng,
        cityCoordinates.lat,
        cityCoordinates.lng,
      );

      if (!isWithinRadius) {
        this.logger.warn(
          `Location "${locationResult.name}" is ${actualDistance.toFixed(2)}km away from ${context.city}, ${context.country}, ` +
          `which exceeds the maximum allowed distance of ${maxDistance}km`,
        );
      } else {
        this.logger.debug(
          `Location "${locationResult.name}" is ${actualDistance.toFixed(2)}km away from ${context.city}, ${context.country}, ` +
          `which is within the allowed distance of ${maxDistance}km`,
        );
      }

      return isWithinRadius;
    } catch (error) {
      this.logger.error(
        `Error during proximity validation: ${error.message}`,
      );
      return true; // Allow validation to pass on error to avoid blocking legitimate results
    }
  }

  /**
   * Get coordinates for a city from the database
   * @param cityName Name of the city
   * @param countryName Optional country name for disambiguation
   * @returns City coordinates or null if not found
   */
  private async getCityCoordinates(
    cityName: string,
    countryName?: string,
  ): Promise<{ lat: number; lng: number } | null> {
    try {
      const query: any = {
        name: new RegExp(`^${cityName}$`, 'i'),
        type: 'city'
      };

      if (countryName) {
        query.country = new RegExp(`^${countryName}$`, 'i');
      }

      const city = await this.locationModel.findOne(query).exec();

      if (city && city.coordinates) {
        return {
          lat: city.coordinates.lat,
          lng: city.coordinates.lng,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Error retrieving city coordinates for ${cityName}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Save city coordinates to the location collection for caching
   * @param cityName Name of the city
   * @param countryName Country name for the city
   * @param coordinates Coordinates to save
   * @returns Promise<void>
   */
  private async saveCityCoordinates(
    cityName: string,
    countryName: string,
    coordinates: { lat: number; lng: number },
  ): Promise<void> {
    try {
      const locationData = {
        name: cityName,
        country: countryName || 'Unknown',
        type: 'city',
        coordinates: coordinates,
      };

      // Use upsert to avoid duplicates
      await this.locationModel.findOneAndUpdate(
        {
          name: new RegExp(`^${cityName}$`, 'i'),
          country: new RegExp(`^${countryName || 'Unknown'}$`, 'i'),
          type: 'city'
        },
        locationData,
        { upsert: true, new: true }
      ).exec();

      this.logger.debug(
        `Successfully cached city coordinates for ${cityName}, ${countryName}: ${coordinates.lat}, ${coordinates.lng}`,
      );
    } catch (error) {
      this.logger.error(
        `Error saving city coordinates for ${cityName}: ${error.message}`,
      );
      // Don't throw error to avoid breaking the geocoding flow
    }
  }

  /**
   * Get city coordinates using geocoding providers when not found in database
   * @param cityName Name of the city
   * @param countryName Optional country name for disambiguation
   * @returns City coordinates or null if not found
   */
  private async geocodeCityCoordinates(
    cityName: string,
    countryName?: string,
  ): Promise<{ lat: number; lng: number } | null> {
    try {
      const context: LocationContext = {
        city: cityName,
        country: countryName,
      };

      // Try geocoding the city name using our providers
      this.logger.debug(`Attempting to geocode city: ${cityName}, ${countryName}`);

      let coordinates: { lat: number; lng: number } | null = null;

      // Try Geocode Earth first
      const geocodeResult = await this.geocodeService.geocode(cityName, context);
      if (geocodeResult && geocodeResult.coordinates) {
        this.logger.debug(`Found city coordinates via Geocode Earth: ${cityName}`);
        coordinates = geocodeResult.coordinates;
      }

      // Try OpenCage if not found
      if (!coordinates) {
        const openCageResult = await this.openCageService.geocode(cityName, context);
        if (openCageResult && openCageResult.coordinates) {
          this.logger.debug(`Found city coordinates via OpenCage: ${cityName}`);
          coordinates = openCageResult.coordinates;
        }
      }

      // Try Google Maps if not found
      if (!coordinates) {
        const googleMapsResult = await this.googleMapsService.geocode(cityName, context);
        if (googleMapsResult && googleMapsResult.coordinates) {
          this.logger.debug(`Found city coordinates via Google Maps: ${cityName}`);
          coordinates = googleMapsResult.coordinates;
        }
      }

      // Try Nominatim if not found
      if (!coordinates) {
        const nominatimResult = await this.nominatimService.geocode(cityName);
        if (nominatimResult && nominatimResult.coordinates) {
          this.logger.debug(`Found city coordinates via Nominatim: ${cityName}`);
          coordinates = nominatimResult.coordinates;
        }
      }

      // If coordinates were found, save them to the database for future use
      if (coordinates && countryName) {
        await this.saveCityCoordinates(cityName, countryName, coordinates);
      }

      if (!coordinates) {
        this.logger.debug(`Could not geocode city coordinates for: ${cityName}, ${countryName}`);
      }

      return coordinates;
    } catch (error) {
      this.logger.error(
        `Error geocoding city coordinates for ${cityName}: ${error.message}`,
      );
      return null;
    }
  }
}
