/* eslint-disable */

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Trip } from '../../trips/schemas/trip.schema';
import { MapRegionDto } from '../dto/map-region.dto';
import { Location } from '../location.schema';
import { LocationsService } from '../locations.service';

@Injectable()
export class MapRegionsService {
  constructor(
    @InjectModel(Location.name) private locationModel: Model<Location>,
    @InjectModel(Trip.name) private tripModel: Model<Trip>,
    private readonly locationsService: LocationsService,
  ) { }

  /**
   * Get map region for a country
   * @param name Country name
   * @returns Map region data
   */
  async getCountryMapRegion(name: string): Promise<MapRegionDto> {
    const country = await this.locationsService.searchWithORS(name, 'country');

    if (!country || country.length === 0) {
      throw new NotFoundException(`Country ${name} not found`);
    }

    const location = country[0];

    // Get country region data from the country-regions.json file
    const countryRegions = await this.locationsService.getCountryRegions();
    const countryRegion = countryRegions.regions.find(
      (region) => region.name.toLowerCase() === location.name.toLowerCase(),
    );

    return {
      region: {
        latitude: location.coordinates?.lat ?? 0,
        longitude: location.coordinates?.lng ?? 0,
        latitudeDelta: countryRegion?.region.latitudeDelta || 10,
        longitudeDelta: countryRegion?.region.longitudeDelta || 10,
      },
      country: location.name,
    };
  }

  /**
   * Get map region for a city
   * @param name City name
   * @param country Optional country name for disambiguation
   * @returns Map region data
   */
  async getCityMapRegion(
    name: string,
    country?: string,
  ): Promise<MapRegionDto> {
    const query: any = { name: new RegExp(`^${name}$`, 'i'), type: 'city' };

    if (country) {
      query.country = new RegExp(`^${country}$`, 'i');
    }

    const city = await this.locationModel.findOne(query).exec();

    if (!city) {
      throw new NotFoundException(`City ${name} not found`);
    }

    return {
      region: {
        latitude: city.coordinates?.lat ?? 0,
        longitude: city.coordinates?.lng ?? 0,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
      },
      city: city.name,
      country: city.country,
    };
  }

  /**
   * Get map region for a trip
   * @param id Trip ID
   * @returns Map region data
   */
  async getTripMapRegion(id: string): Promise<MapRegionDto> {
    const trip = await this.tripModel.findById(id).exec();

    if (!trip) {
      throw new NotFoundException(`Trip ${id} not found`);
    }

    // Get the country for the trip's destination
    const destination = trip.tripDetails?.destination || '';
    const country = await this.locationsService.searchInDb(
      destination,
      'country',
    );

    if (!country || country.length === 0) {
      throw new NotFoundException(`Country ${destination} not found`);
    }

    const location = country[0];

    // Get country region data from the country-regions.json file
    const countryRegions = await this.locationsService.getCountryRegions();
    const countryRegion = countryRegions.regions.find(
      (region) => region.name.toLowerCase() === location.name.toLowerCase(),
    );

    return {
      region: {
        latitude: location.coordinates?.lat ?? 0,
        longitude: location.coordinates?.lng ?? 0,
        latitudeDelta: countryRegion?.region.latitudeDelta || 10,
        longitudeDelta: countryRegion?.region.longitudeDelta || 10,
      },
      tripId: id,
    };
  }

  /**
   * Get map region for a specific day of a trip
   * @param id Trip ID
   * @param day Day number
   * @returns Map region data
   */
  async getDailyMapRegion(id: string, day: number): Promise<MapRegionDto> {
    const trip = await this.tripModel.findById(id).exec();

    if (!trip) {
      throw new NotFoundException(`Trip ${id} not found`);
    }

    if (!trip.itinerary || !trip.itinerary[day - 1]) {
      throw new NotFoundException(`Day ${day} not found in trip ${id}`);
    }

    const dailyItinerary = trip.itinerary[day - 1];

    // Calculate the center of all activities for the day
    const activities = dailyItinerary.activities.filter(
      (activity) => activity.coordinates,
    );

    if (activities.length === 0) {
      // If no activities with coordinates, fall back to the trip's destination
      return this.getTripMapRegion(id);
    }

    // Calculate the center and bounds of all activities
    const validCoordinates = activities
      .map((activity) => activity.coordinates)
      .filter(
        (coords): coords is { lat: number; lng: number } =>
          coords !== undefined &&
          typeof coords.lat === 'number' &&
          typeof coords.lng === 'number',
      );

    if (validCoordinates.length === 0) {
      return this.getTripMapRegion(id);
    }

    // Handle single activity case
    if (validCoordinates.length === 1) {
      const coord = validCoordinates[0];
      return {
        region: {
          latitude: coord.lat,
          longitude: coord.lng,
          latitudeDelta: 0.01, // Tight zoom for single activity
          longitudeDelta: 0.01,
        },
        tripId: id,
        dayNumber: day,
      };
    }

    const bounds = this.calculateBounds(validCoordinates);

    // Calculate the span of coordinates
    const latSpan = bounds.maxLat - bounds.minLat;
    const lngSpan = bounds.maxLng - bounds.minLng;

    // Use minimal padding since client-side fitBounds will handle the perfect fit
    const paddingMultiplier = 1.1; // Minimal padding for initial region

    // Ensure minimum span for very close activities
    const minSpan = 0.002; // ~200 meters minimum span
    const deltaLat = Math.max(latSpan * paddingMultiplier, minSpan);
    const deltaLng = Math.max(lngSpan * paddingMultiplier, minSpan);

    return {
      region: {
        latitude: (bounds.minLat + bounds.maxLat) / 2,
        longitude: (bounds.minLng + bounds.maxLng) / 2,
        latitudeDelta: deltaLat,
        longitudeDelta: deltaLng,
      },
      tripId: id,
      dayNumber: day,
    };
  }

  /**
   * Calculate bounds for a set of coordinates
   * @param coordinates Array of coordinates
   * @returns Bounds object with min/max lat/lng
   */
  private calculateBounds(coordinates: { lat: number; lng: number }[]) {
    if (coordinates.length === 0) {
      return { minLat: 0, maxLat: 0, minLng: 0, maxLng: 0 };
    }

    let minLat = coordinates[0].lat;
    let maxLat = coordinates[0].lat;
    let minLng = coordinates[0].lng;
    let maxLng = coordinates[0].lng;

    coordinates.forEach((coord) => {
      minLat = Math.min(minLat, coord.lat);
      maxLat = Math.max(maxLat, coord.lat);
      minLng = Math.min(minLng, coord.lng);
      maxLng = Math.max(maxLng, coord.lng);
    });

    return { minLat, maxLat, minLng, maxLng };
  }
}
