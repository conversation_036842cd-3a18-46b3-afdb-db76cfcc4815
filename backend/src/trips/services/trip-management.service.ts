/* eslint-disable */

import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateTripDto } from '../dto/create-trip.dto';
import { UpdateTripDto } from '../dto/update-trip.dto';
import { Trip } from '../schemas/trip.schema';

@Injectable()
export class TripManagementService {
  constructor(@InjectModel(Trip.name) private tripModel: Model<Trip>) { }

  async create(createTripDto: CreateTripDto): Promise<Trip> {
    const createdTrip = new this.tripModel(createTripDto);
    return createdTrip.save();
  }

  async findAll(
    userId: string,
    filters: {
      includeArchived?: boolean;
      search?: string;
      favorite?: boolean;
      status?: string;
      includeExamples?: boolean;
    } = {},
  ): Promise<Trip[]> {
    // Destructure filters with defaults
    const {
      includeArchived = false,
      search = '',
      favorite = false,
      status = '',
      includeExamples = false,
    } = filters;

    // Build queries for user trips and example trips
    const userQuery: any = { userId };
    const exampleQuery: any = { isExample: true };

    // Handle archived filter for user trips
    if (!includeArchived) {
      userQuery.isArchived = { $ne: true };
    }

    // Handle favorite filter for user trips
    if (favorite) {
      userQuery.isFavorite = true;
    }

    // Handle status filter for both user and example trips
    if (status) {
      userQuery.status = status;
      exampleQuery.status = status;
    }

    // Handle search filter for both user and example trips
    if (search) {
      const searchConditions = [
        { name: { $regex: search, $options: 'i' } },
        { 'tripDetails.destination': { $regex: search, $options: 'i' } },
        { 'tripDetails.arrivalCity': { $regex: search, $options: 'i' } },
        { 'tripDetails.departureCity': { $regex: search, $options: 'i' } },
      ];
      userQuery.$or = searchConditions;
      exampleQuery.$or = searchConditions;
    }

    // Fetch user trips
    let userTrips = await this.tripModel.find(userQuery).sort({ createdAt: -1 }).exec();

    // Fetch example trips if requested
    let exampleTrips: Trip[] = [];
    if (includeExamples) {
      exampleTrips = await this.tripModel.find(exampleQuery).sort({ createdAt: -1 }).exec();
    }

    // Combine trips and deduplicate by ID
    const tripMap = new Map<string, Trip>();

    // Add user trips first (they take priority)
    userTrips.forEach(trip => {
      const tripId = (trip._id as any)?.toString() || trip.id?.toString();
      if (tripId) {
        tripMap.set(tripId, trip);
      }
    });

    // Add example trips only if they're not already in the map
    exampleTrips.forEach(trip => {
      const tripId = (trip._id as any)?.toString() || trip.id?.toString();
      if (tripId && !tripMap.has(tripId)) {
        tripMap.set(tripId, trip);
      }
    });

    let allTrips = Array.from(tripMap.values());

    // Sort the itinerary by day for all trips
    allTrips = allTrips.map((trip) => {
      if (trip.itinerary && Array.isArray(trip.itinerary)) {
        trip.itinerary.sort((a, b) => a.day - b.day);
      }
      return trip;
    });

    return allTrips;
  }

  async findOne(id: string): Promise<Trip> {
    const trip = await this.tripModel.findById(id).exec();
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${id} not found`);
    }

    // Sort the itinerary by day
    if (trip.itinerary && Array.isArray(trip.itinerary)) {
      trip.itinerary.sort((a, b) => a.day - b.day);
    }
    return trip;
  }

  async update(id: string, updateTripDto: UpdateTripDto): Promise<Trip> {
    const updatedTrip = await this.tripModel
      .findByIdAndUpdate(id, updateTripDto, { new: true })
      .exec();

    if (!updatedTrip) {
      throw new NotFoundException(`Trip with ID ${id} not found`);
    }

    return updatedTrip;
  }

  async updateActivityCompletion(
    tripId: string,
    activityId: string,
    completed: boolean,
  ): Promise<Trip> {
    // Use MongoDB's $set operator with array filters to update the specific activity
    const updatedTrip = await this.tripModel
      .findOneAndUpdate(
        {
          _id: tripId,
          'itinerary.activities.id': activityId,
        },
        {
          $set: {
            'itinerary.$[day].activities.$[activity].completed': completed,
          },
        },
        {
          arrayFilters: [
            { 'day.activities.id': activityId },
            { 'activity.id': activityId },
          ],
          new: true,
        },
      )
      .exec();

    if (!updatedTrip) {
      // Check if trip exists but activity doesn't
      const tripExists = await this.tripModel.findById(tripId).exec();
      if (!tripExists) {
        throw new NotFoundException(`Trip with ID ${tripId} not found`);
      }
      throw new NotFoundException(
        `Activity with ID ${activityId} not found in trip ${tripId}`,
      );
    }

    return updatedTrip;
  }

  async remove(id: string): Promise<Trip> {
    const deletedTrip = await this.tripModel.findById(id).exec();

    if (!deletedTrip) {
      throw new NotFoundException(`Trip with ID ${id} not found`);
    }

    // Check if there are any trips containing some days being generating at the moment
    const daysBeingGenerated = deletedTrip.daysProgress.filter(
      (day) => day.is_generating && day.day !== deletedTrip.currentDayProgress,
    );

    if (daysBeingGenerated.length > 0) {
      throw new BadRequestException(
        'This trip is being generated at the moment',
      );
    }

    await this.tripModel.findByIdAndDelete(id).exec();

    return deletedTrip;
  }

  async toggleFavorite(id: string): Promise<Trip> {
    const trip = await this.tripModel.findById(id).exec();
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${id} not found`);
    }

    trip.isFavorite = !trip.isFavorite;
    return trip.save();
  }

  async archiveTrip(id: string): Promise<Trip> {
    const trip = await this.tripModel.findById(id).exec();
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${id} not found`);
    }

    // Check if there are any trips containing some days being generating at the moment
    const daysBeingGenerated = trip.daysProgress.filter(
      (day) => day.is_generating && day.day !== trip.currentDayProgress,
    );

    if (daysBeingGenerated.length > 0) {
      throw new BadRequestException(
        'This trip is being generated at the moment',
      );
    }

    trip.isArchived = true;
    return trip.save();
  }
}
