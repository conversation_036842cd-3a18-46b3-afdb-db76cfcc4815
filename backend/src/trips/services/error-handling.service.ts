/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { RetryErrorType } from '../../common/retry.service';
import { InsufficientDaysException } from '../../days-balance/day-usage.service';
import { NotificationType } from '../../gateways/stream.gateway';
import { GenerateTripError } from '../helpers/error.format';
import { Trip } from '../schemas/trip.schema';
import { ItineraryProgressService } from './itinerary-progress.service';
import { StreamingService } from './streaming.service';

@Injectable()
export class ErrorHandlingService {
  private readonly logger = new Logger(ErrorHandlingService.name);

  constructor(
    private readonly streamingService: StreamingService,
    private readonly itineraryProgressService: ItineraryProgressService,
  ) { }

  /**
   * Handle high-level plan generation error
   * @param error Error object
   * @param userId User ID (optional)
   * @param destination Trip destination (optional)
   * @throws BadRequestException
   */
  handleHighLevelPlanError(error: any, userId?: string, destination?: string): never {
    this.logger.error(`Failed to generate high-level plan: ${error}`);

    // Send socket notification if user context is available
    if (userId) {
      // Send error to "new-trip" room since mobile app subscribes to this room during initial generation
      this.streamingService.sendTripGenerationError(
        'new-trip', // Mobile app subscribes to this room when no tripId exists yet
        0, // Day 0 indicates high-level plan error
        userId,
        destination || 'Unknown destination',
        new Date(),
        {
          errorType: 'high_level_plan',
          originalError: error.message,
          destination: destination || 'Unknown destination'
        }
      );
    }

    throw new BadRequestException(
      GenerateTripError.FAILED_TO_GENERATE_HIGH_LEVEL_PLAN,
    );
  }

  /**
   * Check if an error is related to high-level plan generation and should be retried
   * @param error Error object
   * @returns True if the error should be retried, false otherwise
   */
  isHighLevelPlanErrorRetryable(error: any): boolean {
    // Don't retry insufficient days errors
    if (error instanceof InsufficientDaysException) {
      return false;
    }

    // Check for specific error messages that indicate retryable errors
    if (error.message) {
      const errorMsg = error.message.toLowerCase();

      // Retry parsing and validation errors for high-level plan
      if (
        errorMsg.includes('failed to parse high level plan') ||
        errorMsg.includes('failed to generate high level plan') ||
        errorMsg.includes('incorrect high level plan length') ||
        errorMsg.includes('failed to parse') ||
        errorMsg.includes('validation') ||
        errorMsg.includes('invalid format') ||
        errorMsg.includes('syntax error')
      ) {
        return true;
      }

      // Retry network, timeout, rate limit, or server errors
      if (
        errorMsg.includes('network') ||
        errorMsg.includes('timeout') ||
        errorMsg.includes('rate limit') ||
        errorMsg.includes('too many requests') ||
        errorMsg.includes('server error') ||
        errorMsg.includes('internal error') ||
        errorMsg.includes('connection')
      ) {
        return true;
      }
    }

    // Check for status codes
    if (error.status) {
      // Retry server errors (5xx)
      if (error.status >= 500 && error.status < 600) {
        return true;
      }

      // Retry rate limit errors (429)
      if (error.status === 429) {
        return true;
      }

      // Don't retry client errors (4xx) except for rate limit
      if (error.status >= 400 && error.status < 500 && error.status !== 429) {
        // Check if it's a parsing or validation error (which we want to retry)
        if (error.message) {
          const errorMsg = error.message.toLowerCase();
          if (
            errorMsg.includes('parse') ||
            errorMsg.includes('validation') ||
            errorMsg.includes('invalid format') ||
            errorMsg.includes('syntax error')
          ) {
            return true;
          }
        }
        return false;
      }
    }

    // By default, retry unknown errors
    return true;
  }

  /**
   * Get the retry error type for an error
   * @param error Error object
   * @returns Retry error type
   */
  getRetryErrorType(error: any): RetryErrorType {
    // Network errors
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ENOTFOUND' ||
      error.message?.includes('network') ||
      error.message?.includes('connection')
    ) {
      return RetryErrorType.NETWORK;
    }

    // Rate limit errors
    if (
      error.status === 429 ||
      error.statusCode === 429 ||
      error.message?.includes('rate limit') ||
      error.message?.includes('too many requests')
    ) {
      return RetryErrorType.RATE_LIMIT;
    }

    // Authentication errors
    if (
      error.status === 401 ||
      error.statusCode === 401 ||
      error.status === 403 ||
      error.statusCode === 403 ||
      error.message?.includes('authentication') ||
      error.message?.includes('unauthorized') ||
      error.message?.includes('forbidden')
    ) {
      return RetryErrorType.AUTHENTICATION;
    }

    // Server errors
    if (
      (error.status >= 500 && error.status < 600) ||
      (error.statusCode >= 500 && error.statusCode < 600) ||
      error.message?.includes('server error')
    ) {
      return RetryErrorType.SERVER;
    }

    // Timeout errors
    if (
      error.code === 'ETIMEDOUT' ||
      error.message?.includes('timeout') ||
      error.message?.includes('timed out')
    ) {
      return RetryErrorType.TIMEOUT;
    }

    // Default to unknown
    return RetryErrorType.UNKNOWN;
  }

  /**
   * Handle single day itinerary generation error
   * @param error Error object
   * @param trip Trip object
   * @param day Day number
   * @throws BadRequestException
   */
  async handleSingleDayGenerationError(
    error: any,
    trip: Trip,
    day: number,
  ): Promise<never> {
    this.logger.warn(
      `Error generating single day itinerary for day ${day}: ${error}`,
    );
    await this.itineraryProgressService.updateDayProgress(
      trip,
      day,
      false,
      GenerateTripError.FAILED_TO_GENERATE_SINGLE_DAY_ITINERARY,
    );
    throw new BadRequestException(
      GenerateTripError.FAILED_TO_GENERATE_SINGLE_DAY_ITINERARY,
    );
  }

  /**
   * Handle parsing error
   * @param error Error object
   * @param trip Trip object
   * @param day Day number
   * @throws BadRequestException
   */
  async handleParsingError(
    error: any,
    trip: Trip,
    day: number,
  ): Promise<never> {
    this.logger.error(`Failed to parse response: ${error}`);
    await this.itineraryProgressService.updateDayProgress(
      trip,
      day,
      false,
      GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY,
    );
    throw new BadRequestException(
      GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY,
    );
  }

  /**
   * Send error notification for trip generation
   * @param tripId Trip ID
   * @param day Day number
   * @param userId User ID
   * @param destination Destination
   * @param startTime Start time
   */
  sendTripGenerationError(
    tripId: string,
    day: number,
    userId: string,
    destination: string,
    startTime: Date = new Date(),
  ): void {
    this.streamingService.sendTripGenerationError(
      tripId,
      day,
      userId,
      destination,
      startTime,
    );
  }

  /**
   * Handle insufficient balance error
   * @param error Error object
   * @param userId User ID
   * @param tripId Trip ID (optional)
   * @param currentBalance Current balance (optional)
   * @param requiredTokens Required credits (optional)
   * @throws InsufficientTokenBalanceException
   */
  handleInsufficientBalanceError(
    error: any,
    userId: string,
    tripId?: string,
    currentBalance?: number,
    requiredTokens?: number,
  ): never {
    this.logger.warn(
      `Insufficient token balance for user ${userId}: ${error.message}`,
    );

    // Extract balance information from error message if not provided
    let extractedBalance = currentBalance;
    let extractedRequired = requiredTokens;

    if (error instanceof InsufficientDaysException && error.message) {
      // Try to extract days information from the error message
      const availableMatch = error.message.match(/Available:\s*(\d+)/i);
      const requiredMatch = error.message.match(/Required:\s*(\d+)/i);

      if (availableMatch && !extractedBalance) {
        extractedBalance = parseInt(availableMatch[1], 10);
      }
      if (requiredMatch && !extractedRequired) {
        extractedRequired = parseInt(requiredMatch[1], 10);
      }

      this.logger.log(
        `Extracted days info from error: Available=${extractedBalance}, Required=${extractedRequired}`,
      );

      this.streamingService.sendInsufficientBalanceNotification(
        userId,
        tripId,
        extractedBalance,
        extractedRequired,
      );
    }

    throw error;
  }
}
