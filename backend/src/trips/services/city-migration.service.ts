/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { TripManagementService } from './trip-management.service';

@Injectable()
export class CityMigrationService {
  private readonly logger = new Logger(CityMigrationService.name);

  constructor(
    private readonly tripManagementService: TripManagementService,
  ) { }

  /**
   * Migrate existing trips to ensure all activities have city information
   * This removes the city field from DailyItinerary and ensures all activities have cities
   */
  async migrateTripsToActivityCities(): Promise<void> {
    this.logger.log('Starting migration to move cities from days to activities');

    try {
      // Get all trips - we need to get all trips regardless of user
      // This is a migration operation, so we'll query the database directly
      const trips = await this.tripManagementService['tripModel'].find({}).exec();
      let migratedCount = 0;
      let errorCount = 0;

      for (const trip of trips) {
        try {
          let tripModified = false;

          // Process each day in the itinerary
          for (const day of trip.itinerary) {
            const dayCity = (day as any).city; // Cast to access the old city field

            if (dayCity) {
              // Extract cities from day-level city string
              const dayCities = this.extractCitiesFromString(dayCity);

              // Update activities that don't have cities
              for (const activity of day.activities) {
                if (!activity.city) {
                  // For activities, use the first city or the only city
                  activity.city = dayCities[0] || dayCity;
                  tripModified = true;
                }
              }

              // Remove the city field from the day (this will be handled by schema change)
              delete (day as any).city;
              tripModified = true;
            } else {
              // If day doesn't have a city, try to infer from activities
              const activitiesWithCities = day.activities.filter(a => a.city);
              if (activitiesWithCities.length > 0) {
                const mostCommonCity = this.getMostCommonCity(activitiesWithCities.map(a => a.city));

                // Assign the most common city to activities without cities
                for (const activity of day.activities) {
                  if (!activity.city) {
                    activity.city = mostCommonCity;
                    tripModified = true;
                  }
                }
              } else {
                // Fallback: use trip destination
                const fallbackCity = trip.tripDetails?.destination || 'Unknown';
                for (const activity of day.activities) {
                  if (!activity.city) {
                    activity.city = fallbackCity;
                    tripModified = true;
                  }
                }
              }
            }
          }

          // Save the trip if it was modified
          if (tripModified) {
            await trip.save();
            migratedCount++;
            this.logger.debug(`Migrated trip: ${trip._id}`);
          }
        } catch (error) {
          this.logger.error(`Error migrating trip ${trip._id}:`, error);
          errorCount++;
        }
      }

      this.logger.log(`Migration completed. Migrated: ${migratedCount}, Errors: ${errorCount}`);
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Extract city names from a string that might contain multiple cities
   */
  private extractCitiesFromString(cityString: string): string[] {
    if (!cityString) return [];

    // Check for "to" pattern (e.g., "Paris to London")
    if (cityString.toLowerCase().includes(' to ')) {
      return cityString.split(' to ').map((city) => city.trim());
    }

    // Check for " - " pattern (e.g., "Paris - London")
    if (cityString.toLowerCase().includes(' - ')) {
      return cityString.split(' - ').map((city) => city.trim());
    }

    // Check for " , " pattern (e.g., "Paris , London")
    if (cityString.toLowerCase().includes(' , ')) {
      return cityString.split(' , ').map((city) => city.trim());
    }

    // Check for "/" pattern (e.g., "Paris / London")
    if (cityString.includes(' / ')) {
      return cityString.split(' / ').map((city) => city.trim());
    }

    // Return as a single city if no special patterns
    return [cityString.trim()];
  }

  /**
   * Get the most common city from an array of cities
   */
  private getMostCommonCity(cities: string[]): string {
    const cityCount = cities.reduce((acc, city) => {
      acc[city] = (acc[city] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.keys(cityCount).reduce((a, b) =>
      cityCount[a] > cityCount[b] ? a : b
    );
  }

  /**
   * Validate that all activities in all trips have cities
   */
  async validateMigration(): Promise<{ valid: boolean; issues: string[] }> {
    this.logger.log('Validating migration results');

    const issues: string[] = [];
    const trips = await this.tripManagementService['tripModel'].find({}).exec();

    for (const trip of trips) {
      for (const day of trip.itinerary) {
        for (const activity of day.activities) {
          if (!activity.city) {
            issues.push(`Trip ${trip._id}, Day ${day.day}, Activity ${activity.id}: Missing city`);
          }
        }
      }
    }

    const valid = issues.length === 0;
    this.logger.log(`Validation ${valid ? 'passed' : 'failed'}. Issues found: ${issues.length}`);

    return { valid, issues };
  }
}
