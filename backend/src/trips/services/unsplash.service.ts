import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import { CountryImageCacheService } from './country-image-cache.service';


interface UnsplashImage {
  id: string;
  urls: {
    raw: string;
    full: string;
    regular: string;
    small: string;
    thumb: string;
  };
  alt_description: string | null;
  description: string | null;
  user: {
    name: string;
    username: string;
  };
}

interface UnsplashSearchResponse {
  total: number;
  total_pages: number;
  results: UnsplashImage[];
}

@Injectable()
export class UnsplashService {
  private readonly logger = new Logger(UnsplashService.name);
  private apiKey: string;
  private readonly apiUrl = 'https://api.unsplash.com';

  constructor(
    private readonly configService: ConfigService,
    private readonly countryImageCacheService: CountryImageCacheService,
  ) {
    this.apiKey = this.configService.getUnsplashConfig().apiKey;
  }

  /**
   * Get a destination image URL for a trip
   * @param destination The destination string (e.g., "France", "Paris, France")
   * @param arrivalCity Optional arrival city
   * @param width Desired image width (default: 800)
   * @param height Desired image height (default: 600)
   * @returns Image URL or null if not found
   */
  async getDestinationImageUrl(
    destination: string,
    _arrivalCity?: string,
    width: number = 800,
    height: number = 600,
  ): Promise<string | null> {
    try {
      // Parse destination to get country
      const country = this.extractCountry(destination);
      if (!country) {
        this.logger.warn(`Could not extract country from destination: ${destination}`);
        return null;
      }

      // First, try to get cached image from database
      const cachedImage = await this.countryImageCacheService.getCachedImage(country);
      if (cachedImage) {
        this.logger.debug(`Using cached image for country: ${country}`);
        // Return cached URL with specific dimensions if it's from Unsplash
        if (cachedImage.source === 'unsplash' && cachedImage.imageUrl.includes('unsplash.com')) {
          return `${cachedImage.imageUrl}&w=${width}&h=${height}&fit=crop&crop=entropy`;
        }
        return cachedImage.imageUrl;
      }

      // If no cached image and no API key, return null
      if (!this.apiKey) {
        this.logger.warn('Unsplash API key not configured and no cached image available');
        return null;
      }

      // Search Unsplash API for new image
      this.logger.log(`Searching Unsplash API for country: ${country}`);
      const image = await this.searchImages(country);

      if (image) {
        // Cache the new image
        await this.cacheUnsplashImage(country, image);

        // Return optimized URL with specific dimensions
        return `${image.urls.raw}&w=${width}&h=${height}&fit=crop&crop=entropy`;
      }

      return null;
    } catch (error) {
      this.logger.error(`Error fetching image for ${destination}:`, error.message);
      return null;
    }
  }

  /**
   * Search for images on Unsplash
   */
  private async searchImages(query: string): Promise<UnsplashImage | null> {
    try {
      const url = new URL(`${this.apiUrl}/search/photos`);
      url.searchParams.append('query', query);
      url.searchParams.append('per_page', '1');
      url.searchParams.append('orientation', 'landscape');
      url.searchParams.append('order_by', 'relevant');

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Client-ID ${this.apiKey}`,
          'Accept-Version': 'v1',
        },
      });

      if (!response.ok) {
        this.logger.warn(`Unsplash API error for query "${query}": ${response.status}`);
        return null;
      }

      const data: UnsplashSearchResponse = await response.json();
      return data.results?.[0] || null;
    } catch (error) {
      this.logger.error(`Error searching Unsplash for "${query}":`, error.message);
      return null;
    }
  }

  /**
   * Cache Unsplash image data
   * @param country Country name
   * @param image Unsplash image data
   */
  private async cacheUnsplashImage(country: string, image: UnsplashImage): Promise<void> {
    try {
      await this.countryImageCacheService.cacheImage(country, {
        imageUrl: image.urls.raw,
        unsplashImageId: image.id,
        altDescription: image.alt_description || undefined,
        description: image.description || undefined,
        photographer: image.user.name,
        photographerUsername: image.user.username,
        source: 'unsplash',
      });

      this.logger.debug(`Cached Unsplash image for country: ${country}`);
    } catch (error) {
      this.logger.error(`Error caching image for ${country}:`, error.message);
    }
  }

  /**
   * Extract country from destination string
   */
  private extractCountry(destination: string): string | null {
    if (!destination) return null;

    // Simple extraction logic - you can enhance this
    const parts = destination.split(',').map(part => part.trim());

    // If there's a comma, assume the last part is the country
    if (parts.length > 1) {
      return parts[parts.length - 1];
    }

    // Otherwise, assume the whole string is the country
    return destination;
  }
}
