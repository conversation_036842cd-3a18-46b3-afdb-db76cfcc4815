/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { TripStatus } from '../helpers/trip-status.helper';
import { Activity } from '../interfaces/activity.interface';
import { Trip } from '../schemas/trip.schema';


@Injectable()
export class ItineraryUpdateService {
  private readonly logger = new Logger(ItineraryUpdateService.name);

  constructor(
    @InjectModel(Trip.name) private tripModel: Model<Trip>,
  ) { }

  /**
   * Extract activities from a day's itinerary and update the highLevelPlanActivities field
   * @param trip Trip to update
   * @param itineraryDay Day's itinerary
   * @returns Updated highLevelPlanActivities object
   * @private
   */
  private extractActivitiesForHighLevelPlan(
    trip: Trip,
    itineraryDay: any,
  ): Record<string, string[]> {
    const highLevelPlanActivities = trip.highLevelPlanActivities || {}

    // Check if itineraryDay exists and has activities
    if (!itineraryDay || !itineraryDay.activities || !Array.isArray(itineraryDay.activities)) {
      this.logger.warn('Invalid itinerary day structure for extracting activities');
      return highLevelPlanActivities;
    }

    const cities = itineraryDay.activities.map((activity: Activity) => activity.city);

    if (!cities || !cities[0]) {
      return highLevelPlanActivities;
    }

    // Initialize the city's activities array if it doesn't exist
    cities.forEach((city) => {
      if (!highLevelPlanActivities[city]) {
        highLevelPlanActivities[city] = [];
      }
    });

    // Extract activity names from the day's itinerary
    const activityNames = itineraryDay.activities
      .filter(
        (activity: Activity) => activity.name &&
          activity.city &&
          highLevelPlanActivities[activity.city] &&
          !highLevelPlanActivities[activity.city].includes(activity.name),
      )
      .map((activity: Activity) => ({
        name: activity.name,
        city: activity.city,
      }));

    // Add new activities to the city's activities array
    if (activityNames.length > 0) {
      activityNames.forEach((activity) => {
        highLevelPlanActivities[activity.city] = [
          ...highLevelPlanActivities[activity.city],
          activity.name,
        ];
        this.logger.log(
          `Added ${activity.name} for ${activity.city} to highLevelPlanActivities`,
        );
      });
    }

    return highLevelPlanActivities;
  }

  /**
   * Calculate cost breakdown using MongoDB aggregation pipeline
   * @param tripId Trip ID to calculate costs for
   * @returns Promise with cost breakdown
   */
  private async calculateCostBreakdownWithAggregation(tripId: Types.ObjectId | string): Promise<{ activities: number; meals: number; total_estimated_cost: number }> {
    const objectId = typeof tripId === 'string' ? new Types.ObjectId(tripId) : tripId;

    const result = await this.tripModel.aggregate([
      { $match: { _id: objectId } },
      { $unwind: '$itinerary' },
      { $unwind: '$itinerary.activities' },
      {
        $group: {
          _id: null,
          activities: {
            $sum: {
              $cond: [
                { $ne: ['$itinerary.activities.type', 'meal'] },
                { $ifNull: ['$itinerary.activities.cost', 0] },
                0
              ]
            }
          },
          meals: {
            $sum: {
              $cond: [
                { $eq: ['$itinerary.activities.type', 'meal'] },
                { $ifNull: ['$itinerary.activities.cost', 0] },
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          activities: 1,
          meals: 1,
          total_estimated_cost: { $add: ['$activities', '$meals'] }
        }
      }
    ]);

    return result[0] || { activities: 0, meals: 0, total_estimated_cost: 0 };
  }

  /**
   * Update a trip with a newly generated itinerary for a specific day
   * @param trip Trip to update
   * @param generatedSingleDayItinerary Generated itinerary for a specific day
   * @param day Day number
   * @returns Updated trip
   */
  async updateTrip(
    trip: Trip,
    generatedSingleDayItinerary: any,
    day: number,
  ): Promise<Trip> {
    // Basic validation - check if itinerary exists
    if (!generatedSingleDayItinerary || !generatedSingleDayItinerary['itinerary']) {
      throw new Error('Invalid itinerary structure');
    }

    const itineraryDay = generatedSingleDayItinerary['itinerary'];

    // Extract activities for the highLevelPlanActivities field
    const highLevelPlanActivities = this.extractActivitiesForHighLevelPlan(
      trip,
      itineraryDay[0],
    );

    // Determine if the trip is complete
    const isComplete = day === trip.tripDetails.totalDays;
    const newStatus = isComplete ? TripStatus.READY : TripStatus.IN_PROGRESS;

    // Check if the day exists in the itinerary array
    const existingDay = await this.tripModel.findOne({
      _id: trip._id,
      'itinerary.day': day,
    });

    let updatedTrip: Trip;

    if (!existingDay) {
      // If day doesn't exist, push new itinerary first
      updatedTrip = await this.tripModel.findOneAndUpdate(
        { _id: trip._id },
        {
          $push: { itinerary: itineraryDay[0] },
          $set: {
            currentDayProgress: day,
            status: newStatus,
            additionalTips: generatedSingleDayItinerary['additional_tips'],
            highLevelPlanActivities,
          },
        },
        { new: true }
      ) || trip;
    } else {
      // If day exists, update using positional $ operator first
      updatedTrip = await this.tripModel.findOneAndUpdate(
        {
          _id: trip._id,
          'itinerary.day': day,
        },
        {
          $set: {
            'itinerary.$': itineraryDay[0],
            currentDayProgress: day,
            status: newStatus,
            additionalTips: generatedSingleDayItinerary['additional_tips'],
            highLevelPlanActivities,
          },
        },
        { new: true }
      ) || trip;
    }

    // Now calculate and update cost breakdown using aggregation
    const costBreakdown = await this.calculateCostBreakdownWithAggregation(trip._id as Types.ObjectId);

    // Final update with calculated cost breakdown
    return await this.tripModel.findByIdAndUpdate(
      trip._id,
      { $set: { costBreakdown } },
      { new: true }
    ) || updatedTrip;
  }
}
