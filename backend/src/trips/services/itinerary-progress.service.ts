import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Trip } from '../schemas/trip.schema';
import { GenerateTripError } from '../helpers/error.format';

@Injectable()
export class ItineraryProgressService {
  private readonly logger = new Logger(ItineraryProgressService.name);

  constructor(@InjectModel(Trip.name) private tripModel: Model<Trip>) {}

  async updateDayProgress(
    trip: Trip,
    day: number,
    starting: boolean = false,
    error: string | null = null,
  ): Promise<Trip> {
    const now = new Date();
    try {
      return (
        (await this.tripModel.findOneAndUpdate(
          { _id: trip._id },
          {
            $set: {
              [`daysProgress.${day - 1}`]: {
                day,
                is_generating: starting,
                started_at: starting
                  ? now
                  : trip.daysProgress[day - 1]?.started_at || now,
                finished_at: starting ? null : now,
                error,
                tries: (trip.daysProgress[day - 1]?.tries || 0) + 1,
              },
            },
          },
          { new: true, upsert: true },
        )) || trip
      );
    } catch (error) {
      this.logger.warn(`Error updating day progress for day ${day}: ${error}`);
      throw new BadRequestException(
        GenerateTripError.FAILED_TO_UPDATE_DAY_PROGRESS,
      );
    }
  }

  async initializeDaysProgress(trip: Trip, totalDays: number): Promise<Trip> {
    if (!trip.daysProgress || trip.daysProgress.length === 0) {
      trip.daysProgress = Array.from({ length: totalDays }, (_, i) => ({
        day: i + 1,
        is_generating: false,
        started_at: null,
        tries: 0,
        finished_at: null,
        error: null,
      }));
      return await trip.save();
    }
    return trip;
  }

  getDayToContinueFrom(trip: Trip): number {
    const day = trip.currentDayProgress <= 0 ? 1 : trip.currentDayProgress;

    if (day >= trip.tripDetails.totalDays) {
      throw new BadRequestException('Trip is already finished');
    }

    // Check if day is in the daysProgress array
    const dayProgress = trip.daysProgress.find((item) => item.day === day);

    if (!dayProgress) {
      throw new BadRequestException('Day is not in the daysProgress array');
    }

    if (dayProgress.is_generating) {
      throw new BadRequestException('Day is still generating');
    }

    if (dayProgress.error) {
      return day;
    }

    if (day + 1 > trip.tripDetails.totalDays) {
      throw new BadRequestException('Trip is already finished');
    }

    return day + 1;
  }
}
