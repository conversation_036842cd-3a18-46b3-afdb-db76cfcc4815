/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { DayUsageService, InsufficientDaysException } from '../../days-balance/day-usage.service';
import { MapsValidationService } from '../../locations/services/maps-validation.service';
import { LocationContext } from '../../locations/interfaces/location-coordinates.interface';
import { AiModel } from '../helpers/ai-model.type';
import { GenerateTripError } from '../helpers/error.format';
import { StreamingResponseHandler } from '../helpers/streaming-response-handler';
import {
  CompletionResponse,
  isAsyncIterable,
} from '../interfaces/ai-provider.interface';
import { Trip } from '../schemas/trip.schema';
import { AiService } from './ai.service';
import { ErrorHandlingService } from './error-handling.service';
import { ItineraryParsingService } from './itinerary-parsing.service';
import { ItineraryProgressService } from './itinerary-progress.service';

import { TripManagementService } from './trip-management.service';
import { constructSingleDayPrompt } from '../helpers/prompts/single-day';


@Injectable()
export class SingleDayGenerationService {
  private readonly logger = new Logger(SingleDayGenerationService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly itineraryParsingService: ItineraryParsingService,
    private readonly itineraryProgressService: ItineraryProgressService,
    private readonly tripManagementService: TripManagementService,
    private readonly errorHandlingService: ErrorHandlingService,
    private readonly dayUsageService: DayUsageService,
    private readonly mapsValidationService: MapsValidationService,
  ) { }

  /**
   * Validate and update coordinates for activities using maps validation service
   * @param parsedItinerary Parsed itinerary with activities
   * @param tripDetails Trip details for context
   * @param day Day number for logging purposes
   * @returns Updated itinerary with validated coordinates
   */
  private async validateActivityCoordinates(
    parsedItinerary: any,
    tripDetails: Trip['tripDetails'],
    day: number,
  ): Promise<any> {
    if (!parsedItinerary?.itinerary || !Array.isArray(parsedItinerary.itinerary) || !parsedItinerary.itinerary[0]) {
      this.logger.warn('No itinerary found for coordinate validation');
      return parsedItinerary;
    }

    // Process each day in the itinerary
    const dayItinerary = parsedItinerary.itinerary[0]
    if (!dayItinerary.activities || !Array.isArray(dayItinerary.activities)) {
      return parsedItinerary;
    }

    // Create location context from trip details and day info
    const locationContext: LocationContext = {
      country: tripDetails.destination,
      city: dayItinerary?.city ?? "Unknown",
    };

    this.logger.debug(
      `Created location context for day ${day}: country=${locationContext.country}, city=${locationContext.city}`,
    );

    // Process each activity
    for (const activity of dayItinerary.activities) {
      if (!activity.location) {
        this.logger.debug(`Activity ${activity.id || activity.name} has no location, skipping coordinate validation`);
        continue;
      }

      try {
        this.logger.debug(`Validating coordinates for activity: ${activity.name} at ${activity.location}`);

        // Use activity-specific city if available, otherwise use day-level city
        if (activity?.city) {
          locationContext.city = activity.city;
          this.logger.debug(`Using activity-specific city: ${activity.city} for ${activity.name}`);
        } else {
          this.logger.debug(`Using day-level city: ${locationContext.city} for ${activity.name}`);
        }

        // Use maps validation service to get coordinates with proper context
        const validatedLocation = await this.mapsValidationService.validateLocation(
          activity.location,
          locationContext,
        );

        if (validatedLocation && validatedLocation.coordinates) {
          // Update activity coordinates with validated ones
          activity.coordinates = {
            lat: validatedLocation.coordinates.lat,
            lng: validatedLocation.coordinates.lng,
          };

          this.logger.debug(
            `Updated coordinates for ${activity.name}: ${activity.coordinates.lat}, ${activity.coordinates.lng}`,
          );
        } else {
          this.logger.warn(`Could not validate location for activity: ${activity.name} at ${activity.location}`);
          // Set default coordinates if validation fails
          activity.coordinates = activity.coordinates ?? {
            lat: 0,
            lng: 0,
          };
        }
      } catch (error) {
        this.logger.error(`Error validating coordinates for activity ${activity.name}: ${error.message}`);
        // Set default coordinates on error
        activity.coordinates = activity.coordinates ?? {
          lat: 0,
          lng: 0,
        };
      }
    }


    return parsedItinerary;
  }

  /**
   * Generate itinerary for a single day
   * @param day Day number
   * @param highLevelPlanContent High-level plan content
   * @param tripDetails Trip details
   * @param trip Trip object
   * @param modelToUse AI model to use
   * @param useStreaming Whether to use streaming
   * @returns Generated itinerary for the day
   */
  async generateSingleDayItinerary(
    day: number,
    highLevelPlanContent: string,
    tripDetails: Trip['tripDetails'],
    trip: Trip,
    modelToUse: AiModel,
    useStreaming: boolean,
  ): Promise<any> {
    const myTrip = await this.tripManagementService.findOne(trip.id);

    // Refetch the latest user balance from DB before starting generation
    const currentBalance = await this.dayUsageService.getCurrentDayBalance(myTrip.userId);

    // Check if user has sufficient days before starting generation
    if (currentBalance < 1) {
      this.logger.warn(
        `User ${myTrip.userId} has insufficient days for day ${day} of trip ${myTrip.id}. Available: ${currentBalance}, Required: 1`,
      );
      // Mark day as failed due to insufficient balance
      await this.itineraryProgressService.updateDayProgress(
        myTrip,
        day,
        false,
        'Insufficient days to generate this day',
      );
      throw new InsufficientDaysException(
        `Insufficient days. Available: ${currentBalance}, Required: 1`,
      );
    }

    this.logger.log(
      `Starting generation for day ${day} of trip ${myTrip.id}. User ${myTrip.userId} has ${currentBalance} days available.`,
    );

    try {
      // Update day progress to mark as in progress
      await this.itineraryProgressService.updateDayProgress(myTrip, day, true);
    } catch (error) {
      this.logger.warn(`Error updating day progress for day ${day}: ${error}`);
      throw new BadRequestException(
        GenerateTripError.FAILED_TO_UPDATE_DAY_PROGRESS,
      );
    }

    // Construct the prompt for this day
    const prompt = constructSingleDayPrompt({
      day,
      tripDetails,
      highLevelPlanContentResponse: highLevelPlanContent,
      model: modelToUse,
    });

    try {
      // Get the AI completion for this day
      let singleDayItineraryResponse = await this.aiService.getCompletion({
        prompt: prompt,
        model: modelToUse,
        stream: useStreaming,
        forSingleDay: true,
        day: day,
        trip: myTrip,
      });

      // Process streaming response if needed
      if (useStreaming && isAsyncIterable(singleDayItineraryResponse)) {
        this.logger.debug(`Processing streaming response for day ${day}`);
        singleDayItineraryResponse =
          (await StreamingResponseHandler.processStreamingResponse(
            singleDayItineraryResponse,
          )) as CompletionResponse;
      }

      if (!singleDayItineraryResponse) {
        await this.itineraryProgressService.updateDayProgress(
          myTrip,
          day,
          false,
          GenerateTripError.FAILED_TO_GET_SINGLE_DAY_ITINERARY_RESPONSE,
        );
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_GET_SINGLE_DAY_ITINERARY_RESPONSE,
        );
      }

      // Parse the response
      let parsedSingleDayItinerary =
        await this.itineraryParsingService.parseSingleDayItinerary(
          singleDayItineraryResponse as OpenAI.Chat.Completions.ChatCompletion,
        );

      // Validate and update coordinates using maps validation service
      this.logger.debug(`Validating coordinates for day ${day} activities`);
      parsedSingleDayItinerary = await this.validateActivityCoordinates(
        parsedSingleDayItinerary,
        tripDetails,
        day,
      );

      // Mark day as completed first
      await this.itineraryProgressService.updateDayProgress(myTrip, day, false);

      // Track day usage for single day generation ONLY after successful completion
      const dayResult = await this.dayUsageService.trackSingleDayGeneration(
        myTrip.userId,
        myTrip.id,
        day,
        { model: modelToUse },
      );

      // Check if day tracking failed (this could indicate insufficient days after generation)
      if (!dayResult) {
        this.logger.error(
          `Failed to track day usage for day ${day} of trip ${myTrip.id}. This may indicate insufficient days.`,
        );
        // Mark day as failed due to day tracking failure
        await this.itineraryProgressService.updateDayProgress(
          myTrip,
          day,
          false,
          'Failed to process day deduction after generation',
        );
        throw new Error('Failed to process day deduction after generation');
      }

      return parsedSingleDayItinerary;
    } catch (error) {
      return this.errorHandlingService.handleSingleDayGenerationError(
        error,
        myTrip,
        day,
      );
    }
  }
}
