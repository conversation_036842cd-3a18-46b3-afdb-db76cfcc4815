/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { RetryService } from '../../common/retry.service';
import { AiModel } from '../helpers/ai-model.type';
import { GenerateTripError } from '../helpers/error.format';
import { StreamingResponseHandler } from '../helpers/streaming-response-handler';
import { isAsyncIterable } from '../interfaces/ai-provider.interface';
import { AiService } from './ai.service';
import { ItineraryParsingService } from './itinerary-parsing.service';
import { RetryErrorType, RetryOptions } from '../../common/retry.service';

@Injectable()
export class HighLevelPlanService {
  private readonly logger = new Logger(HighLevelPlanService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly retryService: RetryService,
    private readonly itineraryParsingService: ItineraryParsingService,
  ) { }

  /**
   * Generate a high-level plan
   * @param prompt Prompt text
   * @param modelToUse AI model to use
   * @param useStreaming Whether to use streaming
   * @param totalDays Total days in trip
   * @param generationType Type of generation (one-shot, multi-round, assistant)
   * @param forSingleDay Whether this is for a single day (one-shot only)
   * @returns Generated high-level plan
   */
  async generateHighLevelPlan(
    prompt: string,
    modelToUse: AiModel,
    useStreaming: boolean,
    totalDays: number,
    generationType: string,
    forSingleDay: boolean = false,
  ): Promise<{
    content: string;
    parsedContent: any;
    name: string | null;
  }> {
    const retryOptions: RetryOptions = {
      maxAttempts: 3,
      initialDelayMs: 1000,
      maxDelayMs: 30000,
      backoffFactor: 2,
      retryableErrorTypes: [
        RetryErrorType.NETWORK,
        RetryErrorType.RATE_LIMIT,
        RetryErrorType.SERVER,
        RetryErrorType.TIMEOUT,
        RetryErrorType.AUTHENTICATION,
      ],
      retryCondition: (error) => {
        // Retry server errors (5xx)
        if (error.status >= 500 && error.status < 600) return true;
        // Retry rate limit errors (429)
        if (error.status === 429) return true;
        // Retry parsing/validation errors
        if (error.message) {
          const errorMsg = error.message.toLowerCase();
          if (
            errorMsg.includes('parse') ||
            errorMsg.includes('validation') ||
            errorMsg.includes('invalid format') ||
            errorMsg.includes('syntax error')
          ) {
            return true;
          }
        }
        return false;
      },
      onRetry: (error, attempt, delay) => {
        this.logger.warn(
          `Retrying ${generationType} plan generation (attempt ${attempt}) after ${Math.round(delay)}ms due to error: ${error.message}`,
        );
      },
    };

    return this.retryService.executeWithRetry(async () => {
      // Step 1: Get the AI completion
      const response = await this.aiService.getCompletion({
        prompt,
        model: modelToUse,
        stream: useStreaming,
        forSingleDay,
      });

      // Process streaming response if needed
      let processedResponse: any;
      if (useStreaming && isAsyncIterable(response)) {
        this.logger.debug(
          `Processing streaming response for ${generationType} plan`,
        );
        processedResponse =
          await StreamingResponseHandler.processStreamingResponse(response);
      } else {
        processedResponse = response;
      }

      // Step 2: Parse the high-level plan response
      const content = this.itineraryParsingService.parseHighLevelPlanResponse(
        processedResponse as OpenAI.Chat.Completions.ChatCompletion,
        totalDays,
      );

      // Step 3: Parse the JSON content
      try {
        const parsedContent = JSON.parse(content);

        // Step 4: Validate the parsed content
        if (
          !parsedContent.itinerary ||
          !Array.isArray(parsedContent.itinerary)
        ) {
          throw new BadRequestException(
            GenerateTripError.FAILED_TO_PARSE_HIGH_LEVEL_PLAN,
          );
        }

        // Step 5: Validate the itinerary length for multi-round
        if (
          generationType === 'multi-round' &&
          parsedContent.itinerary.length !== totalDays
        ) {
          throw new BadRequestException(
            GenerateTripError.INCORRECT_HIGH_LEVEL_PLAN_LENGTH,
          );
        }

        // Return both the string content and parsed object
        return {
          content,
          parsedContent,
          name: this.itineraryParsingService.extractTripName(content),
        };
      } catch (parseError) {
        this.logger.error(
          `Failed to parse high-level plan JSON: ${parseError.message}`,
        );
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_PARSE_HIGH_LEVEL_PLAN,
        );
      }
    }, retryOptions);
  }
}
