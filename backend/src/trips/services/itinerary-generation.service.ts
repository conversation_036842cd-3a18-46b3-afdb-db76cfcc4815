/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { setTimeout } from 'timers/promises';
import { GenerationMode } from '../../config/ai.config';
import { InsufficientDaysException } from '../../days-balance/day-usage.service';
import { DayUsageService } from '../../days-balance/day-usage.service';
import { GenerateTripDto } from '../dto/generate-trip.dto';
import {
  GenerateItineraryError,
  GenerateTripError,
} from '../helpers/error.format';
import { TripStatus } from '../helpers/trip-status.helper';
import { Trip } from '../schemas/trip.schema';
import { ErrorHandlingService } from './error-handling.service';
import { HighLevelPlanService } from './high-level-plan.service';
import { ItineraryConfigService } from './itinerary-config.service';
import { ItineraryProgressService } from './itinerary-progress.service';
import { ItineraryUpdateService } from './itinerary-update.service';

import { SingleDayGenerationService } from './single-day-generation.service';
import { StreamingNotificationService } from './streaming-notification.service';

import { TripCreationService } from './trip-creation.service';
import { TripManagementService } from './trip-management.service';
import { constructPrimaryPrompt } from '../helpers/prompts/high-level-plan';
import { constructOneShotPrompt } from '../helpers/prompts/one-shot';

@Injectable()
export class ItineraryGenerationService {
  private readonly logger = new Logger(ItineraryGenerationService.name);

  constructor(
    private readonly tripManagementService: TripManagementService,
    private readonly itineraryConfigService: ItineraryConfigService,
    private readonly itineraryProgressService: ItineraryProgressService,
    private readonly itineraryUpdateService: ItineraryUpdateService,
    private readonly errorHandlingService: ErrorHandlingService,
    private readonly tripCreationService: TripCreationService,

    private readonly streamingNotificationService: StreamingNotificationService,
    private readonly highLevelPlanService: HighLevelPlanService,
    private readonly singleDayGenerationService: SingleDayGenerationService,
    private readonly dayUsageService: DayUsageService,
  ) { }

  /**
   * Process trip generation with modern deferred execution
   * @param trip Trip object
   * @param modelToUse AI model to use
   * @param useStreaming Whether to use streaming
   * @param startDay Day to start from (defaults to 1)
   * @param endDay Day to end at (uses trip's total days if not specified)
   * @param startTime Start time of generation (optional, used for completion notifications)
   */
  private async processTripGeneration(
    trip: Trip,
    modelToUse: any, // Using any to match the existing code's type
    useStreaming: boolean,
    startDay: number = 1,
    endDay?: number,
    startTime?: Date,
  ): Promise<void> {
    const tripId = trip.id;
    const userId = trip.userId;
    const destination = trip.tripDetails.destination;
    const totalDays = endDay || trip.tripDetails.totalDays;

    // Defer execution to the next event loop iteration (equivalent to setImmediate)
    await setTimeout(0);

    for (let currentDay = startDay; currentDay <= totalDays; currentDay++) {
      try {
        this.logger.log(
          `Generating itinerary for day ${currentDay} of ${totalDays}`,
        );

        // Send progress notification
        this.streamingNotificationService.sendDayGenerationProgress(
          tripId,
          currentDay,
          totalDays,
          startTime,
        );

        const parsedSingleDayItinerary =
          await this.singleDayGenerationService.generateSingleDayItinerary(
            currentDay,
            trip.highLevelPlan || '',
            trip.tripDetails,
            trip,
            modelToUse,
            useStreaming,
          );

        await this.itineraryUpdateService.updateTrip(
          trip,
          parsedSingleDayItinerary,
          currentDay,
        );

        // Send completion notification if this is the last day
        if (currentDay === totalDays) {
          this.streamingNotificationService.sendTripGenerationComplete(
            tripId,
            destination,
            userId,
            startTime,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error generating itinerary for day ${currentDay}: ${error.message}`,
        );

        // Check if it's an insufficient days error or day deduction failure
        if (
          error.message?.includes('Insufficient days') ||
          error.message?.includes('Failed to process day deduction')
        ) {
          this.logger.warn(
            `Stopping trip generation due to day issues at day ${currentDay}: ${error.message}`,
          );
          // Send insufficient days notification instead of generic error
          try {
            this.errorHandlingService.handleInsufficientBalanceError(
              error,
              userId,
              tripId,
            );
          } catch (error) { }
        } else {
          // Send error notification for other errors
          this.errorHandlingService.sendTripGenerationError(
            tripId,
            currentDay,
            userId,
            destination,
            startTime,
          );
        }

        // Update day progress to mark as failed
        try {
          await this.itineraryProgressService.updateDayProgress(
            trip,
            currentDay,
            false,
            error.message ||
            GenerateTripError.FAILED_TO_GENERATE_SINGLE_DAY_ITINERARY,
          );
        } catch (progressError) {
          this.logger.error(
            `Failed to update day progress for day ${currentDay}: ${progressError.message}`,
          );
        }

        // break the loop
        break;
      }
    }
  }

  /**
   * Generate a trip based on the provided details
   * @param tripDto Trip details
   * @param userId User ID
   * @returns Generated trip
   */
  async generateTrip(
    tripDto: GenerateTripDto,
    userId: string,
  ): Promise<Trip | null> {
    if (!userId) {
      throw new BadRequestException('User ID is required');
    }

    const startTime = new Date();
    const generationMode = this.itineraryConfigService.getGenerationMode();

    this.logger.debug(`Generation mode: ${generationMode}`);

    const isOneShot = generationMode === GenerationMode.ONE_SHOT;

    try {
      // Select the appropriate generation method based on configuration
      if (isOneShot) {
        return this.generateOneShotTrip(tripDto, userId, startTime);
      }

      return this.generateMultiRoundTrip(tripDto, userId, startTime);
    } catch (error) {
      if (error instanceof InsufficientDaysException) {
        this.logger.warn(
          `User ${userId} has insufficient days for trip generation: ${error.message}`,
        );
        this.errorHandlingService.handleInsufficientBalanceError(error, userId);
      }
      throw error;
    }
  }

  /**
   * Generate a trip using the one-shot approach
   * @param tripDto Trip details
   * @param userId User ID
   * @param startTime Start time of generation
   * @returns Generated trip
   * @private
   */
  private async generateOneShotTrip(
    tripDto: GenerateTripDto,
    userId: string,
    startTime: Date,
  ): Promise<Trip> {
    const prompt = constructOneShotPrompt(tripDto);
    const totalDays = this.tripCreationService.calculateTotalDays(
      tripDto.startDate,
      tripDto.endDate,
    );
    const modelToUse = this.itineraryConfigService.getModelToUse();
    const useStreaming = this.itineraryConfigService.isStreamingEnabled();

    // Refetch the latest user balance from DB before starting generation
    const currentBalance = await this.dayUsageService.getCurrentDayBalance(userId);

    // Check if user has sufficient days before starting generation
    if (currentBalance < 1) {
      this.logger.warn(
        `User ${userId} has insufficient days for one-shot trip generation. Available: ${currentBalance}, Required: 1`,
      );
      throw new InsufficientDaysException(
        `Insufficient days. Available: ${currentBalance}, Required: 1`,
      );
    }

    this.logger.log(
      `Starting one-shot generation for user ${userId}. User has ${currentBalance} days available.`,
    );

    try {
      // Generate the high-level plan
      const result = await this.highLevelPlanService.generateHighLevelPlan(
        prompt,
        modelToUse,
        useStreaming,
        totalDays,
        'one-shot',
        true,
      );

      // Extract the trip name and content
      const tripName = result.name;
      const parsedContent = result.parsedContent;

      // Create the initial trip
      const trip = await this.tripCreationService.createInitialTrip(
        tripDto,
        totalDays,
        userId,
        prompt,
        tripName,
      );
      const tripId = trip.id;

      // Update trip with one shot content
      trip.itinerary = parsedContent.itinerary;
      trip.additionalTips = parsedContent.additionalTips;
      trip.costBreakdown = parsedContent.costBreakdown;
      trip.status = TripStatus.READY;
      trip.currentDayProgress = totalDays;

      // Update days progress
      await this.tripCreationService.updateTripWithCompletedDaysProgress(
        trip,
        totalDays,
        startTime,
      );

      // Track day usage for one-shot trip generation
      const dayResult = await this.dayUsageService.trackTripGenerationDay(
        userId,
        tripId,
        'Day used for one-shot trip generation',
        { model: modelToUse, generationType: 'one-shot' },
      );

      // Check if day tracking failed (this could indicate insufficient days after generation)
      if (!dayResult) {
        this.logger.error(
          `Failed to track day usage for one-shot trip ${tripId}. This may indicate insufficient days.`,
        );
        // Mark trip as failed due to day tracking failure
        trip.status = TripStatus.ERROR;
        await trip.save();
        throw new Error('Failed to process day deduction after generation');
      }

      // Send completion notification
      this.streamingNotificationService.sendTripGenerationComplete(
        tripId,
        tripDto.destination,
        userId,
        startTime,
      );

      return trip;
    } catch (error) {
      // If it's an insufficient days error, don't retry and handle it specially
      if (error instanceof InsufficientDaysException) {
        this.logger.warn(
          `Insufficient days for user ${userId}: ${error.message}`,
        );
        this.errorHandlingService.handleInsufficientBalanceError(error, userId);
      }

      return this.errorHandlingService.handleHighLevelPlanError(error, userId, tripDto.destination);
    }
  }

  /**
   * Generate a trip using the multi-round approach
   * @param tripDto Trip details
   * @param userId User ID
   * @param startTime Start time of generation
   * @returns Generated trip
   * @private
   */
  private async generateMultiRoundTrip(
    tripDto: GenerateTripDto,
    userId: string,
    startTime: Date,
  ): Promise<Trip> {
    const modelToUse = this.itineraryConfigService.getModelToUse();
    const useStreaming = this.itineraryConfigService.isStreamingEnabled();
    const prompt = constructPrimaryPrompt(tripDto, modelToUse);
    const totalDays = this.tripCreationService.calculateTotalDays(
      tripDto.startDate,
      tripDto.endDate,
    );

    // Refetch the latest user balance from DB before starting generation
    const currentBalance = await this.dayUsageService.getCurrentDayBalance(userId);

    // Check if user has sufficient days before starting generation
    // For multi-round, we need at least 1 day to start (will check per day during generation)
    if (currentBalance < 1) {
      this.logger.warn(
        `User ${userId} has insufficient days for multi-round trip generation. Available: ${currentBalance}, Required: 1`,
      );
      throw new InsufficientDaysException(
        `Insufficient days. Available: ${currentBalance}, Required: 1`,
      );
    }

    this.logger.log(
      `Starting multi-round generation for user ${userId}. User has ${currentBalance} days available.`,
    );

    try {
      // Generate the high-level plan
      const result = await this.highLevelPlanService.generateHighLevelPlan(
        prompt,
        modelToUse,
        useStreaming,
        totalDays,
        'multi-round',
      );

      // Extract the trip name
      const tripName = result.name;

      this.logger.debug(
        `Extracted trip name: ${tripName || 'Not found, using default'}`,
      );

      // Create the initial trip
      const trip = await this.tripCreationService.createInitialTrip(
        tripDto,
        totalDays,
        userId,
        prompt,
        tripName,
      );

      // Save the high-level plan
      trip.highLevelPlan = result.content || '';
      await trip.save();

      // No day deduction for high-level plan generation
      // Days will be deducted per individual day generation

      const maxDaysToGenerate = Math.min(
        totalDays,
        this.itineraryConfigService.getMaxDaysToGenerate(),
      );

      // Generate itineraries for each day using modern deferred execution
      this.processTripGeneration(
        trip,
        modelToUse,
        useStreaming,
        1, // startDay
        maxDaysToGenerate, // endDay
        startTime,
      ).catch((error) => {
        this.logger.error(`Error in day generation process: ${error.message}`);
      });

      return trip;
    } catch (error) {
      // If it's an insufficient days error, don't retry and handle it specially
      if (error instanceof InsufficientDaysException) {
        this.logger.warn(
          `Insufficient days for user ${userId}: ${error.message}`,
        );
        this.errorHandlingService.handleInsufficientBalanceError(error, userId);
      }

      return this.errorHandlingService.handleHighLevelPlanError(error, userId, tripDto.destination);
    }
  }

  /**
   * Continue generating a trip from where it left off
   * @param id Trip ID
   * @returns Updated trip
   */
  async continueGeneration(id: string): Promise<Trip> {
    const trip = await this.tripManagementService.findOne(id);
    if (!trip) {
      throw new BadRequestException(`Trip with ID ${id} not found`);
    }

    if (trip.status !== TripStatus.IN_PROGRESS) {
      throw new BadRequestException('Trip is not in progress');
    }

    // Check if any day is currently generating
    const isAnyDayGenerating = trip.daysProgress?.some((day) => day.is_generating);
    if (isAnyDayGenerating) {
      throw new BadRequestException('Trip generation is already in progress. Please wait for the current generation to complete.');
    }

    const day = this.itineraryProgressService.getDayToContinueFrom(trip);
    const totalDays = trip.tripDetails.totalDays;
    const modelToUse = this.itineraryConfigService.getModelToUse();
    const useStreaming = this.itineraryConfigService.isStreamingEnabled();

    try {
      // Days will be checked and deducted per individual day generation

      // Continue generation using modern deferred execution
      this.processTripGeneration(
        trip,
        modelToUse,
        useStreaming,
        day, // startDay
        totalDays, // endDay
      ).catch((error) => {
        this.logger.error(
          `Error in continue generation process: ${error.message}`,
        );
      });

      return trip;
    } catch (error) {
      if (error instanceof InsufficientDaysException) {
        this.logger.warn(
          `Insufficient days for user ${trip.userId}: ${error.message}`,
        );
        this.errorHandlingService.handleInsufficientBalanceError(
          error,
          trip.userId,
          trip.id,
        );
      } else {
        this.logger.error(
          `Error checking days for trip ${id}: ${error.message}`,
        );
      }
      throw error;
    }
  }

  /**
   * Get missing days for a trip
   * @param id Trip ID
   * @returns Days progress array
   */
  async getMissingDays(id: string): Promise<Trip['daysProgress']> {
    if (!id) {
      throw new BadRequestException(
        GenerateItineraryError.INCOMPLETE_TRIP_DETAILS,
      );
    }

    const trip = await this.tripManagementService.findOne(id);
    if (!trip) {
      throw new BadRequestException(`Trip with ID ${id} not found`);
    }

    if (!trip.tripDetails) {
      throw new BadRequestException(
        GenerateItineraryError.INCOMPLETE_TRIP_DETAILS,
      );
    }

    // Initialize days progress if needed
    await this.itineraryProgressService.initializeDaysProgress(
      trip,
      trip.tripDetails.totalDays,
    );

    return trip.daysProgress;
  }
}
