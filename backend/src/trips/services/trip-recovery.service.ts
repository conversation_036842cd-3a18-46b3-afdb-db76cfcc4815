import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '../../config/config.service';
import { TripStatus } from '../helpers/trip-status.helper';
import { Trip } from '../schemas/trip.schema';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * Service responsible for finding trips with gaps in daily itineraries
 * or trips that are stuck in generation
 */
@Injectable()
export class TripRecoveryService {
  private readonly logger = new Logger(TripRecoveryService.name);
  private readonly threshold: number;

  constructor(
    @InjectModel(Trip.name) private tripModel: Model<Trip>,
    private readonly configService: ConfigService,
  ) {
    this.threshold = this.configService.getTripRecoveryConfig().threshold || 20; // Default to 20 minutes if not configured
  }

  /**
   * Search for trips with gaps in daily itineraries
   * Returns trips that have missing days in their itinerary array
   * (e.g., having day 2 and day 3 but missing day 1)
   */
  async findTripsWithGaps(): Promise<Trip[]> {
    try {
      this.logger.log('Searching for trips with gaps in daily itineraries');

      // Find all trips with itinerary data
      const trips = await this.tripModel.find({
        itinerary: { $exists: true, $ne: [] },
      });

      const tripsWithGaps: Trip[] = [];

      for (const trip of trips) {
        if (this.hasGapsInItinerary(trip)) {
          tripsWithGaps.push(trip);
        }
      }

      this.logger.log(`Found ${tripsWithGaps.length} trips with gaps in itineraries`);
      return tripsWithGaps;
    } catch (error) {
      this.logger.error(
        `Error searching for trips with gaps: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Search for stuck trips that have status in_progress and one of their days
   * being generated for more than the threshold time
   */
  async findStuckTrips(): Promise<Trip[]> {
    try {
      this.logger.log('Searching for stuck trips');

      // Calculate the timestamp for threshold minutes ago
      const thresholdMinutesAgo = new Date();
      thresholdMinutesAgo.setMinutes(
        thresholdMinutesAgo.getMinutes() - this.threshold,
      );

      // Find all in-progress trips
      const inProgressTrips = await this.tripModel.find({
        status: TripStatus.IN_PROGRESS,
      });

      const stuckTrips: Trip[] = [];

      for (const trip of inProgressTrips) {
        // Skip trips with no days progress
        if (!trip.daysProgress || trip.daysProgress.length === 0) {
          continue;
        }

        // Check if the trip has a day that's been generating for too long
        const stuckDay = trip.daysProgress.find(
          (day) =>
            day.is_generating === true &&
            day.started_at &&
            day.started_at < thresholdMinutesAgo,
        );

        if (stuckDay) {
          stuckTrips.push(trip);
        }
      }

      this.logger.log(`Found ${stuckTrips.length} stuck trips`);
      return stuckTrips;
    } catch (error) {
      this.logger.error(
        `Error searching for stuck trips: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if a trip has gaps in its itinerary
   * @param trip The trip to check
   * @returns true if there are gaps in the itinerary, false otherwise
   */
  private hasGapsInItinerary(trip: Trip): boolean {
    if (!trip.itinerary || trip.itinerary.length === 0) {
      return false;
    }

    if (!trip.tripDetails || !trip.tripDetails.totalDays) {
      return false;
    }

    // Get all existing days in the itinerary
    const existingDays = new Set(trip.itinerary.map((day) => day.day));

    // Check if there are any missing days from 1 to totalDays
    for (let day = 1; day <= trip.tripDetails.totalDays; day++) {
      if (!existingDays.has(day)) {
        // Check if any later day exists - if so, this is a gap
        for (let laterDay = day + 1; laterDay <= trip.tripDetails.totalDays; laterDay++) {
          if (existingDays.has(laterDay)) {
            return true; // Found a gap
          }
        }
      }
    }

    return false;
  }

  /**
   * Cron job that runs every 5 minutes to fix stuck trips
   * by setting their generating days to false
   */
  @Cron(CronExpression.EVERY_HOUR)
  async fixStuckTrips(): Promise<void> {
    this.logger.log('Starting stuck trips recovery process');

    try {
      const stuckTrips = await this.findStuckTrips();

      if (stuckTrips.length === 0) {
        this.logger.log('No stuck trips found');
        return;
      }

      this.logger.log(`Found ${stuckTrips.length} stuck trips, fixing them...`);

      let fixedTripsCount = 0;

      for (const trip of stuckTrips) {
        try {
          await this.fixStuckTrip(trip);
          fixedTripsCount++;
        } catch (error) {
          this.logger.error(
            `Error fixing stuck trip ${trip.id}: ${error.message}`,
            error.stack,
          );
        }
      }

      this.logger.log(`Successfully fixed ${fixedTripsCount} out of ${stuckTrips.length} stuck trips`);
    } catch (error) {
      this.logger.error(
        `Error during stuck trips recovery: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Fix a stuck trip by setting all generating days to false
   * @param trip The stuck trip to fix
   */
  private async fixStuckTrip(trip: Trip): Promise<void> {
    const thresholdMinutesAgo = new Date();
    thresholdMinutesAgo.setMinutes(
      thresholdMinutesAgo.getMinutes() - this.threshold,
    );

    // Find all stuck days (generating for too long)
    const stuckDays = trip.daysProgress.filter(
      (day) =>
        day.is_generating === true &&
        day.started_at &&
        day.started_at < thresholdMinutesAgo,
    );

    if (stuckDays.length === 0) {
      return;
    }

    this.logger.log(
      `Fixing trip ${trip.id}: setting ${stuckDays.length} stuck days to not generating`,
    );

    // Update each stuck day to not generating
    const updatePromises = stuckDays.map((stuckDay) =>
      this.tripModel.updateOne(
        {
          _id: trip.id,
          'daysProgress.day': stuckDay.day
        },
        {
          $set: {
            'daysProgress.$.is_generating': false,
            'daysProgress.$.finished_at': new Date(),
            'daysProgress.$.error': 'Generation timeout - reset by recovery process',
          },
        },
      ),
    );

    await Promise.all(updatePromises);

    this.logger.log(`Successfully fixed stuck days for trip ${trip.id}`);
  }
}
