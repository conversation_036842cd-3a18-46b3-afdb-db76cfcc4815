import { Injectable } from '@nestjs/common';
import { Trip } from '../schemas/trip.schema';
import { GenerateTripDto } from '../dto/generate-trip.dto';
import { TripManagementService } from './trip-management.service';
import { UnsplashService } from './unsplash.service';

import { TripStatus } from '../helpers/trip-status.helper';


@Injectable()
export class TripCreationService {


  constructor(
    private readonly tripManagementService: TripManagementService,
    private readonly unsplashService: UnsplashService,
  ) { }

  /**
   * Calculate total days between start and end dates (inclusive)
   * @param startDate Start date
   * @param endDate End date
   * @returns Total days (inclusive of both start and end dates)
   */
  calculateTotalDays(startDate: Date, endDate: Date): number {
    // Create new Date objects to avoid modifying the original dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Set both dates to midnight UTC to ensure consistent calculation across timezones
    start.setUTCHours(0, 0, 0, 0);
    end.setUTCHours(0, 0, 0, 0);

    // Calculate the difference in milliseconds
    const diffTime = end.getTime() - start.getTime();

    // Convert to days using Math.floor to avoid rounding issues
    // Add 1 to include both the start and end date (inclusive counting)
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;

    // Ensure minimum of 1 day (same day trips)
    return Math.max(1, diffDays);
  }

  /**
   * Create initial trip
   * @param tripDto Trip details
   * @param totalDays Total days
   * @param userId User ID
   * @param prompt High-level plan prompt
   * @param tripName Optional AI-generated trip name
   * @returns Created trip
   */
  async createInitialTrip(
    tripDto: GenerateTripDto,
    totalDays: number,
    userId: string,
    prompt: string,
    tripName?: string | null,
  ): Promise<Trip> {
    // Assistants API is no longer supported, so no thread_id needed
    const thread_id: string | undefined = undefined;

    // Fetch destination image URL
    const imageUrl = await this.unsplashService.getDestinationImageUrl(
      tripDto.destination,
      tripDto.arrivalCity,
    );

    return await this.tripManagementService.create({
      name: tripName || `Your Trip to ${tripDto.destination}`,
      tripDetails: {
        destination: tripDto.destination,
        tripType: tripDto.tripType,
        startDate: tripDto.startDate.toISOString(),
        endDate: tripDto.endDate.toISOString(),
        totalDays,
        budget: tripDto.budget,
        intensity: tripDto.intensity,
        arrivalCity: tripDto.arrivalCity,
        departureCity: tripDto.departureCity,
        arrivalTime: tripDto.arrivalTime,
        departureTime: tripDto.departureTime,
        wakeUpTime: tripDto.wakeUpTime,
        sleepTime: tripDto.sleepTime,
        cuisinePreferences: tripDto.cuisinePreferences,
        mustVisitCities: tripDto.mustVisitCities,
        additionalRequirements: tripDto.additionalRequirements,
        interests: tripDto.interests,
        people: {
          adults: tripDto.adults,
          children: tripDto.children,
        },
        imageUrl,
      },
      itinerary: [],
      status: TripStatus.IN_PROGRESS,
      additionalTips: [],
      costBreakdown: {
        activities: 0,
        meals: 0,
        total_estimated_cost: 0,
      },
      currentBuffer: '',
      currentDayProgress: 0,
      retryCount: 0,
      lastRetryDate: new Date(),
      daysProgress: Array.from({ length: totalDays }, (_, i) => ({
        day: i + 1,
        is_generating: false,
        started_at: null,
        tries: 0,
        finished_at: null,
        error: null,
      })),
      userId,
      highLevelPlanPrompt: prompt,
      thread_id,
    });
  }

  /**
   * Update trip with completed days progress
   * @param trip Trip to update
   * @param totalDays Total days
   * @param startTime Start time
   * @returns Updated trip
   */
  async updateTripWithCompletedDaysProgress(
    trip: Trip,
    totalDays: number,
    startTime: Date,
  ): Promise<Trip> {
    trip.daysProgress = Array.from({ length: totalDays }, (_, i) => ({
      day: i + 1,
      is_generating: false,
      started_at: startTime,
      tries: 1,
      finished_at: new Date(),
      error: null,
    }));

    return await trip.save();
  }
}
