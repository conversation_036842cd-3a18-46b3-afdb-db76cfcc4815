/* eslint-disable */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ApiKeyManagerService } from '../../api-key-manager/api-key-manager.service';
import { ConfigService } from '../../config/config.service';
import { AiProviderFactory } from '../factories/ai-provider.factory';
import { AiModel } from '../helpers/ai-model.type';
import { constructSingleDayPrompt } from '../helpers/prompts/single-day';
import {
  CompletionResponse,
  Message,
} from '../interfaces/ai-provider.interface';
import { DeepSeekProvider } from '../providers/deepseek.provider';
import { GroqProvider } from '../providers/groq.provider';
import { OpenAiProvider } from '../providers/openai.provider';
import { Trip } from '../schemas/trip.schema';

@Injectable()
export class AiService implements OnModuleInit {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly providerFactory: AiProviderFactory,
    private readonly openAiProvider: OpenAiProvider,
    private readonly groqProvider: GroqProvider,
    private readonly deepSeekProvider: DeepSeekProvider,
    private readonly apiKeyManagerService: ApiKeyManagerService,
  ) { }

  /**
   * Calculate the date for a specific day in the trip
   * @param startDate The start date of the trip (ISO string)
   * @param daysToAdd Number of days to add to the start date
   * @returns Formatted date string (YYYY-MM-DD)
   */
  calculateDayDate(startDate: string | Date, daysToAdd: number): string {
    const date = new Date(startDate);
    date.setDate(date.getDate() + daysToAdd);
    return date.toISOString().split('T')[0];
  }

  /**
   * Initialize providers on module initialization
   */
  onModuleInit() {
    // Register providers
    this.providerFactory.registerProvider(AiModel.OPENAI, this.openAiProvider);
    this.providerFactory.registerProvider(AiModel.GROQ, this.groqProvider);
    this.providerFactory.registerProvider(
      AiModel.DEEPSEEK,
      this.deepSeekProvider,
    );

    // Initialize each provider
    this.initializeProviders();

    this.logger.log('AI Service initialized with providers');
  }

  /**
   * Initialize all registered providers
   */
  private initializeProviders(): void {
    // Initialize OpenAI provider
    const openAiConfig = this.providerFactory.getProviderConfig(AiModel.OPENAI);
    this.openAiProvider.initialize(openAiConfig);

    // Initialize Groq provider
    const groqConfig = this.providerFactory.getProviderConfig(AiModel.GROQ);
    this.groqProvider.initialize(groqConfig);

    // Initialize DeepSeek provider
    const deepSeekConfig = this.providerFactory.getProviderConfig(
      AiModel.DEEPSEEK,
    );
    this.deepSeekProvider.initialize(deepSeekConfig);

    // Log that API key manager is available for usage tracking
    if (this.apiKeyManagerService) {
      this.logger.log('API key manager is available for usage tracking');
    } else {
      this.logger.warn(
        'API key manager is not available, API key usage will not be tracked',
      );
    }
  }

  /**
   * Get the completion configuration for the specified model
   * @param model AI model to use
   * @param forSingleDay Whether this is for a single day generation
   * @param messages Messages to send to the AI
   * @param stream Whether to stream the response
   * @returns Completion configuration
   */
  private getCompletionConfig(
    model: AiModel,
    forSingleDay: boolean,
    messages: Message[],
    stream: boolean = false,
  ) {
    const provider = this.providerFactory.getProvider(model);
    const modelConfig = provider.getModelConfig(model, forSingleDay);

    return {
      max_tokens: 6000, // Increased to handle more complex responses with city information
      top_p: 1,
      messages,
      stream,
      ...modelConfig,
    };
  }

  private getMessages(prompt: string, trip?: Trip, day?: number): Message[] {
    const messages: Message[] = [
      {
        role: 'system',
        content: prompt.includes('high level itinerary')
          ? 'You are a travel assistant that generates structured high level itineraries based on trip details. Your output must be logically ordered, geographically feasible, Optimal routes and free of pointless detours.'
          : 'You are a travel assistant that generates structured daily itineraries based on user preferences. Ensure balanced time allocation and realistic scheduling, And make sure to avoid Duplicate Activities',
      },
    ];

    if (!trip || !day) {
      messages.push({
        role: 'user',
        content: prompt,
      });
      return messages;
    }

    // We don't need to include the high-level plan in the messages
    // since we're already extracting the relevant information from it

    // Extract day-specific information from the high-level plan
    const highLevelPlanContent = trip.highLevelPlan;
    const tripDto = trip.tripDetails;
    const model =
      this.configService.getGenerationConfig().modelToUse || AiModel.GROQ;

    const existingDay = trip.itinerary.find((item) => item.day === day);
    let activitiesContext = '';

    // Extract day-specific information from the high-level plan
    const relevantCities: string[] = [];
    const dayInfo: {
      startCity?: string;
      endCity?: string;
      description?: string;
      isFirstDay: boolean;
      isLastDay: boolean;
      date?: string;
    } = {
      isFirstDay: day === 1,
      isLastDay: day === tripDto.totalDays,
      date: this.calculateDayDate(tripDto.startDate, day - 1),
    };

    try {
      const parsedHighLevelPlan = JSON.parse(highLevelPlanContent);
      const dayPlan = parsedHighLevelPlan.itinerary.find(
        (d: { day: number }) => d.day === day,
      );

      if (dayPlan) {
        // Extract city information
        if (dayPlan.start_city) {
          const city = dayPlan.start_city.trim().split(',')[0].trim();
          dayInfo.startCity = city;
          relevantCities.push(city);
        }

        if (dayPlan.end_city) {
          const city = dayPlan.end_city.trim().split(',')[0].trim();
          dayInfo.endCity = city;
          if (
            dayPlan.end_city !== dayPlan.start_city &&
            dayPlan.end_city !== dayInfo.startCity &&
            dayPlan.end_city !== dayInfo.endCity
          ) {
            relevantCities.push(city);
          }
        }

        // Extract description if available
        if (dayPlan.description) {
          dayInfo.description = dayPlan.description;
        }
      }
    } catch (error) {
      this.logger.warn(
        `Error parsing high-level plan for day ${day}: ${error}`,
      );
    }

    // Only prepare activities context if we're generating a new day or regenerating an existing day
    if (!existingDay || trip.daysProgress.find((d) => d.day === day)?.error) {
      // Filter activities to only include those for relevant cities
      const relevantActivities: Record<string, string[]> = {};
      if (trip.highLevelPlanActivities && relevantCities.length > 0) {
        relevantCities.forEach((city) => {
          if (trip.highLevelPlanActivities[city]) {
            relevantActivities[city] = trip.highLevelPlanActivities[city];
          }
        });
      }

      // Format activities for the prompt
      if (Object.keys(relevantActivities).length > 0) {
        activitiesContext = "Don't include the following activities :\n";
        Object.entries(relevantActivities).forEach(([city, activities]) => {
          if (activities.length > 0) {
            activitiesContext += `${city}: ${activities.join(', ')}\n`;
          }
        });
        this.logger.log(
          `Activities context for day ${day}:\n${activitiesContext}`,
        );
      }
    } else {
      this.logger.log(
        `Day ${day} already exists in the itinerary, skipping activities context preparation`,
      );
    }

    // Use the updated constructSingleDayPrompt function
    const currentDayPrompt = constructSingleDayPrompt({
      day,
      tripDetails: tripDto,
      highLevelPlanContentResponse: highLevelPlanContent,
      model,
      trip,
      dayInfo,
      activitiesContext,
    });

    messages.push({
      role: 'user',
      content: currentDayPrompt,
    });

    return messages;
  }

  /**
   * Get a completion from the AI provider
   * @param options Completion options
   * @returns Completion response or stream
   */
  async getCompletion({
    prompt,
    model,
    stream = false,
    forSingleDay = false,
    day,
    trip,
  }: {
    stream: boolean;
    prompt: string;
    model: AiModel;
    forSingleDay?: boolean;
    day?: number;
    trip?: Trip;
  }): Promise<CompletionResponse | AsyncIterable<any>> {
    // Use streaming from config if not explicitly provided
    // This is specifically for AI streaming, not WebSocket notifications
    const useStreaming =
      stream || this.configService.get<boolean>('ai.useStreaming') || false;
    this.logger.debug(`Using AI streaming: ${useStreaming}`);

    const messages: Message[] = this.getMessages(prompt, trip, day);

    // Get the provider for the specified model
    const provider = this.providerFactory.getProvider(model);
    const completionConfig = this.getCompletionConfig(
      model,
      forSingleDay,
      messages,
      useStreaming,
    );

    return await provider.getCompletion(completionConfig);
  }
}
