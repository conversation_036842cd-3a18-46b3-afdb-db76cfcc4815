/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { GenerateTripError } from '../helpers/error.format';


@Injectable()
export class ItineraryParsingService {
  private readonly logger = new Logger(ItineraryParsingService.name);

  constructor() { }

  /**
   * Extract city names from a string that might contain multiple cities
   * @param cityString String containing city names (e.g., "Paris to London" or "Paris / London")
   * @returns Array of city names
   */
  private extractCitiesFromString(cityString: string): string[] {
    if (!cityString) return [];

    // Check for "to" pattern (e.g., "Paris to London")
    if (cityString.includes(' to ')) {
      return cityString.split(' to ').map((city) => city.trim());
    }

    // Check for "/" pattern (e.g., "Paris / London")
    if (cityString.includes(' / ')) {
      return cityString.split(' / ').map((city) => city.trim());
    }

    // Return as a single city if no special patterns
    return [cityString];
  }

  parseHighLevelPlanResponse(
    response: OpenAI.Chat.Completions.ChatCompletion,
    totalDays: number,
  ): string {
    if (!response) {
      throw new BadRequestException(
        GenerateTripError.FAILED_TO_GET_HIGH_LEVEL_PLAN_RESPONSE,
      );
    }

    if (
      !response ||
      !response.choices ||
      !response.choices[0] ||
      !response.choices[0].message ||
      !response.choices[0].message.content ||
      response.choices[0].message.content.trim() === ''
    ) {
      this.logger.error('Failed to get high level plan content');
      throw new BadRequestException(
        GenerateTripError.FAILED_TO_GET_HIGH_LEVEL_PLAN_CONTENT,
      );
    }

    const content = response.choices[0].message.content;

    try {
      const parsedContent = JSON.parse(content);

      if (!parsedContent.itinerary) {
        this.logger.error('Failed to get high level plan content');
        throw new BadRequestException(
          GenerateTripError.INCORRECT_HIGH_LEVEL_PLAN_LENGTH,
        );
      }

      if (!Array.isArray(parsedContent.itinerary)) {
        this.logger.error('Failed to get high level plan content');
        throw new BadRequestException(
          GenerateTripError.INCORRECT_HIGH_LEVEL_PLAN_LENGTH,
        );
      }

      if (Math.abs(parsedContent.itinerary.length - totalDays) > 1) {
        this.logger.error('Failed to get high level plan content');
        throw new BadRequestException(
          GenerateTripError.INCORRECT_HIGH_LEVEL_PLAN_LENGTH,
        );
      }

      // Validate trip_name is present
      if (!parsedContent.trip_name) {
        this.logger.warn(
          'Trip name not found in high level plan, using default name',
        );
      }
    } catch (error) {
      throw new BadRequestException(
        GenerateTripError.INCORRECT_HIGH_LEVEL_PLAN_LENGTH,
      );
    }

    return content;
  }

  /**
   * Extract the trip name from the high-level plan response
   * @param highLevelPlanContent High-level plan content as a string
   * @returns Trip name or null if not found
   */
  extractTripName(highLevelPlanContent: string): string | null {
    try {
      const parsedContent = JSON.parse(highLevelPlanContent);
      if (parsedContent.trip_name) {
        return parsedContent.trip_name;
      }
      return null;
    } catch (error) {
      this.logger.error(`Failed to extract trip name: ${error.message}`);
      return null;
    }
  }

  async parseSingleDayItinerary(
    response: OpenAI.Chat.Completions.ChatCompletion,
  ): Promise<any> {
    try {
      if (!response) {
        this.logger.error('No response received for single day itinerary');
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_GET_SINGLE_DAY_ITINERARY_RESPONSE,
        );
      }

      if (
        !response.choices ||
        !response.choices[0] ||
        !response.choices[0].message ||
        !response.choices[0].message.content ||
        response.choices[0].message.content.trim() === ''
      ) {
        this.logger.error('Invalid response format for single day itinerary');
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_GET_SINGLE_DAY_ITINERARY_CONTENT,
        );
      }

      const content = response.choices[0].message.content;
      this.logger.debug(
        `Received content for single day itinerary: ${content ? content.substring(0, 100) : 'null'}...`,
      );

      // Try to parse the JSON content
      let parsedContent: any;
      try {
        parsedContent = JSON.parse(content);
      } catch (parseError) {
        this.logger.error(
          `Failed to parse JSON content: ${parseError.message}`,
        );
        this.logger.debug(`Content that failed to parse: ${content}`);
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY,
        );
      }

      // Basic validation - check if we have the expected structure
      if (!parsedContent || typeof parsedContent !== 'object') {
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY,
        );
      }

      // If verification fails, try to extract useful information from the response
      if (!parsedContent.itinerary) {
        this.logger.debug('Attempting to recover from missing itinerary structure');
        // Log the structure to help with debugging
        this.logger.debug(
          `Parsed content structure: ${JSON.stringify(Object.keys(parsedContent))}`,
        );

        // Check if we can recover the structure
        if (this.canRecoverItineraryStructure(parsedContent)) {
          this.logger.debug('Attempting to recover itinerary structure');
          parsedContent = this.recoverItineraryStructure(parsedContent);
        } else {
          throw new BadRequestException(
            GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY,
          );
        }
      }

      // Set the city field for each activity based on the day's city
      if (
        parsedContent.itinerary &&
        Array.isArray(parsedContent.itinerary) &&
        parsedContent.itinerary.length > 0
      ) {
        const dayItinerary = parsedContent.itinerary[0];
        if (
          dayItinerary.city &&
          dayItinerary.activities &&
          Array.isArray(dayItinerary.activities)
        ) {
          // Extract city names from the day's city field
          const cityNames = this.extractCitiesFromString(dayItinerary.city);
          const primaryCity = cityNames[0]; // Use the first city as the primary one

          // Set the city field for each activity
          dayItinerary.activities.forEach((activity: any) => {
            // Skip if the activity already has a city field
            if (activity.city) {
              return;
            }

            activity.city = primaryCity;
          });
        }
      }

      return parsedContent;
    } catch (error) {
      // Log the detailed error for debugging
      this.logger.error(`Error in parseSingleDayItinerary: ${error.message}`);
      if (error instanceof BadRequestException) {
        // If it's already a BadRequestException, just rethrow it
        throw error;
      } else {
        // For other errors, wrap in a BadRequestException with a clear message
        throw new BadRequestException(
          GenerateTripError.FAILED_TO_PARSE_SINGLE_DAY_ITINERARY +
          (error.message ? `: ${error.message}` : ''),
        );
      }
    }
  }

  /**
   * Check if the itinerary structure can be recovered
   * @param content Parsed content to check
   * @returns boolean indicating if the structure can be recovered
   */
  private canRecoverItineraryStructure(content: any): boolean {
    // Check if we have any data that could be used to construct a valid itinerary
    if (!content) return false;

    // Check if we have a direct itinerary array
    if (Array.isArray(content)) {
      return content.length > 0 && content[0] && content[0].day && content[0].activities;
    }

    // Check if we have a nested itinerary structure
    if (content.itinerary) {
      return true;
    }

    // Check if we have a day object directly
    if (content.day && content.activities) {
      return true;
    }

    return false;
  }

  /**
   * Attempt to recover the itinerary structure from malformed data
   * @param content Parsed content to recover
   * @returns Recovered itinerary structure
   */
  private recoverItineraryStructure(content: any): any {
    this.logger.debug('Recovering itinerary structure');

    // Create a base structure
    const result: any = {
      itinerary: [],
      cost_breakdown: {
        activities: 0,
        meals: 0,
        total_estimated_cost: 0,
      },
      additional_tips: [],
    };

    try {
      // Case 1: If content is already an array, assume it's the itinerary array
      if (Array.isArray(content)) {
        result.itinerary = content;
      }
      // Case 2: If content has an itinerary property that's an array
      else if (content.itinerary && Array.isArray(content.itinerary)) {
        result.itinerary = content.itinerary;

        // Copy cost breakdown if available
        if (content.cost_breakdown) {
          result.cost_breakdown = content.cost_breakdown;
        }

        // Copy additional tips if available
        if (content.additional_tips && Array.isArray(content.additional_tips)) {
          result.additional_tips = content.additional_tips;
        }
      }
      // Case 3: If content has day and activities properties, wrap it in an array
      else if (content.day && content.activities) {
        result.itinerary = [content];
      }

      // Ensure the itinerary has at least one day
      if (result.itinerary.length === 0) {
        const today = new Date();
        result.itinerary = [
          {
            day: 1,
            date: today.toISOString().split('T')[0],
            city: 'Unknown',
            activities: [],
          },
        ];
      }

      this.logger.debug(
        `Recovered itinerary with ${result.itinerary.length} days`,
      );
      return result;
    } catch (e) {
      this.logger.error(`Failed to recover itinerary structure: ${e.message}`);
      // Return a minimal valid structure
      return {
        itinerary: [
          {
            day: 1,
            date: new Date().toISOString().split('T')[0],
            city: 'Unknown',
            activities: [],
          },
        ],
        cost_breakdown: {
          activities: 0,
          meals: 0,
          total_estimated_cost: 0,
        },
        additional_tips: ['Error recovering itinerary data'],
      };
    }
  }
}
