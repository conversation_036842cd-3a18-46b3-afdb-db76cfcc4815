import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { ConfigService } from '../../config/config.service';
import { CountryImage } from '../schemas/country-image.schema';

export interface CachedCountryImage {
  country: string;
  imageUrl: string;
  unsplashImageId?: string;
  altDescription?: string;
  description?: string;
  photographer?: string;
  photographerUsername?: string;
  source: string;
  lastUpdated: Date;
  expiresAt?: Date;
}

@Injectable()
export class CountryImageCacheService {
  private readonly logger = new Logger(CountryImageCacheService.name);
  private readonly cacheExpiryMs: number;

  constructor(
    @InjectModel(CountryImage.name)
    private readonly countryImageModel: Model<CountryImage>,
    private readonly configService: ConfigService,
  ) {
    // Get cache expiry from config (default 30 days)
    const expiryDays = this.configService.getUnsplashConfig().cacheExpiryDays || 30;
    this.cacheExpiryMs = expiryDays * 24 * 60 * 60 * 1000;
  }

  /**
   * Get cached image for a country
   * @param country Country name
   * @returns Cached image data or null if not found or expired
   */
  async getCachedImage(country: string): Promise<CachedCountryImage | null> {
    try {
      const normalizedCountry = this.normalizeCountryName(country);
      
      const cachedImage = await this.countryImageModel.findOne({
        country: normalizedCountry,
      }).exec();

      if (!cachedImage) {
        this.logger.debug(`No cached image found for country: ${normalizedCountry}`);
        return null;
      }

      // Check if cache has expired
      if (this.isCacheExpired(cachedImage)) {
        this.logger.debug(`Cached image expired for country: ${normalizedCountry}`);
        // Optionally delete expired cache entry
        await this.deleteCachedImage(normalizedCountry);
        return null;
      }

      this.logger.debug(`Found cached image for country: ${normalizedCountry}`);
      return {
        country: cachedImage.country,
        imageUrl: cachedImage.imageUrl,
        unsplashImageId: cachedImage.unsplashImageId,
        altDescription: cachedImage.altDescription,
        description: cachedImage.description,
        photographer: cachedImage.photographer,
        photographerUsername: cachedImage.photographerUsername,
        source: cachedImage.source,
        lastUpdated: cachedImage.lastUpdated,
        expiresAt: cachedImage.expiresAt,
      };
    } catch (error) {
      this.logger.error(`Error retrieving cached image for ${country}:`, error.message);
      return null;
    }
  }

  /**
   * Cache an image for a country
   * @param country Country name
   * @param imageData Image data to cache
   */
  async cacheImage(country: string, imageData: Omit<CachedCountryImage, 'country' | 'lastUpdated'>): Promise<void> {
    try {
      const normalizedCountry = this.normalizeCountryName(country);
      const expiresAt = new Date(Date.now() + this.cacheExpiryMs);

      await this.countryImageModel.findOneAndUpdate(
        { country: normalizedCountry },
        {
          ...imageData,
          country: normalizedCountry,
          lastUpdated: new Date(),
          expiresAt,
        },
        { upsert: true, new: true }
      ).exec();

      this.logger.debug(`Cached image for country: ${normalizedCountry}`);
    } catch (error) {
      this.logger.error(`Error caching image for ${country}:`, error.message);
    }
  }

  /**
   * Delete cached image for a country
   * @param country Country name
   */
  async deleteCachedImage(country: string): Promise<void> {
    try {
      const normalizedCountry = this.normalizeCountryName(country);
      await this.countryImageModel.deleteOne({ country: normalizedCountry }).exec();
      this.logger.debug(`Deleted cached image for country: ${normalizedCountry}`);
    } catch (error) {
      this.logger.error(`Error deleting cached image for ${country}:`, error.message);
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      const result = await this.countryImageModel.deleteMany({
        expiresAt: { $lt: new Date() }
      }).exec();

      if (result.deletedCount > 0) {
        this.logger.log(`Cleaned up ${result.deletedCount} expired cache entries`);
      }
    } catch (error) {
      this.logger.error('Error cleaning up expired cache:', error.message);
    }
  }

  /**
   * Scheduled cleanup of expired cache entries (runs daily at 2 AM)
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async scheduledCleanup(): Promise<void> {
    this.logger.debug('Running scheduled cleanup of expired country image cache');
    await this.cleanupExpiredCache();
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{ total: number; expired: number }> {
    try {
      const total = await this.countryImageModel.countDocuments().exec();
      const expired = await this.countryImageModel.countDocuments({
        expiresAt: { $lt: new Date() }
      }).exec();

      return { total, expired };
    } catch (error) {
      this.logger.error('Error getting cache stats:', error.message);
      return { total: 0, expired: 0 };
    }
  }

  /**
   * Get comprehensive dashboard statistics
   */
  async getDashboardStats(): Promise<{
    overview: { total: number; expired: number; active: number };
    recentlyAdded: Array<{ country: string; lastUpdated: Date; photographer?: string }>;
    popularCountries: Array<{ country: string; lastUpdated: Date }>;
    cacheHealth: { expiryDays: number; oldestEntry?: Date; newestEntry?: Date };
  }> {
    try {
      const total = await this.countryImageModel.countDocuments().exec();
      const expired = await this.countryImageModel.countDocuments({
        expiresAt: { $lt: new Date() }
      }).exec();
      const active = total - expired;

      // Get 10 most recently added countries
      const recentlyAdded = await this.countryImageModel
        .find({}, { country: 1, lastUpdated: 1, photographer: 1 })
        .sort({ lastUpdated: -1 })
        .limit(10)
        .exec();

      // Check popular countries (you can customize this list)
      const popularCountryNames = ['france', 'italy', 'spain', 'japan', 'usa', 'united states', 'uk', 'united kingdom', 'germany', 'brazil', 'australia', 'canada', 'mexico', 'thailand', 'greece'];
      const popularCountries = await this.countryImageModel
        .find(
          { country: { $in: popularCountryNames } },
          { country: 1, lastUpdated: 1 }
        )
        .sort({ lastUpdated: -1 })
        .exec();

      // Get oldest and newest entries
      const oldestEntry = await this.countryImageModel
        .findOne({}, { lastUpdated: 1 })
        .sort({ lastUpdated: 1 })
        .exec();

      const newestEntry = await this.countryImageModel
        .findOne({}, { lastUpdated: 1 })
        .sort({ lastUpdated: -1 })
        .exec();

      return {
        overview: { total, expired, active },
        recentlyAdded: recentlyAdded.map(item => ({
          country: item.country,
          lastUpdated: item.lastUpdated,
          photographer: item.photographer,
        })),
        popularCountries: popularCountries.map(item => ({
          country: item.country,
          lastUpdated: item.lastUpdated,
        })),
        cacheHealth: {
          expiryDays: Math.floor(this.cacheExpiryMs / (24 * 60 * 60 * 1000)),
          oldestEntry: oldestEntry?.lastUpdated,
          newestEntry: newestEntry?.lastUpdated,
        },
      };
    } catch (error) {
      this.logger.error('Error getting dashboard stats:', error.message);
      return {
        overview: { total: 0, expired: 0, active: 0 },
        recentlyAdded: [],
        popularCountries: [],
        cacheHealth: { expiryDays: 30 },
      };
    }
  }

  /**
   * Normalize country name for consistent caching
   * @param country Country name
   * @returns Normalized country name
   */
  private normalizeCountryName(country: string): string {
    return country.trim().toLowerCase();
  }

  /**
   * Check if cached image has expired
   * @param cachedImage Cached image document
   * @returns True if expired, false otherwise
   */
  private isCacheExpired(cachedImage: CountryImage): boolean {
    if (!cachedImage.expiresAt) {
      // If no expiry date, consider it expired if older than cache expiry time
      const ageMs = Date.now() - cachedImage.lastUpdated.getTime();
      return ageMs > this.cacheExpiryMs;
    }
    
    return cachedImage.expiresAt < new Date();
  }
}
