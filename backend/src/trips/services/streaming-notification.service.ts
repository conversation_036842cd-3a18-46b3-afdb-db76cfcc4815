/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { StreamingService } from './streaming.service';

@Injectable()
export class StreamingNotificationService {
  private readonly logger = new Logger(StreamingNotificationService.name);

  constructor(private readonly streamingService: StreamingService) {}

  /**
   * Send trip generation progress notification
   * @param tripId Trip ID
   * @param currentDay Current day being generated
   * @param totalDays Total days in trip
   * @param startTime Start time of generation (optional)
   */
  sendDayGenerationProgress(
    tripId: string,
    currentDay: number,
    totalDays: number,
    startTime?: Date,
  ): void {
    this.streamingService.sendDayGenerationProgress(
      tripId,
      currentDay,
      totalDays,
      startTime,
    );
  }

  /**
   * Send trip generation complete notification
   * @param tripId Trip ID
   * @param destination Trip destination
   * @param userId User ID
   * @param startTime Start time of generation (optional)
   */
  sendTripGenerationComplete(
    tripId: string,
    destination: string,
    userId: string,
    startTime?: Date,
  ): void {
    this.streamingService.sendTripGenerationComplete(
      tripId,
      destination,
      userId,
      startTime,
    );
  }

  /**
   * Send trip generation error notification
   * @param tripId Trip ID
   * @param currentDay Current day being generated
   * @param userId User ID
   * @param destination Trip destination
   * @param startTime Start time of generation (optional)
   */
  sendTripGenerationError(
    tripId: string,
    currentDay: number,
    userId: string,
    destination: string,
    startTime?: Date,
  ): void {
    this.logger.error(
      `Error generating itinerary for day ${currentDay} of trip ${tripId}`,
    );
    this.streamingService.sendTripGenerationError(
      tripId,
      currentDay,
      userId,
      destination,
      startTime,
    );
  }
}
