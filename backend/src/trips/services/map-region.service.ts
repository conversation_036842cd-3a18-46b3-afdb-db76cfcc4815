/* eslint-disable */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { LocationsService } from '../../locations/locations.service';
import { Activity } from '../interfaces/activity.interface';
import {
  DailyMapRegionResponse,
  MapRegion,
  TripMapRegionResponse,
} from '../interfaces/map-region.interface';
import { TripManagementService } from './trip-management.service';

@Injectable()
export class MapRegionService {
  private readonly logger = new Logger(MapRegionService.name);

  constructor(
    private readonly tripManagementService: TripManagementService,
    private readonly locationsService: LocationsService,
  ) { }

  /**
   * Calculate map region for a trip
   * @param tripId Trip ID
   * @returns Map region data
   */
  async calculateTripMapRegion(tripId: string): Promise<TripMapRegionResponse> {
    this.logger.log(`Calculating map region for trip ${tripId}`);

    // Get the trip
    const trip = await this.tripManagementService.findOne(tripId);
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${tripId} not found`);
    }

    // Try to get the country region based on the trip's destination
    try {
      const countryName = trip.tripDetails.destination;
      const countryRegions = await this.locationsService.getCountryRegions();
      const countryRegion = countryRegions.regions.find(
        (region) => region.name.toLowerCase() === countryName.toLowerCase(),
      );

      if (countryRegion) {
        return {
          mapRegion: {
            latitude: countryRegion.lat,
            longitude: countryRegion.lng,
            latitudeDelta: countryRegion.region.latitudeDelta,
            longitudeDelta: countryRegion.region.longitudeDelta,
          },
          tripId,
        };
      }
    } catch (error) {
      this.logger.error(`Error getting country region: ${error.message}`);
    }

    // Fallback to calculating based on activities
    const allActivities = trip.itinerary && Array.isArray(trip.itinerary)
      ? trip.itinerary.flatMap((day) => day && day.activities && Array.isArray(day.activities) ? day.activities : [])
      : [];
    const mapRegion = this.adjustMapToShowActivities(allActivities);

    if (mapRegion) {
      return { mapRegion, tripId };
    }

    // Fallback to default region if no valid coordinates found
    return {
      mapRegion: {
        latitude: 30,
        longitude: 0,
        latitudeDelta: 180,
        longitudeDelta: 180,
      },
      tripId,
    };
  }

  /**
   * Calculate map region for a specific day of a trip
   * @param tripId Trip ID
   * @param dayNumber Day number
   * @returns Map region data
   */
  async calculateDailyMapRegion(
    tripId: string,
    dayNumber: number,
  ): Promise<DailyMapRegionResponse> {
    this.logger.log(
      `Calculating map region for day ${dayNumber} of trip ${tripId}`,
    );

    // Get the trip
    const trip = await this.tripManagementService.findOne(tripId);
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${tripId} not found`);
    }

    // Check if itinerary exists and is an array
    if (!trip.itinerary || !Array.isArray(trip.itinerary)) {
      throw new NotFoundException(
        `No itinerary found for trip ${tripId}`,
      );
    }

    // Get the day's activities
    const dayItinerary = trip.itinerary.find((day) => day && day.day === dayNumber);
    if (!dayItinerary) {
      throw new NotFoundException(
        `Day ${dayNumber} not found in trip ${tripId}`,
      );
    }

    // Calculate map region based on the day's activities
    const activities = dayItinerary.activities && Array.isArray(dayItinerary.activities) ? dayItinerary.activities : [];
    const mapRegion = this.adjustMapToShowActivities(activities);

    if (mapRegion) {
      return { mapRegion, dayNumber, tripId };
    }

    // Fallback to trip-level map region if no valid coordinates found for the day
    const tripMapRegion = await this.calculateTripMapRegion(tripId);
    return {
      mapRegion: tripMapRegion.mapRegion,
      dayNumber,
      tripId,
    };
  }

  /**
   * Calculate map region for a specific city of a trip
   * @param tripId Trip ID
   * @param city City name
   * @param dayNumber Optional day number
   * @returns Map region data
   */
  async calculateCityMapRegion(
    tripId: string,
    city: string,
    dayNumber?: number,
  ): Promise<TripMapRegionResponse> {
    this.logger.log(
      `Calculating map region for city ${city} of trip ${tripId}`,
    );

    // Get the trip
    const trip = await this.tripManagementService.findOne(tripId);
    if (!trip) {
      throw new NotFoundException(`Trip with ID ${tripId} not found`);
    }

    // Check if itinerary exists and is an array
    if (!trip.itinerary || !Array.isArray(trip.itinerary)) {
      // Fallback to trip-level map region if no itinerary found
      const tripMapRegion = await this.calculateTripMapRegion(tripId);
      return {
        mapRegion: tripMapRegion.mapRegion,
        tripId,
      };
    }

    // Get the city's activities
    const cityActivities = trip.itinerary
      .filter((day) => day && (!dayNumber || day.day === dayNumber))
      .flatMap((day) => day && day.activities && Array.isArray(day.activities) ? day.activities : [])
      .filter(
        (activity) =>
          activity && activity.city?.trim()?.toLowerCase() === city?.trim()?.toLowerCase(),
      )
      .filter((activity) => activity);

    // Calculate map region based on the city's activities
    const mapRegion = this.adjustMapToShowActivities(cityActivities);

    if (mapRegion) {
      return { mapRegion, tripId };
    }

    // Fallback to trip-level map region if no valid coordinates found for the city
    const tripMapRegion = await this.calculateTripMapRegion(tripId);
    return {
      mapRegion: tripMapRegion.mapRegion,
      tripId,
    };
  }

  /**
   * Adjusts the map region to show all activities
   * @param activities Array of activities
   * @returns Map region data
   */
  private adjustMapToShowActivities(activities: Activity[]): MapRegion | null {
    if (activities.length === 0) return null;

    const validCoordinates = activities
      .map((a) => a.coordinates)
      .filter(
        (coord): coord is { lat: number; lng: number } =>
          coord !== undefined &&
          typeof coord?.lat === 'number' &&
          typeof coord?.lng === 'number',
      );

    if (validCoordinates.length === 0) return null;

    // Handle single activity case
    if (validCoordinates.length === 1) {
      const coord = validCoordinates[0];
      return {
        latitude: coord.lat,
        longitude: coord.lng,
        latitudeDelta: 0.01, // Tight zoom for single activity
        longitudeDelta: 0.01,
      };
    }

    const minLat = Math.min(...validCoordinates.map((c) => c.lat));
    const maxLat = Math.max(...validCoordinates.map((c) => c.lat));
    const minLng = Math.min(...validCoordinates.map((c) => c.lng));
    const maxLng = Math.max(...validCoordinates.map((c) => c.lng));

    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;

    // Calculate the span of coordinates
    const latSpan = maxLat - minLat;
    const lngSpan = maxLng - minLng;

    // Use minimal padding since client-side fitBounds will handle the perfect fit
    const paddingMultiplier = 1.1; // Minimal padding for initial region

    // Ensure minimum span for very close activities
    const minSpan = 0.002; // ~200 meters minimum span
    const deltaLat = Math.max(latSpan * paddingMultiplier, minSpan);
    const deltaLng = Math.max(lngSpan * paddingMultiplier, minSpan);

    return {
      latitude: centerLat,
      longitude: centerLng,
      latitudeDelta: deltaLat,
      longitudeDelta: deltaLng,
    };
  }
}
