/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '../../config/config.service';
import { NotificationType, StreamGateway } from '../../gateways/stream.gateway';
import { UserPreferencesService } from '../../users/services/user-preferences.service';
import { TripStatus } from '../helpers/trip-status.helper';

@Injectable()
export class StreamingService {
  private readonly useStreaming: boolean;
  private readonly logger = new Logger(StreamingService.name);

  constructor(
    private readonly streamGateway: StreamGateway,
    private readonly configService: ConfigService,
    private readonly userPreferencesService: UserPreferencesService,
  ) {
    // Use the new useWebsocket flag for socket notifications
    this.useStreaming =
      this.configService.getWebsocketConfig().useWebsocket || false;

    // For backward compatibility, also check useSockets if useWebsocket is not set
    if (!this.useStreaming) {
      this.useStreaming =
        this.configService.getWebsocketConfig().useSockets || false;
    }

    this.logger.log(
      `WebSocket notifications are ${this.useStreaming ? 'enabled' : 'disabled'}`,
    );
  }

  /**
   * Check if a notification type is enabled for a user
   * @param userId User ID
   * @param notificationType Notification type to check
   * @returns Promise resolving to boolean indicating if notification is enabled
   */
  private async isNotificationEnabled(
    userId: string,
    notificationType: string,
  ): Promise<boolean> {
    try {
      return await this.userPreferencesService.isNotificationEnabled(
        userId,
        notificationType,
      );
    } catch (error) {
      this.logger.error(
        `Error checking notification preferences for user ${userId}: ${error.message}`,
      );
      // Default to true if there's an error checking preferences
      return true;
    }
  }

  /**
   * Send a stream chunk for day generation progress
   * @param tripId Trip ID
   * @param currentDay Current day being generated
   * @param totalDays Total days to generate
   * @param startTime Start time of generation
   * @param additionalData Optional additional data to include in the stream chunk
   */
  sendDayGenerationProgress(
    tripId: string,
    currentDay: number,
    totalDays: number,
    startTime: Date = new Date(),
    additionalData?: { [key: string]: any },
  ): void {
    if (!this.useStreaming) {
      this.logger.warn(
        `Streaming is disabled, not sending day generation progress for trip ${tripId}`,
      );
      return;
    }

    this.logger.log(
      `Sending day generation progress for trip ${tripId}: day ${currentDay}/${totalDays}`,
    );

    this.streamGateway.sendStreamChunk(tripId, {
      type: TripStatus.IN_PROGRESS,
      content: `Generating itinerary for day ${currentDay}...`,
      progress: Math.min(currentDay / totalDays, 1) * 100,
      elapsedTime: new Date().getTime() - startTime.getTime(),
      notificationType: NotificationType.INFO,
      // Include any additional data if provided
      ...additionalData,
    });
  }

  /**
   * Send a stream chunk for trip generation completion
   * @param tripId Trip ID
   * @param destination Trip destination
   * @param userId User ID
   * @param startTime Start time of generation
   * @param additionalData Optional additional data to include in the stream chunk and notification
   */
  async sendTripGenerationComplete(
    tripId: string,
    destination: string,
    userId: string,
    startTime: Date = new Date(),
    additionalData?: { [key: string]: any },
  ): Promise<void> {
    if (!this.useStreaming) {
      this.logger.warn(
        `Streaming is disabled, not sending trip generation complete for trip ${tripId}`,
      );
      return;
    }

    this.logger.log(
      `Sending trip generation complete for trip ${tripId} to user ${userId}`,
    );

    // Always send stream chunk to the trip room
    this.streamGateway.sendStreamChunk(tripId, {
      type: TripStatus.READY,
      content: `Itinerary generated successfully`,
      elapsedTime: new Date().getTime() - startTime.getTime(),
      notificationType: NotificationType.SUCCESS,
      // Include any additional data if provided
      ...additionalData,
    });

    // Check if trip_updates notifications are enabled for this user
    const notificationsEnabled = await this.isNotificationEnabled(
      userId,
      'trip_updates',
    );

    if (notificationsEnabled) {
      this.logger.log(
        `Sending notification to user ${userId} for trip ${tripId} completion`,
      );
      this.streamGateway.sendNotification(userId, {
        title: 'Trip Generation Complete',
        body: `Your trip to ${destination} has been generated successfully!`,
        type: NotificationType.SUCCESS,
        // Include any additional data if provided
        data: additionalData,
      });
    } else {
      this.logger.log(
        `Trip update notifications disabled for user ${userId}, skipping notification`,
      );
    }
  }

  /**
   * Send a stream chunk for trip generation error
   * @param tripId Trip ID
   * @param day Day that failed
   * @param userId User ID
   * @param destination Trip destination
   * @param startTime Start time of generation
   * @param errorDetails Optional additional error details
   */
  async sendTripGenerationError(
    tripId: string,
    day: number,
    userId: string,
    destination: string,
    startTime: Date = new Date(),
    errorDetails?: { [key: string]: any },
  ): Promise<void> {
    if (!this.useStreaming) {
      this.logger.warn(
        `Streaming is disabled, not sending trip generation error for trip ${tripId}`,
      );
      return;
    }

    this.logger.log(
      `Sending trip generation error for trip ${tripId}, day ${day} to user ${userId}`,
    );

    // Always send stream chunk to the trip room
    this.streamGateway.sendStreamChunk(tripId, {
      type: TripStatus.ERROR,
      content: `Error generating itinerary for day ${day}, press continue to try again`,
      elapsedTime: new Date().getTime() - startTime.getTime(),
      notificationType: NotificationType.ERROR,
      // Include any additional error details if provided
      ...errorDetails,
    });

    // Check if trip_updates notifications are enabled for this user
    const notificationsEnabled = await this.isNotificationEnabled(
      userId,
      'trip_updates',
    );

    if (notificationsEnabled) {
      this.logger.log(
        `Sending error notification to user ${userId} for trip ${tripId}`,
      );
      this.streamGateway.sendNotification(userId, {
        title: 'Trip Generation Failed',
        body: `There was an error generating your trip to ${destination}. Please try again.`,
        type: NotificationType.ERROR,
        // Include any additional error details if provided
        data: errorDetails,
      });
    } else {
      this.logger.log(
        `Trip update notifications disabled for user ${userId}, skipping error notification`,
      );
    }
  }

  /**
   * Send a custom stream chunk
   * @param tripId Trip ID
   * @param type Stream type
   * @param content Stream content
   * @param notificationType Notification type
   * @param additionalData Optional additional data to include in the stream chunk
   */
  sendCustomStreamChunk(
    tripId: string,
    type: string,
    content: string,
    notificationType: NotificationType,
    additionalData?: { [key: string]: any },
  ): void {
    if (!this.useStreaming) {
      this.logger.warn(
        `Streaming is disabled, not sending custom stream chunk for trip ${tripId}`,
      );
      return;
    }

    this.logger.log(
      `Sending custom stream chunk for trip ${tripId}: ${type} - ${content}`,
    );

    this.streamGateway.sendStreamChunk(tripId, {
      type,
      content,
      notificationType,
      // Include any additional data if provided
      ...additionalData,
    });
  }

  /**
   * Send a custom notification
   * @param userId User ID
   * @param title Notification title
   * @param body Notification body
   * @param type Notification type
   * @param notificationType Notification preference type
   * @param additionalData Optional additional data to include in the notification
   */
  async sendCustomNotification(
    userId: string,
    title: string,
    body: string,
    type: NotificationType,
    notificationType: string = 'trip_updates',
    additionalData?: { [key: string]: any },
  ): Promise<void> {
    if (!this.useStreaming) return;

    // Map notification types to preference keys
    let preferenceKey = notificationType;
    if (type === NotificationType.INSUFFICIENT_BALANCE) {
      preferenceKey = 'credit_balance';
    }

    // Check if this notification type is enabled for the user
    const notificationsEnabled = await this.isNotificationEnabled(
      userId,
      preferenceKey,
    );

    if (notificationsEnabled) {
      this.logger.log(
        `Sending custom notification to user ${userId}: ${title}`,
      );
      this.streamGateway.sendNotification(userId, {
        title,
        body,
        type,
        // Include any additional data if provided
        data: additionalData,
      });
    } else {
      this.logger.log(
        `${preferenceKey} notifications disabled for user ${userId}, skipping notification`,
      );
    }
  }

  /**
   * Send an insufficient balance notification to the user
   * @param userId User ID
   * @param tripId Optional trip ID
   * @param currentBalance Current user balance
   * @param requiredTokens Required tokens for the operation
   */
  async sendInsufficientBalanceNotification(
    userId: string,
    tripId?: string,
    currentBalance?: number,
    requiredTokens?: number,
  ): Promise<void> {
    if (!this.useStreaming) {
      this.logger.warn(
        `Streaming is disabled, not sending insufficient balance notification for user ${userId}`,
      );
      return;
    }

    this.logger.log(
      `Sending insufficient balance notification to user ${userId}. Available: ${currentBalance}, Required: ${requiredTokens}`,
    );

    // Send stream chunk if tripId is provided (always send to trip room)
    if (tripId) {
      this.streamGateway.sendStreamChunk(tripId, {
        type: TripStatus.INSUFFICIENT_BALANCE, // Use specific insufficient balance type
        content: `Not enough days to generate trip. Please add more days to continue.`,
        notificationType: NotificationType.INSUFFICIENT_BALANCE,
        // Always include balance information in the stream chunk for structured handling
        available: currentBalance ?? 0,
        needed: requiredTokens ?? 0,
        requiredTokens: requiredTokens ?? 0, // Legacy field for backward compatibility
        // Include elapsed time for consistency with other messages
        elapsedTime: new Date().getTime(),
      });
    }

    // Always send to the user room regardless of tripId
    // This ensures the user gets the notification even when there's no tripId
    // Check if credit_balance notifications are enabled for this user
    const notificationsEnabled = await this.isNotificationEnabled(
      userId,
      'credit_balance',
    );

    if (notificationsEnabled) {
      // Prepare the notification message with structured data
      let body = "You don't have enough credits to perform this action.";

      // Add balance details if available
      if (currentBalance !== undefined && requiredTokens !== undefined) {
        body += ` Available: ${currentBalance}, Needed: ${requiredTokens}.`;
      }

      body += ' Please add more credits to continue.';

      // Send notification to user
      this.logger.log(
        `Sending insufficient balance notification to user ${userId}`,
      );
      this.streamGateway.sendNotification(userId, {
        title: 'Not Enough Credits',
        body,
        type: NotificationType.INSUFFICIENT_BALANCE,
        // Include structured data for consistent client handling
        data: {
          available: currentBalance ?? 0,
          needed: requiredTokens ?? 0,
          requiredTokens: requiredTokens ?? 0, // Legacy field for backward compatibility
        },
      });
    } else {
      this.logger.log(
        `Credit balance notifications disabled for user ${userId}, skipping notification`,
      );
    }
  }
}
