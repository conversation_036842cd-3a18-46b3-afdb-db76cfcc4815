/* eslint-disable */

import { Injectable } from '@nestjs/common';
import {
  AiModel as ConfigAiModel,
  GenerationMode,
} from '../../config/ai.config';
import { ConfigService } from '../../config/config.service';
import { AiModel } from '../helpers/ai-model.type';

@Injectable()
export class ItineraryConfigService {
  private readonly generationMode: GenerationMode;
  private readonly modelToUse: AiModel;
  private readonly maxDaysToGenerate: number;
  private readonly useStreaming: boolean; // For AI streaming only
  private readonly useWebsocket: boolean; // For WebSocket notifications
  private readonly useAssistantsApi: boolean;

  constructor(private readonly configService: ConfigService) {
    const aiConfig = this.configService.getAiConfig();
    const generationConfig = this.configService.getGenerationConfig();
    const websocketConfig = this.configService.getWebsocketConfig();

    this.generationMode =
      generationConfig.generationMode || GenerationMode.MULTI_ROUND;
    this.modelToUse = this.mapConfigModelToAiModel(
      generationConfig.modelToUse || ConfigAiModel.GROQ,
    );
    this.maxDaysToGenerate = generationConfig.maxDaysToGenerate || 2;
    this.useStreaming = generationConfig.useStreaming || false; // For AI streaming only
    this.useWebsocket = websocketConfig.useWebsocket || false; // For WebSocket notifications
    this.useAssistantsApi = aiConfig.useAssistantsApi || false;
  }

  private mapConfigModelToAiModel(model: string): AiModel {
    switch (model) {
      case ConfigAiModel.OPENAI:
        return AiModel.OPENAI;
      case ConfigAiModel.GROQ:
        return AiModel.GROQ;
      case ConfigAiModel.DEEPSEEK:
        return AiModel.DEEPSEEK;
      default:
        return AiModel.GROQ;
    }
  }

  getGenerationMode(): GenerationMode {
    return this.generationMode;
  }

  getModelToUse(): AiModel {
    return this.modelToUse;
  }

  getMaxDaysToGenerate(): number {
    return this.maxDaysToGenerate;
  }

  isStreamingEnabled(): boolean {
    return this.useStreaming;
  }

  isWebsocketEnabled(): boolean {
    return this.useWebsocket;
  }

  isAssistantsApiEnabled(): boolean {
    return this.useAssistantsApi;
  }
}
