import {
  IsString,
  IsDate,
  IsArray,
  IsNumber,
  IsEnum,
  IsNotEmpty,
  Min,
  Max,
  IsO<PERSON>al,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum TripType {
  LUXURY = 'Luxury',
  BUDGET = 'Budget',
  CULTURAL = 'Cultural',
  ADVENTURE = 'Adventure',
  FAMILY = 'Family',
  ROMANTIC = 'Romantic',
  BUSINESS = 'Business',
  SOLO = 'Solo',
  ROADTRIP = 'Roadtrip',
}

export enum BudgetType {
  BUDGET = 'budget',
  NORMAL = 'normal',
  LUXURY = 'luxury',
}



export class GenerateTripDto {
  @ApiProperty({ description: 'Country of arrival' })
  @IsNotEmpty()
  @IsString()
  destination: string;

  @ApiProperty({ description: 'City of arrival' })
  @IsNotEmpty()
  @IsString()
  arrivalCity: string;

  @ApiProperty({ description: 'City of departure' })
  @IsNotEmpty()
  @IsString()
  departureCity: string;

  @ApiProperty({ description: 'Start date of the trip' })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @ApiProperty({ description: 'End date of the trip' })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @ApiProperty({ description: 'Arrival time (legacy field, not used)', required: false })
  @IsOptional()
  @IsString()
  arrivalTime?: string;

  @ApiProperty({ description: 'Departure time (legacy field, not used)', required: false })
  @IsOptional()
  @IsString()
  departureTime?: string;

  @ApiProperty({ description: 'Arrival mode', enum: ['Air', 'Land', 'Sea'], required: false })
  @IsOptional()
  @IsEnum(['Air', 'Land', 'Sea'])
  arrivalMode?: 'Air' | 'Land' | 'Sea';

  @ApiProperty({ description: 'Departure mode', enum: ['Air', 'Land', 'Sea'], required: false })
  @IsOptional()
  @IsEnum(['Air', 'Land', 'Sea'])
  departureMode?: 'Air' | 'Land' | 'Sea';


  @ApiProperty({
    description: 'Type of trip (Budget, Luxury, Midrange)',
    enum: TripType,
  })
  @IsNotEmpty()
  @IsEnum(TripType)
  tripType: TripType;

  @ApiProperty({ description: 'Budget category for the trip', enum: BudgetType })
  @IsNotEmpty()
  @IsEnum(BudgetType)
  budget: BudgetType;

  @ApiProperty({
    description: 'Intensity level of the trip',
    minimum: 1,
    maximum: 10,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(10)
  intensity: number;

  @ApiProperty({
    description: 'List of interests for the trip',
    type: [String],
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  interests: string[];

  @ApiProperty({
    description: 'Preferred cuisine types',
    required: false,
    type: [String],
    default: ['Local', 'Traditional', 'International'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  cuisinePreferences?: string[] = ['Local', 'Traditional', 'International'];

  @ApiProperty({
    description: 'List of cities that must be included in the itinerary',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  mustVisitCities?: string[];

  @ApiProperty({
    description: 'Additional requirements or preferences for the trip',
    required: false,
  })
  @IsOptional()
  @IsString()
  additionalRequirements?: string;

  @ApiProperty({ description: 'Daily wake up time in HH:MM format' })
  @IsNotEmpty()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Time must be in HH:MM format',
  })
  wakeUpTime: string;

  @ApiProperty({ description: 'Daily sleep time in HH:MM format' })
  @IsNotEmpty()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Time must be in HH:MM format',
  })
  sleepTime: string;

  @ApiProperty({ description: 'Number of adults on the trip' })
  @IsNotEmpty()
  @IsNumber()
  adults: number;

  @ApiProperty({ description: 'Number of children on the trip' })
  @IsNotEmpty()
  @IsNumber()
  children: number;
}
