import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { BudgetType } from './generate-trip.dto';

class CoordinatesDto {
  @ApiProperty({ description: 'Latitude coordinate' })
  @IsNumber()
  lat: number;

  @ApiProperty({ description: 'Longitude coordinate' })
  @IsNumber()
  lng: number;
}

class ActivityDto {
  @ApiProperty({ description: 'Unique identifier for the activity' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'Type of activity', required: false })
  @IsString()
  type?: string;

  @ApiProperty({ description: 'Name of the activity', required: false })
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Description of the activity', required: false })
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Location of the activity', required: false })
  @IsString()
  location?: string;

  @ApiProperty({ description: 'Start time of the activity', required: false })
  @IsString()
  startTime?: string;

  @ApiProperty({ description: 'End time of the activity', required: false })
  @IsString()
  endTime?: string;

  @ApiProperty({ description: 'Cost of the activity', required: false })
  @IsNumber()
  cost?: number;

  @ApiProperty({ description: 'Icon for the activity', required: false })
  @IsString()
  icon?: string;

  @ApiProperty({
    description: 'Coordinates of the activity',
    required: false,
    type: () => CoordinatesDto,
  })
  @ValidateNested()
  @Type(() => CoordinatesDto)
  coordinates?: CoordinatesDto;

  @ApiProperty({ description: 'Image URL for the activity', required: false })
  @IsString()
  imageUrl?: string;

  @ApiProperty({ description: 'Source of the activity data', required: false })
  @IsString()
  source?: string;

  @ApiProperty({ description: 'Rating of the activity', required: false })
  @IsNumber()
  rating?: number;

  @ApiProperty({
    description: 'Tags associated with the activity',
    required: false,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Duration of the activity', required: false })
  @IsString()
  duration?: string;

  @ApiProperty({
    description: 'Type of meal if activity is food-related',
    required: false,
  })
  @IsString()
  mealType?: string;

  @ApiProperty({
    description: 'Restaurant name if activity is food-related',
    required: false,
  })
  @IsString()
  restaurant?: string;



  @ApiProperty({
    description: 'City where the activity takes place',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  city: string;
}

class DailyItineraryDto {
  @ApiProperty({ description: 'Day number of the itinerary' })
  @IsNumber()
  @IsNotEmpty()
  day: number;

  @ApiProperty({ description: 'Date of the itinerary' })
  @IsString()
  @IsNotEmpty()
  date: string;



  @ApiProperty({
    description: 'List of activities for the day',
    type: [ActivityDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActivityDto)
  activities: ActivityDto[];
}

class TripDetailsDto {
  @ApiProperty({ description: 'Destination of the trip', example: 'Paris' })
  @IsString()
  @IsNotEmpty()
  destination: string;

  @ApiProperty({ description: 'City of arrival', required: false })
  @IsOptional()
  @IsString()
  arrivalCity?: string;

  @ApiProperty({ description: 'City of departure', required: false })
  @IsOptional()
  @IsString()
  departureCity?: string;

  @ApiProperty({ description: 'Start date of the trip', example: '2024-01-01' })
  @IsString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({ description: 'End date of the trip', example: '2024-01-05' })
  @IsString()
  @IsNotEmpty()
  endDate: string;

  @ApiProperty({ description: 'Total number of days for the trip', example: 5 })
  @IsNumber()
  @IsNotEmpty()
  totalDays: number;

  @ApiProperty({ description: 'Budget category for the trip', enum: BudgetType, example: 'normal' })
  @IsEnum(BudgetType)
  @IsNotEmpty()
  budget: BudgetType;

  @ApiProperty({
    description: 'Intensity level of the trip (1-10)',
    example: 5,
  })
  @IsNumber()
  @IsNotEmpty()
  intensity: number;

  @ApiProperty({
    description: 'Type of travel (e.g., leisure, business)',
    example: 'leisure',
  })
  @IsString()
  @IsNotEmpty()
  tripType: string;

  @ApiProperty({ description: 'Arrival time (legacy field, not used)', required: false })
  @IsOptional()
  @IsString()
  arrivalTime?: string;

  @ApiProperty({ description: 'Departure time (legacy field, not used)', required: false })
  @IsOptional()
  @IsString()
  departureTime?: string;

  @ApiProperty({ description: 'Arrival mode', enum: ['Air', 'Land', 'Sea'], required: false })
  @IsOptional()
  @IsEnum(['Air', 'Land', 'Sea'])
  arrivalMode?: 'Air' | 'Land' | 'Sea';

  @ApiProperty({ description: 'Departure mode', enum: ['Air', 'Land', 'Sea'], required: false })
  @IsOptional()
  @IsEnum(['Air', 'Land', 'Sea'])
  departureMode?: 'Air' | 'Land' | 'Sea';

  @ApiProperty({ description: 'Daily wake up time (HH:MM)', example: '07:00' })
  @IsString()
  @IsNotEmpty()
  wakeUpTime: string;

  @ApiProperty({ description: 'Daily sleep time (HH:MM)', example: '23:00' })
  @IsString()
  @IsNotEmpty()
  sleepTime: string;

  @ApiProperty({
    description: 'Preferred cuisine types',
    required: false,
    type: [String],
    example: ['Local', 'Traditional', 'International'],
  })
  @IsArray()
  @IsString({ each: true })
  cuisinePreferences?: string[];

  @ApiProperty({
    description: 'Must visit cities',
    required: false,
    type: [String],
    example: ['Paris', 'London', 'New York'],
  })
  @IsArray()
  @IsString({ each: true })
  mustVisitCities?: string[];

  @ApiProperty({ description: 'Additional requirements', required: false })
  @IsString()
  additionalRequirements?: string;

  @ApiProperty({
    description: 'Interests',
    required: false,
    type: [String],
    example: ['Travel', 'Entertainment', 'Fitness'],
  })
  @IsArray()
  @IsString({ each: true })
  interests?: string[];

  @ApiProperty({ description: 'Number of people', example: 1 })
  @IsNumber()
  people: {
    adults: number;
    children: number;
  };

  @ApiProperty({ description: 'Image URL', required: false })
  @IsString()
  imageUrl?: string | null;
}

class CostBreakdownDto {
  @ApiProperty({ description: 'Total activities cost', required: false })
  @IsNumber()
  activities?: number;

  @ApiProperty({ description: 'Total meals cost', required: false })
  @IsNumber()
  meals?: number;

  @ApiProperty({
    description: 'Total estimated cost for the trip',
    required: false,
  })
  @IsNumber()
  total_estimated_cost?: number;
}

class DayProgressDto {
  @ApiProperty({ description: 'Day number' })
  @IsNumber()
  @IsNotEmpty()
  day: number;

  @ApiProperty({ description: 'Is generating' })
  @IsBoolean()
  @IsNotEmpty()
  is_generating: boolean;

  @ApiProperty({ description: 'Tries', required: false, default: 0 })
  @IsNumber()
  tries: number;

  @ApiProperty({ description: 'Started at', required: false })
  @IsDate()
  started_at: Date | null;

  @ApiProperty({ description: 'Finished at', required: false })
  @IsDate()
  finished_at: Date | null;

  @ApiProperty({ description: 'Error', required: false })
  @IsString()
  error: string | null;
}
export class CreateTripDto {
  @ApiProperty({ description: 'Name of the trip' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Trip details', type: () => TripDetailsDto })
  @ValidateNested()
  @Type(() => TripDetailsDto)
  @IsNotEmpty()
  tripDetails: TripDetailsDto;

  @ApiProperty({ description: 'Daily itineraries', type: [DailyItineraryDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DailyItineraryDto)
  itinerary: DailyItineraryDto[];

  @ApiProperty({ description: 'Current status of the trip' })
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty({ description: 'Cost breakdown', type: () => CostBreakdownDto })
  @ValidateNested()
  @Type(() => CostBreakdownDto)
  @IsNotEmpty()
  costBreakdown: CostBreakdownDto;

  @ApiProperty({ description: 'Additional tips for the trip', type: [String] })
  @IsArray()
  @IsString({ each: true })
  additionalTips: string[];

  @ApiProperty({ description: 'Current processing buffer' })
  @IsString()
  @IsNotEmpty()
  currentBuffer: string;

  @ApiProperty({ description: 'Current day progress' })
  @IsNumber()
  @IsNotEmpty()
  currentDayProgress: number;

  @ApiProperty({ description: 'Number of retries' })
  @IsNumber()
  @IsNotEmpty()
  retryCount: number;

  @ApiProperty({ description: 'Last retry date' })
  @IsDate()
  @IsNotEmpty()
  lastRetryDate: Date;

  @ApiProperty({
    description: 'Days progress',
    type: [DayProgressDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DayProgressDto)
  daysProgress: DayProgressDto[];

  @ApiProperty({ description: 'User ID' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'High level plan prompt', required: false })
  @IsString()
  highLevelPlanPrompt: string;

  @ApiProperty({
    description: 'OpenAI thread ID for Assistants API',
    required: false,
  })
  @IsString()
  thread_id?: string;

  @ApiProperty({
    description: 'Previous response ID for Assistants API',
    required: false,
  })
  @IsString()
  previous_response_id?: string;

  @ApiProperty({
    description: 'Whether this trip is an example visible to all users',
    required: false,
    default: false,
  })
  @IsBoolean()
  isExample?: boolean;
}
