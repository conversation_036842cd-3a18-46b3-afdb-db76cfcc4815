import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional, IsEnum } from 'class-validator';

export enum PriceRange {
  Budget = 'Budget',
  Moderate = 'Moderate',
  Luxury = 'Luxury',
}

export class GenerateRestaurantsDto {
  @ApiProperty({ description: 'Country where restaurants will be generated' })
  @IsString()
  country: string;

  @ApiProperty({ description: 'City to find restaurants in' })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Price range (Budget, Moderate, Luxury)',
    enum: PriceRange,
  })
  @IsEnum(PriceRange)
  priceRange: PriceRange;

  @ApiProperty({ description: 'Preferred cuisines', type: [String] })
  @IsArray()
  cuisinePreferences: string[];

  @ApiProperty({
    description: 'Additional requirements or preferences',
    required: false,
  })
  @IsString()
  @IsOptional()
  additionalRequirements?: string;
  @ApiProperty({
    description: 'Dietary restrictions',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  dietaryRestrictions?: string[];
}
