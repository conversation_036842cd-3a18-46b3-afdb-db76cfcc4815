import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsArray,
  IsOptional,
  IsEnum,
  Min,
  Max,
} from 'class-validator';
import { PriceRange } from './generate-restaurants.dto';

export class GenerateHotelsDto {
  @ApiProperty({ description: 'Country where hotels will be generated' })
  @IsString()
  country: string;

  @ApiProperty({ description: 'City to find hotels in' })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Price range (Budget, Moderate, Luxury)',
    enum: PriceRange,
  })
  @IsEnum(PriceRange)
  priceRange: PriceRange;

  @ApiProperty({
    description: 'Minimum star rating (1-5)',
    minimum: 1,
    maximum: 5,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  starRating: number;

  @ApiProperty({ description: 'Required amenities', type: [String] })
  @IsArray()
  amenities: string[];

  @ApiProperty({
    description: 'Additional requirements or preferences',
    required: false,
  })
  @IsString()
  @IsOptional()
  additionalRequirements?: string;

  @ApiProperty({ description: 'Required amenities', type: [String] })
  @IsArray()
  requiredAmenities: string[];
}
