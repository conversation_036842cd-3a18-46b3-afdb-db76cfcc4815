import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  Min,
  Max,
} from 'class-validator';
import { TripType } from './generate-trip.dto';

export class GenerateActivitiesDto {
  @ApiProperty({ description: 'Country where activities will be generated' })
  @IsString()
  country: string;

  @ApiProperty({ description: 'City to generate activities for' })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Type of travel (Budget, Moderate, Luxury)',
    enum: TripType,
  })
  @IsEnum(TripType)
  travelType: TripType;

  @ApiProperty({
    description: 'Activity intensity level (1-10)',
    minimum: 1,
    maximum: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(10)
  intensity: number;

  @ApiProperty({
    description: 'Additional requirements or preferences',
    required: false,
  })
  @IsString()
  @IsOptional()
  additionalRequirements?: string;
}
