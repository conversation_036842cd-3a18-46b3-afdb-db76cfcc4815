/* eslint-disable */

import { AiModel } from '../helpers/ai-model.type';

export interface ToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
}

export interface Message {
  role: string;
  content: string;
  tool_calls?: ToolCall[];
}

export interface CompletionOptions {
  messages: Message[];
  model: string;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
  response_format?: any;
}

export interface CompletionResponse {
  id: string;
  choices: {
    index: number;
    message?: {
      role: string;
      content: string;
      tool_calls?: ToolCall[];
    };
    delta?: {
      role?: string;
      content?: string;
    };
    finish_reason: string;
  }[];
  created: number;
  model: string;
  object: string;
}

// Type guard to check if a response is an AsyncIterable
export function isAsyncIterable(obj: any): obj is AsyncIterable<any> {
  return obj !== null && typeof obj === 'object' && Symbol.asyncIterator in obj;
}

export interface AiProviderConfig {
  timeout?: number;
  maxRetries?: number;
  baseURL?: string;
  apiKey: string;
}

export interface AiProvider {
  /**
   * Initialize the AI provider with configuration
   * @param config Provider configuration
   */
  initialize(config: AiProviderConfig): void;

  /**
   * Get the completion from the AI provider
   * @param options Completion options
   * @returns Completion response or stream
   */
  getCompletion(
    options: CompletionOptions,
  ): Promise<CompletionResponse | AsyncIterable<any>>;

  /**
   * Get the model configuration for this provider
   * @param aiModel The AI model enum value
   * @param forSingleDay Whether this is for a single day generation
   * @returns Model configuration
   */
  getModelConfig(aiModel: AiModel, forSingleDay: boolean): any;
}
