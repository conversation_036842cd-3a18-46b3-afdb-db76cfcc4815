export interface Coordinates {
  lat: number;
  lng: number;
}

export interface Activity {
  id: string;
  type?: "activity" | "meal";
  name?: string;
  description: string;
  location?: string;
  startTime?: string;
  endTime?: string;
  cost?: number;
  icon?: string;
  coordinates?: Coordinates;
  imageUrl?: string;
  source?: string;
  rating?: number;
  tags?: string[];
  duration?: string;
  mealType?: string;
  restaurant?: string;
  completed?: boolean;
  city: string;
}
