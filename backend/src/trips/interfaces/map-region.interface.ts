/**
 * Interface for map region data
 */
export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

/**
 * Interface for daily itinerary map region response
 */
export interface DailyMapRegionResponse {
  mapRegion: MapRegion;
  dayNumber: number;
  tripId: string;
}

/**
 * Interface for trip map region response
 */
export interface TripMapRegionResponse {
  mapRegion: MapRegion;
  tripId: string;
}
