/* eslint-disable */

import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ApiKeyManagerModule } from '../api-key-manager/api-key-manager.module';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { StreamGateway } from '../gateways/stream.gateway';
import { WebSocketModule } from '../gateways/websocket.module';
import { LocationsModule } from '../locations/locations.module';
import { StreamingModule } from '../streaming/streaming.module';
import { DaysBalanceModule } from '../days-balance/days-balance.module';
import { AiProviderFactory } from './factories/ai-provider.factory';
import { CustomProvider } from './providers/custom.provider';
import { DeepSeekProvider } from './providers/deepseek.provider';
import { GroqProvider } from './providers/groq.provider';
import { OpenAiProvider } from './providers/openai.provider';
import { Trip, TripSchema } from './schemas/trip.schema';
import { CountryImage, CountryImageSchema } from './schemas/country-image.schema';
import { AiService } from './services/ai.service';
import { ErrorHandlingService } from './services/error-handling.service';
import { HighLevelPlanService } from './services/high-level-plan.service';
import { ItineraryConfigService } from './services/itinerary-config.service';
import { ItineraryGenerationService } from './services/itinerary-generation.service';
import { ItineraryParsingService } from './services/itinerary-parsing.service';
import { ItineraryProgressService } from './services/itinerary-progress.service';
import { ItineraryUpdateService } from './services/itinerary-update.service';
import { MapRegionService } from './services/map-region.service';
import { SingleDayGenerationService } from './services/single-day-generation.service';
import { StreamingNotificationService } from './services/streaming-notification.service';

import { TripCreationService } from './services/trip-creation.service';
import { TripManagementService } from './services/trip-management.service';
import { TripRecoveryService } from './services/trip-recovery.service';
import { UnsplashService } from './services/unsplash.service';
import { CountryImageCacheService } from './services/country-image-cache.service';
import { CityMigrationService } from './services/city-migration.service';
import { TripsController } from './trips.controller';
import { CountryImageCacheController } from './controllers/country-image-cache.controller';
import { TripsService } from './trips.service';

@Module({
  imports: [
    WebSocketModule,
    ConfigModule,
    ScheduleModule.forRoot(),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),

    LocationsModule,
    DaysBalanceModule,
    StreamingModule,
    CommonModule,
    ApiKeyManagerModule,
    MongooseModule.forFeatureAsync([
      {
        name: Trip.name,
        imports: [WebSocketModule],
        useFactory: (streamGateway: StreamGateway) => {
          const schema = TripSchema;

          schema.post('findOneAndUpdate', function (doc) {
            streamGateway.sendTripUpdate((doc._id as string).toString(), doc);
          });

          return schema;
        },
        inject: [StreamGateway],
      },
      {
        name: CountryImage.name,
        useFactory: () => CountryImageSchema,
      },
    ]),
  ],
  controllers: [TripsController, CountryImageCacheController],
  providers: [
    ConfigService,
    TripsService,
    StreamGateway,
    AiService,
    TripManagementService,
    ItineraryGenerationService,
    ItineraryConfigService,
    ItineraryProgressService,
    ItineraryParsingService,
    ItineraryUpdateService,
    ErrorHandlingService,
    TripCreationService,
    UnsplashService,
    CountryImageCacheService,
    StreamingNotificationService,
    HighLevelPlanService,
    SingleDayGenerationService,
    AiProviderFactory,
    OpenAiProvider,
    GroqProvider,
    DeepSeekProvider,
    CustomProvider,
    MapRegionService,
    TripRecoveryService,
    CityMigrationService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
  exports: [
    TripsService,
    TripManagementService,
    ItineraryGenerationService,
    ItineraryConfigService,
    ItineraryProgressService,
    ItineraryParsingService,
    ItineraryUpdateService,
    ErrorHandlingService,
    TripCreationService,
    StreamingNotificationService,
    HighLevelPlanService,
    SingleDayGenerationService,
    MapRegionService,
    TripRecoveryService,
  ],
})
export class TripsModule { }
