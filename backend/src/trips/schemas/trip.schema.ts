import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { TripType, BudgetType } from '../dto/generate-trip.dto';

@Schema()
class Coordinates {
  @Prop()
  lat: number;

  @Prop()
  lng: number;
}

@Schema()
class Activity {
  @Prop({ required: true })
  id: string;

  @Prop({ required: false })
  type?: "activity" | "meal";

  @Prop({ required: false })
  name?: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: false })
  location?: string;

  @Prop({ required: false })
  startTime?: string;

  @Prop({ required: false })
  endTime?: string;

  @Prop({ required: false })
  cost?: number;

  @Prop({ required: false })
  icon?: string;

  @Prop({ type: Coordinates })
  coordinates?: Coordinates;

  @Prop()
  imageUrl?: string;

  @Prop()
  source?: string;

  @Prop()
  rating?: number;

  @Prop([String])
  tags?: string[];

  @Prop()
  duration?: string;

  @Prop()
  mealType?: string;

  @Prop()
  restaurant?: string;



  @Prop({ default: false })
  completed?: boolean;

  @Prop({ required: true })
  city: string;
}

@Schema()
class DailyItinerary {
  @Prop({ required: true })
  day: number;

  @Prop({ required: true })
  date: string;

  @Prop({ type: [Activity], default: [] })
  activities: Activity[];
}

@Schema()
class People {
  @Prop({ required: true })
  adults: number;

  @Prop({ required: true })
  children: number;
}
@Schema()
class TripDetails {
  @Prop({ required: true })
  destination: string;

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  endDate: Date;

  @Prop({ required: true, default: 0 })
  totalDays: number;

  @Prop({ required: true, enum: BudgetType })
  budget: BudgetType;

  @Prop({ required: true })
  intensity: number;

  @Prop({ required: true, enum: TripType })
  tripType: TripType;

  @Prop({ required: true })
  interests: string[];

  @Prop({ required: true })
  wakeUpTime: string;

  @Prop({ required: true })
  sleepTime: string;

  @Prop([String])
  cuisinePreferences?: string[];

  @Prop({ required: true, type: People, default: { adults: 1, children: 0 } })
  people: People;

  @Prop({ required: false })
  mustVisitCities?: string[];

  @Prop({ required: false })
  additionalRequirements?: string;

  @Prop({ required: false })
  arrivalCity?: string;

  @Prop({ required: false })
  departureCity?: string;

  @Prop({ required: false })
  arrivalTime?: string;

  @Prop({ required: false })
  departureTime?: string;

  @Prop({ required: false, enum: ['Air', 'Land', 'Sea'] })
  arrivalMode?: string;

  @Prop({ required: false, enum: ['Air', 'Land', 'Sea'] })
  departureMode?: string;

  @Prop({ required: false })
  imageUrl?: string;
}

@Schema()
class CostBreakdown {
  @Prop({ required: false })
  activities: number;

  @Prop({ required: false })
  meals: number;

  @Prop({ required: false })
  total_estimated_cost: number;
}

@Schema({ timestamps: true })
export class Trip extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ type: TripDetails, required: true })
  tripDetails: TripDetails;

  @Prop({ type: [DailyItinerary], default: [] })
  itinerary: DailyItinerary[];

  @Prop({ type: [DailyItinerary], default: [] })
  customItinerary: DailyItinerary[];

  @Prop({ type: CostBreakdown, default: {} })
  costBreakdown: CostBreakdown;

  @Prop({ type: [String], default: [] })
  additionalTips: string[];

  @Prop({
    type: String,
    enum: ['ready', 'in_progress', 'error'],
    default: 'in_progress',
  })
  status: string;

  @Prop({ type: String, default: '', required: false })
  highLevelPlan: string;

  @Prop({ type: Number, default: 0, required: false })
  currentDayProgress: number;

  @Prop({ type: Number, default: 0, required: false })
  retryCount: number;

  @Prop({ type: Date, required: false })
  lastRetryDate: Date;

  @Prop({ type: String, required: false })
  thread_id: string;

  @Prop({ type: String, required: false })
  previous_response_id: string;

  @Prop({
    type: [
      {
        day: Number,
        is_generating: Boolean,
        started_at: Date,
        tries: Number,
        finished_at: Date,
        error: String,
      },
    ],
    default: [],
    required: false,
  })
  daysProgress: Array<{
    day: number;
    is_generating: boolean;
    started_at: Date | null;
    tries: number;
    finished_at: Date | null;
    error?: string | null;
  }>;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: false })
  highLevelPlanPrompt: string;

  @Prop({ required: false, default: false })
  isFavorite: boolean;

  @Prop({ required: false, default: false })
  isArchived: boolean;

  @Prop({ required: false, default: false })
  isExample: boolean;

  @Prop({ type: Object, default: {}, required: false })
  highLevelPlanActivities: Record<string, string[]>;
}

export const TripSchema = SchemaFactory.createForClass(Trip);
