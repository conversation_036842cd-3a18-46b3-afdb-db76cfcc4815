import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class CountryImage extends Document {
  @Prop({ required: true, unique: true })
  country: string;

  @Prop({ required: true })
  imageUrl: string;

  @Prop({ required: false })
  unsplashImageId?: string;

  @Prop({ required: false })
  altDescription?: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ required: false })
  photographer?: string;

  @Prop({ required: false })
  photographerUsername?: string;

  @Prop({ required: true, default: 'unsplash' })
  source: string;

  @Prop({ required: true, default: Date.now })
  lastUpdated: Date;

  @Prop({ required: false })
  expiresAt?: Date;
}

export const CountryImageSchema = SchemaFactory.createForClass(CountryImage);

// Create indexes for efficient querying
CountryImageSchema.index({ country: 1 });
CountryImageSchema.index({ lastUpdated: 1 });
CountryImageSchema.index({ expiresAt: 1 });
