import { Controller, Delete, Get, Param, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt.guard';
import { CountryImageCacheService } from '../services/country-image-cache.service';

@Controller('country-image-cache')
@UseGuards(JwtAuthGuard)
export class CountryImageCacheController {
  constructor(
    private readonly countryImageCacheService: CountryImageCacheService,
  ) {}

  /**
   * Get cache statistics
   */
  @Get('stats')
  async getCacheStats() {
    const stats = await this.countryImageCacheService.getCacheStats();
    return {
      success: true,
      data: stats,
    };
  }

  /**
   * Get detailed cache dashboard
   */
  @Get('dashboard')
  async getCacheDashboard() {
    const dashboard = await this.countryImageCacheService.getDashboardStats();
    return {
      success: true,
      data: dashboard,
    };
  }

  /**
   * Clean up expired cache entries
   */
  @Delete('cleanup')
  async cleanupExpiredCache() {
    await this.countryImageCacheService.cleanupExpiredCache();
    return {
      success: true,
      message: 'Expired cache entries cleaned up',
    };
  }

  /**
   * Delete cached image for a specific country
   */
  @Delete('country/:country')
  async deleteCachedImage(@Param('country') country: string) {
    await this.countryImageCacheService.deleteCachedImage(country);
    return {
      success: true,
      message: `Cached image for ${country} deleted`,
    };
  }

  /**
   * Get cached image for a specific country
   */
  @Get('country/:country')
  async getCachedImage(@Param('country') country: string) {
    const cachedImage = await this.countryImageCacheService.getCachedImage(country);
    return {
      success: true,
      data: cachedImage,
    };
  }
}
