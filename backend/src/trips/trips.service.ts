/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { CreateTripDto } from './dto/create-trip.dto';
import { GenerateTripDto } from './dto/generate-trip.dto';
import { UpdateTripDto } from './dto/update-trip.dto';
import { Activity } from './interfaces/activity.interface';
import {
  DailyMapRegionResponse,
  TripMapRegionResponse,
} from './interfaces/map-region.interface';
import { Trip } from './schemas/trip.schema';
import { ItineraryGenerationService } from './services/itinerary-generation.service';
import { MapRegionService } from './services/map-region.service';
import { TripManagementService } from './services/trip-management.service';

@Injectable()
export class TripsService {
  constructor(
    private readonly tripManagementService: TripManagementService,
    private readonly itineraryGenerationService: ItineraryGenerationService,
    private readonly mapRegionService: MapRegionService,
  ) { }

  async create(createTripDto: CreateTripDto): Promise<Trip> {
    return this.tripManagementService.create(createTripDto);
  }

  async findAll(
    userId: string,
    filters: {
      includeArchived?: boolean;
      search?: string;
      favorite?: boolean;
      status?: string;
      includeExamples?: boolean;
    } = {},
  ): Promise<Trip[]> {
    return this.tripManagementService.findAll(userId, filters);
  }

  async findOne(id: string): Promise<Trip> {
    return this.tripManagementService.findOne(id);
  }

  async update(id: string, updateTripDto: UpdateTripDto): Promise<Trip> {
    return this.tripManagementService.update(id, updateTripDto);
  }

  async updateActivityCompletion(
    tripId: string,
    activityId: string,
    completed: boolean,
  ): Promise<Trip> {
    return this.tripManagementService.updateActivityCompletion(
      tripId,
      activityId,
      completed,
    );
  }

  async remove(id: string): Promise<Trip> {
    return this.tripManagementService.remove(id);
  }

  async generateTrip(
    tripDto: GenerateTripDto,
    userId: string,
  ): Promise<Trip | null> {
    return this.itineraryGenerationService.generateTrip(tripDto, userId);
  }

  async continueGeneration(id: string): Promise<Trip> {
    return this.itineraryGenerationService.continueGeneration(id);
  }

  async getMissingDays(id: string): Promise<Trip['daysProgress']> {
    return this.itineraryGenerationService.getMissingDays(id);
  }

  async toggleFavorite(id: string): Promise<Trip> {
    return this.tripManagementService.toggleFavorite(id);
  }

  async archiveTrip(id: string): Promise<Trip> {
    return this.tripManagementService.archiveTrip(id);
  }



  /**
   * Get all cities for a trip
   * @param id Trip ID
   * @returns Array of city names
   */
  async getTripCities(id: string): Promise<string[]> {
    const trip = await this.tripManagementService.findOne(id);

    // Extract cities from the itinerary
    const cities = new Set<string>();

    // Check if itinerary exists and is an array
    if (!trip.itinerary || !Array.isArray(trip.itinerary)) {
      return [];
    }

    // Add cities from the activities' city field
    trip.itinerary.forEach((day) => {
      // Check if day has activities and it's an array
      if (day && day.activities && Array.isArray(day.activities)) {
        day.activities.forEach((activity) => {
          if (activity && activity.city) {
            cities.add(activity.city?.trim()?.toLowerCase());
          }
        });
      }
    });

    return Array.from(cities).sort();
  }

  /**
   * Get activities grouped by city for a trip
   * @param id Trip ID
   * @param lowerCasedCityFilter Optional city filter
   * @returns Object with city names as keys and arrays of activities as values
   */
  async getActivitiesByCity(
    id: string,
    cityFilter?: string,
  ): Promise<Record<string, any[]>> {
    const trip = await this.tripManagementService.findOne(id);

    // Group activities by city
    const activitiesByCity: Record<string, any[]> = {};
    const lowerCasedCityFilter = cityFilter?.trim()?.toLowerCase();

    // Check if itinerary exists and is an array
    if (!trip.itinerary || !Array.isArray(trip.itinerary)) {
      return activitiesByCity;
    }

    trip.itinerary.forEach((day) => {
      // Check if day has activities and it's an array
      if (day && day.activities && Array.isArray(day.activities)) {
        day.activities.forEach((activity) => {
          if (!activity) return;

          // All activities now have a city field
          const cityKey = activity.city?.trim()?.toLowerCase();

          // Skip if we're filtering by city and this doesn't match
          if (lowerCasedCityFilter && cityKey !== lowerCasedCityFilter) {
            return;
          }

          if (!activitiesByCity[cityKey]) {
            activitiesByCity[cityKey] = [];
          }

          activitiesByCity[cityKey].push(
            {
              ...this.convertActivityToPlainObject(activity),
              dayNumber: day.day,
              date: day.date
            },
          );
        });
      }
    });

    return activitiesByCity;
  }

  /**
   * Get filtered activities for a trip based on day, city, and completion status
   * @param id Trip ID
   * @param filters Filter options
   * @returns Array of filtered activities
   */
  async getFilteredActivities(
    id: string,
    filters: {
      day: number;
      city: string;
      status: 'all' | 'completed' | 'pending';
    },
  ): Promise<any[]> {
    const trip = await this.tripManagementService.findOne(id);
    const { day, city, status } = filters;

    // Function to check if an activity belongs to the selected city
    const activityMatchesCity = (
      activity: Activity,
      selectedCity: string,
    ): boolean => {
      if (selectedCity === 'all') return true;

      // All activities now have a city field
      return (
        activity.city?.trim()?.toLowerCase() ===
        selectedCity?.trim()?.toLowerCase()
      );
    };

    let activities: any[] = [];

    // Check if itinerary exists and is an array
    if (!trip.itinerary || !Array.isArray(trip.itinerary)) {
      return [];
    }

    if (day === 0) {
      // Show all days
      activities = trip.itinerary.flatMap((d) => {
        // Check if day has activities and it's an array
        if (!d || !d.activities || !Array.isArray(d.activities)) {
          return [];
        }
        return d.activities.filter((activity: Activity) => {
          if (!activity) return false;
          const activityStatus = activity.completed ? 'completed' : 'pending';
          const statusMatch = status === 'all' || activityStatus === status;
          const cityMatch = activityMatchesCity(activity, city);

          return cityMatch && statusMatch;
        });
      });
    } else {
      // Show specific day
      const dayData = trip.itinerary[day - 1];
      if (!dayData || !dayData.activities || !Array.isArray(dayData.activities)) {
        return [];
      }

      activities = dayData.activities.filter((activity: Activity) => {
        if (!activity) return false;
        const activityStatus = activity.completed ? 'completed' : 'pending';
        const statusMatch = status === 'all' || activityStatus === status;
        const cityMatch = activityMatchesCity(activity, city);

        return cityMatch && statusMatch;
      });
    }

    return activities.map((activity) =>
      this.convertActivityToPlainObject(activity),
    );
  }

  /**
   * Extract city names from a string that might contain multiple cities
   * @param cityString String containing city names (e.g., "Paris to London" or "Paris / London")
   * @returns Array of city names
   */
  private extractCitiesFromString(cityString: string): string[] {
    if (!cityString) return [];

    // Check for "to" pattern (e.g., "Paris to London")
    if (cityString?.toLowerCase().includes(' to ')) {
      return cityString.split(' to ').map((city) => city.trim()?.toLowerCase());
    }

    // Check for " - " pattern (e.g., "Paris - London")
    if (cityString?.toLowerCase().includes(' - ')) {
      return cityString.split(' - ').map((city) => city.trim()?.toLowerCase());
    }

    // Check for " , " pattern (e.g., "Paris , London")
    if (cityString?.toLowerCase().includes(' , ')) {
      return cityString.split(' , ').map((city) => city.trim()?.toLowerCase());
    }

    // Check for "/" pattern (e.g., "Paris / London")
    if (cityString?.includes(' / ')) {
      return cityString.split(' / ').map((city) => city.trim()?.toLowerCase());
    }

    // Return as a single city if no special patterns
    return [cityString.trim().toLowerCase()];
  }

  /**
   * Convert an activity to a plain JavaScript object
   * This handles both Mongoose documents (which have toObject) and plain objects
   * @param activity The activity to convert
   * @returns A plain JavaScript object representation of the activity
   */
  private convertActivityToPlainObject(activity: any): Record<string, any> {
    // If the activity has a toObject method (Mongoose document), use it
    if (activity && typeof activity.toObject === 'function') {
      return activity.toObject();
    }

    // Otherwise, create a shallow copy of the object
    return { ...activity };
  }

  /**
   * Get map region for a trip
   * @param id Trip ID
   * @returns Map region data
   */
  async getTripMapRegion(id: string): Promise<TripMapRegionResponse> {
    return this.mapRegionService.calculateTripMapRegion(id);
  }

  /**
   * Get map region for a specific day of a trip
   * @param id Trip ID
   * @param dayNumber Day number
   * @returns Map region data
   */
  async getDailyMapRegion(
    id: string,
    dayNumber: number,
  ): Promise<DailyMapRegionResponse> {
    return this.mapRegionService.calculateDailyMapRegion(id, dayNumber);
  }

  /**
   * Get map region for a specific city of a trip
   * @param id Trip ID
   * @param city City name
   * @param dayNumber Optional day number
   * @returns Map region data
   */
  async getCityMapRegion(
    id: string,
    city: string,
    dayNumber?: number,
  ): Promise<TripMapRegionResponse> {
    return this.mapRegionService.calculateCityMapRegion(id, city, dayNumber);
  }
}
