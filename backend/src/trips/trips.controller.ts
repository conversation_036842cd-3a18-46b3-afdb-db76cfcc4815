/* eslint-disable */
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,
} from '@nestjs/common';

import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { InsufficientDaysException } from '../days-balance/day-usage.service';
import { CreateTripDto } from './dto/create-trip.dto';
import { GenerateTripDto } from './dto/generate-trip.dto';
import { UpdateTripDto } from './dto/update-trip.dto';
import {
  DailyMapRegionResponse,
  TripMapRegionResponse,
} from './interfaces/map-region.interface';
import { Trip } from './schemas/trip.schema';
import { TripsService } from './trips.service';
import { CityMigrationService } from './services/city-migration.service';

@ApiTags('trips')
@Controller('trips')
export class TripsController {
  constructor(
    private readonly tripsService: TripsService,
    private readonly cityMigrationService: CityMigrationService,
  ) { }

  @Get()
  @ApiOperation({ summary: 'Get all trips' })
  @ApiQuery({
    name: 'includeArchived',
    description: 'Include archived trips',
    required: false,
    type: Boolean,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search query for trip name or destination',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'favorite',
    description: 'Filter by favorite status',
    required: false,
    type: Boolean,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by trip status (e.g., in_progress, ready)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'includeExamples',
    description: 'Include example trips visible to all users',
    required: false,
    type: Boolean,
  })
  @ApiResponse({ status: 200, description: 'Returns all trips', type: [Trip] })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async findAll(
    @Request() req: { user: { _id: string } },
    @Query('includeArchived') includeArchived: boolean = false,
    @Query('search') search?: string,
    @Query('favorite') favorite?: boolean,
    @Query('status') status?: string,
    @Query('includeExamples') includeExamples: boolean = true,
  ) {
    try {
      const userId = req.user._id;
      return await this.tripsService.findAll(userId, {
        includeArchived,
        search,
        favorite,
        status,
        includeExamples,
      });
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post()
  @ApiOperation({ summary: 'Create a new trip' })
  @ApiBody({ type: CreateTripDto })
  @ApiResponse({
    status: 201,
    description: 'Trip created successfully',
    type: Trip,
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createTrip(@Body() createTripDto: CreateTripDto) {
    try {
      return await this.tripsService.create(createTripDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('generate')
  @ApiOperation({ summary: 'Generate a new trip itinerary' })
  @ApiBody({ type: GenerateTripDto })
  @ApiResponse({
    status: 201,
    description: 'Trip itinerary generated successfully',
    type: Trip,
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async generateTrip(
    @Body() generateTripDto: GenerateTripDto,
    @Request() req: { user: { _id: string } },
  ): Promise<Trip | null> {
    try {
      return await this.tripsService.generateTrip(
        generateTripDto,
        req.user._id,
      );
    } catch (error) {
      if (error instanceof InsufficientDaysException) {
        throw new HttpException(error.message, HttpStatus.PAYMENT_REQUIRED);
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch(':id/activity/:activityId/completion')
  @ApiOperation({ summary: 'Update activity completion status' })
  @ApiParam({ name: 'id', description: 'Trip ID', required: true })
  @ApiParam({ name: 'activityId', description: 'Activity ID', required: true })
  @ApiBody({
    schema: { type: 'object', properties: { completed: { type: 'boolean' } } },
  })
  @ApiResponse({
    status: 200,
    description: 'Activity completion status updated successfully',
    type: Trip,
  })
  @ApiResponse({ status: 404, description: 'Trip or activity not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateActivityCompletion(
    @Param('id') id: string,
    @Param('activityId') activityId: string,
    @Body('completed') completed: boolean,
  ): Promise<Trip> {
    try {
      return await this.tripsService.updateActivityCompletion(
        id,
        activityId,
        completed,
      );
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id/continue-generation')
  @ApiOperation({
    summary:
      'Continue generating an itinerary for a trip starting from the day you left',
  })
  @ApiParam({ name: 'id', description: 'Trip ID', required: true })
  @ApiResponse({
    status: 200,
    description: 'Itinerary generated successfully',
    type: Trip,
  })
  @ApiResponse({ status: 402, description: 'Insufficient credits' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async continueGeneration(@Param('id') id: string) {
    try {
      return await this.tripsService.continueGeneration(id);
    } catch (error) {
      if (error instanceof InsufficientDaysException) {
        throw new HttpException(error.message, HttpStatus.PAYMENT_REQUIRED);
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch(':id/favorite')
  @ApiOperation({ summary: 'Toggle favorite status of a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID', required: true })
  @ApiResponse({
    status: 200,
    description: 'Trip favorite status toggled successfully',
    type: Trip,
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async toggleFavorite(@Param('id') id: string) {
    try {
      return await this.tripsService.toggleFavorite(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/missing-days')
  @ApiOperation({ summary: 'Get missing days for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID', required: true })
  @ApiResponse({
    status: 200,
    description: 'Returns missing days',
    type: [Number],
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getMissingDays(@Param('id') id: string) {
    try {
      return await this.tripsService.getMissingDays(id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/cities')
  @ApiOperation({ summary: 'Get all cities for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns all cities for the trip',
    type: [String],
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getTripCities(@Param('id') id: string) {
    try {
      return await this.tripsService.getTripCities(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/activities-by-city')
  @ApiOperation({ summary: 'Get activities grouped by city for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiQuery({
    name: 'city',
    description: 'Filter by city (optional)',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns activities grouped by city',
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getActivitiesByCity(
    @Param('id') id: string,
    @Query('city') city?: string,
  ) {
    try {
      return await this.tripsService.getActivitiesByCity(id, city);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/activities/filtered')
  @ApiOperation({ summary: 'Get filtered activities for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiQuery({
    name: 'day',
    description: 'Filter by day (0 for all days)',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'city',
    description: 'Filter by city (all for all cities)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by completion status (all, completed, pending)',
    required: false,
    enum: ['all', 'completed', 'pending'],
  })
  @ApiResponse({ status: 200, description: 'Returns filtered activities' })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getFilteredActivities(
    @Param('id') id: string,
    @Query('day') day?: number,
    @Query('city') city?: string,
    @Query('status') status?: 'all' | 'completed' | 'pending',
  ) {
    try {
      return await this.tripsService.getFilteredActivities(id, {
        day: day || 0,
        city: city || 'all',
        status: status || 'all',
      });
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/map-region')
  @ApiOperation({ summary: 'Get map region for a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns map region data for the trip',
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getTripMapRegion(
    @Param('id') id: string,
  ): Promise<TripMapRegionResponse> {
    try {
      return await this.tripsService.getTripMapRegion(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/daily-map-region/:day')
  @ApiOperation({ summary: 'Get map region for a specific day of a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiParam({ name: 'day', description: 'Day number' })
  @ApiResponse({
    status: 200,
    description: 'Returns map region data for the specified day',
  })
  @ApiResponse({ status: 404, description: 'Trip or day not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDailyMapRegion(
    @Param('id') id: string,
    @Param('day') day: string,
  ): Promise<DailyMapRegionResponse> {
    try {
      const dayNumber = parseInt(day, 10);
      if (isNaN(dayNumber)) {
        throw new HttpException('Invalid day number', HttpStatus.BAD_REQUEST);
      }
      return await this.tripsService.getDailyMapRegion(id, dayNumber);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id/city-map-region/:city')
  @ApiOperation({ summary: 'Get map region for a specific city of a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiParam({ name: 'city', description: 'City name' })
  @ApiQuery({
    name: 'day',
    description: 'Day number (optional)',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns map region data for the specified city',
  })
  @ApiResponse({ status: 404, description: 'Trip or city not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getCityMapRegion(
    @Param('id') id: string,
    @Param('city') city: string,
    @Query('day') day?: string,
  ): Promise<TripMapRegionResponse> {
    try {
      const dayNumber = day ? parseInt(day, 10) : undefined;
      if (dayNumber && isNaN(dayNumber)) {
        throw new HttpException('Invalid day number', HttpStatus.BAD_REQUEST);
      }
      return await this.tripsService.getCityMapRegion(id, city, dayNumber);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a trip by id' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiResponse({ status: 200, description: 'Returns the trip', type: Trip })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async findOne(@Param('id') id: string) {
    try {
      return await this.tripsService.findOne(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiBody({ type: UpdateTripDto })
  @ApiResponse({
    status: 200,
    description: 'Trip updated successfully',
    type: Trip,
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async update(@Param('id') id: string, @Body() updateTripDto: UpdateTripDto) {
    try {
      return await this.tripsService.update(id, updateTripDto);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch(':id/archive')
  @ApiOperation({ summary: 'Archive a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID', required: true })
  @ApiResponse({
    status: 200,
    description: 'Trip archived successfully',
    type: Trip,
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async archiveTrip(@Param('id') id: string) {
    try {
      return await this.tripsService.archiveTrip(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a trip' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiResponse({ status: 200, description: 'Trip deleted successfully' })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async remove(@Param('id') id: string) {
    try {
      return await this.tripsService.remove(id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('migrate-cities')
  @ApiOperation({ summary: 'Migrate existing trips to use activity-level cities' })
  @ApiResponse({ status: 200, description: 'Migration completed successfully' })
  @ApiResponse({ status: 500, description: 'Migration failed' })
  async migrateCities() {
    try {
      await this.cityMigrationService.migrateTripsToActivityCities();
      return { message: 'Migration completed successfully' };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('validate-migration')
  @ApiOperation({ summary: 'Validate that all activities have cities' })
  @ApiResponse({ status: 200, description: 'Validation results' })
  @ApiResponse({ status: 500, description: 'Validation failed' })
  async validateMigration() {
    try {
      return await this.cityMigrationService.validateMigration();
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch(':id/example')
  @ApiOperation({ summary: 'Mark or unmark a trip as an example' })
  @ApiParam({ name: 'id', description: 'Trip ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        isExample: {
          type: 'boolean',
          description: 'Whether to mark the trip as an example',
        },
      },
      required: ['isExample'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Trip example status updated successfully',
    type: Trip,
  })
  @ApiResponse({ status: 404, description: 'Trip not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateExampleStatus(
    @Param('id') id: string,
    @Body() body: { isExample: boolean },
  ) {
    try {
      return await this.tripsService.update(id, { isExample: body.isExample });
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
