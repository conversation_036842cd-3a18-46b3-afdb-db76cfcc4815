/* eslint-disable */

import { BadRequestException } from '@nestjs/common';

export enum TripStatus {
  IN_PROGRESS = 'in_progress',
  READY = 'ready',
  ERROR = 'error',
  INSUFFICIENT_BALANCE = 'insufficient_balance',
}

export class TripStatusHelper {
  /**
   * Validates if a status transition is allowed
   * @param currentStatus Current trip status
   * @param newStatus New trip status
   * @throws BadRequestException if transition is not allowed
   */
  static validateStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): void {
    // Allow same status
    if (currentStatus === newStatus) {
      return;
    }

    // Define allowed transitions
    const allowedTransitions = {
      [TripStatus.IN_PROGRESS]: [TripStatus.READY, TripStatus.ERROR, TripStatus.INSUFFICIENT_BALANCE],
      [TripStatus.ERROR]: [TripStatus.IN_PROGRESS],
      [TripStatus.READY]: [TripStatus.IN_PROGRESS],
      [TripStatus.INSUFFICIENT_BALANCE]: [TripStatus.IN_PROGRESS],
    };

    if (!allowedTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(
        `Cannot transition from ${currentStatus} to ${newStatus}`,
      );
    }
  }

  /**
   * Checks if a trip is in progress
   * @param status Trip status
   * @returns boolean
   */
  static isInProgress(status: string): boolean {
    return status === TripStatus.IN_PROGRESS;
  }

  /**
   * Checks if a trip is ready
   * @param status Trip status
   * @returns boolean
   */
  static isReady(status: string): boolean {
    return status === TripStatus.READY;
  }

  /**
   * Checks if a trip has an error
   * @param status Trip status
   * @returns boolean
   */
  static hasError(status: string): boolean {
    return status === TripStatus.ERROR;
  }
}
