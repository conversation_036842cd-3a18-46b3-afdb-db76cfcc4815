export enum GenerateTripError {
  // High Level Plan
  FAILED_TO_GENERATE_HIGH_LEVEL_PLAN = 'Failed to generate high level plan',
  FAILED_TO_GET_HIGH_LEVEL_PLAN_RESPONSE = 'Failed to get high level plan response',
  FAILED_TO_GET_HIGH_LEVEL_PLAN_CONTENT = 'Failed to get high level plan content',
  FAILED_TO_PARSE_HIGH_LEVEL_PLAN = 'Failed to parse high level plan',
  INCORRECT_HIGH_LEVEL_PLAN_LENGTH = 'Incorrect high level plan length',

  // Single Day Itinerary
  FAILED_TO_GENERATE_SINGLE_DAY_ITINERARY = 'Failed to generate single day itinerary',
  FAILED_TO_GET_SINGLE_DAY_ITINERARY_RESPONSE = 'Failed to get single day itinerary response',
  FAILED_TO_GET_SINGLE_DAY_ITINERARY_CONTENT = 'Failed to get single day itinerary content',
  FAILED_TO_PARSE_SINGLE_DAY_ITINERARY = 'Failed to parse single day itinerary',
  INCORRECT_SINGLE_DAY_ITINERARY_LENGTH = 'Incorrect single day itinerary length',
  FAILED_TO_GENERATE_ITINERARY = 'Failed to generate itinerary',
  FAILED_TO_UPDATE_DAY_PROGRESS = 'Failed to update day progress',
}

export enum GenerateItineraryError {
  INCOMPLETE_TRIP_DETAILS = 'Incomplete trip details',
  INCOMPLETE_HIGH_LEVEL_PLAN = 'Incomplete high level plan',
  FAILED_TO_GENERATE_ITINERARY = 'Failed to generate itinerary',
  FAILED_TO_GET_ITINERARY_RESPONSE = 'Failed to get itinerary response',
  FAILED_TO_GET_ITINERARY_CONTENT = 'Failed to get itinerary content',
  FAILED_TO_PARSE_ITINERARY = 'Failed to parse itinerary',
  INCORRECT_ITINERARY_FORMAT = 'Incorrect itinerary format',
  INCORRECT_ITINERARY_LENGTH = 'Incorrect itinerary length',
  MISSING_ITINERARY_PROPERTY = 'Missing itinerary property in response',
  INVALID_ITINERARY_ARRAY = 'Itinerary is not a valid array',
  MISSING_DAY_PROPERTY = 'Missing day property in itinerary',
  MISSING_DATE_PROPERTY = 'Missing date property in itinerary',
  MISSING_CITY_PROPERTY = 'Missing city property in itinerary',
  MISSING_ACTIVITIES_PROPERTY = 'Missing activities property in itinerary',
  INVALID_ACTIVITIES_ARRAY = 'Activities is not a valid array',
  MISSING_ACTIVITY_ID = 'Activity is missing ID',
  MISSING_ACTIVITY_DESCRIPTION = 'Activity is missing description',
  INVALID_COORDINATES = 'Activity has invalid coordinates',
}
