/* eslint-disable */

import { Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { isAsyncIterable } from '../interfaces/ai-provider.interface';

/**
 * Utility class to handle streaming responses from OpenAI API
 */
export class StreamingResponseHandler {
  private static readonly logger = new Logger(StreamingResponseHandler.name);

  /**
   * Process a streaming response from OpenAI API and convert it to a standard response format
   * @param streamingResponse The streaming response from OpenAI API
   * @returns A promise that resolves to a standard OpenAI response format
   */
  static async processStreamingResponse(
    streamingResponse: AsyncIterable<any>,
  ): Promise<OpenAI.Chat.Completions.ChatCompletion> {
    if (!isAsyncIterable(streamingResponse)) {
      throw new Error('Response is not an AsyncIterable');
    }

    let accumulatedContent = '';
    let responseId = '';
    let model = '';
    let created = 0;
    let finishReason:
      | 'stop'
      | 'length'
      | 'tool_calls'
      | 'content_filter'
      | 'function_call' = 'stop';

    try {
      for await (const chunk of streamingResponse) {
        // Extract metadata from the first chunk
        if (!responseId && chunk.id) {
          responseId = chunk.id;
        }
        if (!model && chunk.model) {
          model = chunk.model;
        }
        if (!created && chunk.created) {
          created = chunk.created;
        }

        // Extract content from the chunk
        if (chunk.choices && chunk.choices.length > 0) {
          const choice = chunk.choices[0];

          // Update finish reason if available
          if (choice.finish_reason) {
            finishReason = choice.finish_reason as
              | 'stop'
              | 'length'
              | 'tool_calls'
              | 'content_filter'
              | 'function_call';
          }

          // Accumulate content
          if (choice.delta && choice.delta.content) {
            accumulatedContent += choice.delta.content;
          }
        }
      }

      // Construct a standard response format
      const standardResponse: OpenAI.Chat.Completions.ChatCompletion = {
        id: responseId || 'unknown',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: accumulatedContent,
              refusal: null,
            },
            logprobs: null,
            finish_reason: finishReason || 'stop',
          },
        ],
        created: created || Date.now(),
        model: model || 'unknown',
        object: 'chat.completion',
      };

      this.logger.debug(
        `Processed streaming response with ${accumulatedContent.length} characters`,
      );
      return standardResponse;
    } catch (error) {
      this.logger.error(
        `Error processing streaming response: ${error.message}`,
      );
      throw error;
    }
  }
}
