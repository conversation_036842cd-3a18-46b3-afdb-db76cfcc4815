import { Activity } from '../interfaces/activity.interface';

export class CoordinatesSanitizerHelper {
  /**
   * Sanitize coordinates in activities
   * @param activities Array of activities
   * @returns Array of activities with sanitized coordinates
   */
  static sanitizeCoordinates(activities: Activity[]): Activity[] {
    return activities.map((activity) => {
      if (activity.coordinates) {
        const lat = parseFloat(String(activity.coordinates.lat));
        const lng = parseFloat(String(activity.coordinates.lng));

        activity.coordinates = {
          lat: isNaN(lat) ? 0 : lat,
          lng: isNaN(lng) ? 0 : lng,
        };
      } else {
        activity.coordinates = {
          lat: 0,
          lng: 0,
        };
      }
      return activity;
    });
  }
}
