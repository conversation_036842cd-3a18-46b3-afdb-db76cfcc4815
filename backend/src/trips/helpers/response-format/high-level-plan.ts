export const response_format_high_level_plan = {
  response_format: {
    type: 'json_schema',
    json_schema: {
      name: 'itinerary',
      schema: {
        type: 'object',
        properties: {
          trip_name: {
            type: 'string',
            description:
              "A creative name for the trip in the format 'Your dream trip to the secret land'",
          },
          itinerary: {
            type: 'array',
            description: 'List of travel itinerary items',
            items: {
              type: 'object',
              properties: {
                day: {
                  type: 'number',
                  description: 'Day number of the itinerary',
                },
                date: {
                  type: 'string',
                  description: 'Date of the itinerary in YYYY-MM-DD format',
                },
                start_city: {
                  type: 'string',
                  description: 'Name of the starting city',
                },
                end_city: {
                  type: 'string',
                  description: 'Name of the ending city',
                },
              },
              required: ['day', 'date', 'start_city', 'end_city'],
              additionalProperties: false,
            },
          },
        },
        required: ['trip_name', 'itinerary'],
        additionalProperties: false,
      },
      strict: true,
    },
  },
};
