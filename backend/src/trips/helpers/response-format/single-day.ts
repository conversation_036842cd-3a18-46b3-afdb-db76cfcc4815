export const response_format_single_day = {
  response_format: {
    type: 'json_schema',
    json_schema: {
      name: 'itinerary_schema',
      schema: {
        type: 'object',
        properties: {
          itinerary: {
            type: 'array',
            description: 'A list of daily itineraries.',
            items: {
              type: 'object',
              properties: {
                day: {
                  type: 'number',
                  description: 'The day of the itinerary.',
                },
                date: {
                  type: 'string',
                  description:
                    'The date of the itinerary in format YYYY-MM-DD.',
                },
                activities: {
                  type: 'array',
                  description: 'A list of activities for the day.',
                  items: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        description: 'Unique identifier for the activity.',
                      },
                      type: {
                        type: 'string',
                        enum: [
                          'activity',
                          'meal',
                        ],
                        description: 'The type of the activity.',
                      },
                      startTime: {
                        type: 'string',
                        description:
                          'The start time of the activity in HH:MM format.',
                      },
                      endTime: {
                        type: 'string',
                        description:
                          'The end time of the activity in HH:MM format.',
                      },
                      name: {
                        type: 'string',
                        description:
                          'The name of the activity/attraction or restaurant or hotel.',
                      },
                      description: {
                        type: 'string',
                        description:
                          'Detailed description of the activity or dining experience.',
                      },
                      location: {
                        type: 'string',
                        description:
                          'Specific location of the activity or restaurant or hotel.',
                      },
                      lat: {
                        type: 'string',
                        description: 'GPS latitude of the location.',
                      },
                      lng: {
                        type: 'string',
                        description: 'GPS longitude of the location.',
                      },
                      cost: {
                        type: 'number',
                        description: 'Estimated cost in dollars.',
                      },
                      duration: {
                        type: 'string',
                        description: 'Duration of the activity.',
                      },
                      rating: {
                        type: 'number',
                        description: 'Rating of the activity from 0 to 5.',
                      },
                      mealType: {
                        type: 'string',
                        enum: ['breakfast', 'lunch', 'dinner'],
                        description: 'Type of meal, applicable for meal types.',
                      },
                      city: {
                        type: 'string',
                        description: 'The city where the activity takes place.',
                      },
                    },
                    required: [
                      'id',
                      'type',
                      'startTime',
                      'endTime',
                      'name',
                      'description',
                      'location',
                      'lat',
                      'lng',
                      'cost',
                      'duration',
                      'rating',
                      'mealType',
                      'city',
                    ],
                    additionalProperties: false,
                  },
                },
              },
              required: ['day', 'date', 'activities'],
              additionalProperties: false,
            },
          },
          cost_breakdown: {
            type: 'object',
            properties: {
              activities: {
                type: 'number',
                description: 'Total cost for activities.',
              },
              meals: {
                type: 'number',
                description: 'Total cost for meals.',
              },
              total_estimated_cost: {
                type: 'number',
                description: 'Total estimated cost of the itinerary.',
              },
            },
            required: [
              'activities',
              'meals',
              'total_estimated_cost',
            ],
            additionalProperties: false,
          },
          additional_tips: {
            type: 'array',
            description: 'Additional travel tips.',
            items: {
              type: 'string',
            },
          },
        },
        required: ['itinerary', 'cost_breakdown', 'additional_tips'],
        additionalProperties: false,
      },
    },
  },
};
