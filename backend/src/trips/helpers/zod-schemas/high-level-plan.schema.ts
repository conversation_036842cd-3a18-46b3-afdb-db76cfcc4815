import { z } from 'zod';

/**
 * Zod schema for high-level trip plan generation
 * This schema defines the structure for the initial trip planning phase
 * where we generate a high-level overview of the trip with daily city movements
 */

export const HighLevelPlanItineraryItemSchema = z.object({
  day: z.number().int().positive().describe('Day number of the itinerary'),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).describe('Date of the itinerary in YYYY-MM-DD format'),
  start_city: z.string().min(1).describe('Name of the starting city'),
  end_city: z.string().min(1).describe('Name of the ending city'),
});

export const HighLevelPlanSchema = z.object({
  trip_name: z.string()
    .min(1)
    .describe("A creative name for the trip in the format 'Your dream trip to the secret land'"),
  itinerary: z.array(HighLevelPlanItineraryItemSchema)
    .min(1)
    .describe('List of travel itinerary items'),
});

export type HighLevelPlanItineraryItem = z.infer<typeof HighLevelPlanItineraryItemSchema>;
export type HighLevelPlan = z.infer<typeof HighLevelPlanSchema>;
