import { z } from 'zod';

/**
 * Zod schema for single day itinerary generation
 * This schema defines the structure for detailed daily itineraries
 * with activities, accommodations, and cost breakdowns
 */

export const CoordinatesSchema = z.object({
  lat: z.number().describe('Latitude coordinate'),
  lng: z.number().describe('Longitude coordinate'),
});

export const ActivitySchema = z.object({
  id: z.string().describe('Unique identifier for the activity'),
  name: z.string().min(1).describe('Name of the activity'),
  description: z.string().min(1).describe('Detailed description of the activity'),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).describe('Start time in HH:MM format'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).describe('End time in HH:MM format'),
  duration: z.number().positive().describe('Duration in minutes'),
  location: z.string().min(1).describe('Location of the activity'),
  coordinates: CoordinatesSchema.optional().describe('GPS coordinates of the activity'),
  cost: z.number().min(0).describe('Cost of the activity'),
  category: z.string().min(1).describe('Category of the activity (e.g., sightseeing, dining, transport)'),
  bookingUrl: z.string().url().optional().describe('URL for booking the activity'),
  tips: z.array(z.string()).optional().describe('Tips for the activity'),
  completed: z.boolean().default(false).describe('Whether the activity is completed'),
  city: z.string().min(1).describe('City where the activity takes place'),
});


export const DailyItinerarySchema = z.object({
  day: z.number().int().positive().describe('Day number of the itinerary'),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).describe('Date of the itinerary in YYYY-MM-DD format'),
  activities: z.array(ActivitySchema).describe('List of activities for the day'),
});

export const CostBreakdownSchema = z.object({
  activities: z.number().min(0).optional().describe('Total activities costs'),
  meals: z.number().min(0).optional().describe('Total meals costs'),
  total_estimated_cost: z.number().min(0).optional().describe('Total estimated cost'),
});

export const SingleDayItinerarySchema = z.object({
  itinerary: z.array(DailyItinerarySchema).min(1).describe('List of daily itineraries'),
  additionalTips: z.array(z.string()).optional().describe('Additional tips for the trip'),
  costBreakdown: CostBreakdownSchema.optional().describe('Cost breakdown for the trip'),
});

export type Coordinates = z.infer<typeof CoordinatesSchema>;
export type Activity = z.infer<typeof ActivitySchema>;
export type DailyItinerary = z.infer<typeof DailyItinerarySchema>;
export type CostBreakdown = z.infer<typeof CostBreakdownSchema>;
export type SingleDayItinerary = z.infer<typeof SingleDayItinerarySchema>;
