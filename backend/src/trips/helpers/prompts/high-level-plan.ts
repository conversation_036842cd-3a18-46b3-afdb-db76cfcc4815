import { GenerateTripDto } from 'src/trips/dto/generate-trip.dto';
import { AiModel } from '../ai-model.type';
import { response_format_high_level_plan } from '../response-format/high-level-plan';
const formatDate = (date: Date | string) => {
  if (typeof date === 'string') {
    return date.split('T')[0];
  }
  return date.toISOString().split('T')[0];
};
export function constructPrimaryPrompt(
  tripDto: GenerateTripDto,
  model: AiModel,
): string {
  const totalDays =
    Math.ceil(
      (new Date(tripDto.endDate).getTime() -
        new Date(tripDto.startDate).getTime()) /
      (1000 * 60 * 60 * 24),
    ) + 1;
  const lerp = (i: number, A: number, B: number) => A + (B - A) * (i / 10);
  const nightsPerCity = lerp(tripDto.intensity, 6.5, 1.5);
  const numberOfcities = Math.round((totalDays - 1) / nightsPerCity);

  return `Create a high level itinerary for my trip (${totalDays} days) ${totalDays > 3 ? `including ${numberOfcities} cities with an average of ${Math.round(nightsPerCity)} nights per city` : ''} based on the following inputs:
- Arrival Date and Time: ${formatDate(tripDto.startDate)} at ${tripDto.arrivalTime}.
- Departure Date and Time: ${formatDate(tripDto.endDate)} at ${tripDto.departureTime}.
- Arrival City: ${tripDto.arrivalCity}.
- Departure City: ${tripDto.departureCity}.
- Type of Trip: ${tripDto.tripType}.
- List of Interests: ${tripDto.interests.join(',')}.
${tripDto.mustVisitCities?.length && tripDto.mustVisitCities?.length > 0 ? `- Must Visit Cities: ${tripDto.mustVisitCities.join(', ')}` : ''}
The plan should take into account:
- Create a short, creative trip name in the format "Your dream trip to the secret land" that captures the essence of this journey.
- A general flow of the trip (e.g., travel days, city changes).
- The first day's startCity should be ${tripDto.arrivalCity} and the last day's endCity should be ${tripDto.departureCity}.
- Select cities that match the interests and travel type
- Consider geographical proximity and logical travel routes.
- IMPORTANT: For all cities and locations, always include the country name to avoid ambiguity with similarly named locations in different countries.
${model === AiModel.DEEPSEEK ? 'This is the JSON response format instructions and the response should be in JSON format' : ''}
${model === AiModel.DEEPSEEK ? JSON.stringify(response_format_high_level_plan) : ''}
`;
}
