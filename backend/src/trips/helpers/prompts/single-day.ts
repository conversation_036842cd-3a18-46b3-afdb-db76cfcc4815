/* eslint-disable */

import { Trip } from 'src/trips/schemas/trip.schema';
import { AiModel } from '../ai-model.type';
import { response_format_single_day } from '../response-format/single-day';

type SingleDayPrompt = {
  day: number;
  tripDetails: Trip['tripDetails'];
  highLevelPlanContentResponse: string;
  model: AiModel;
  trip?: Trip;
  dayInfo?: {
    startCity?: string;
    endCity?: string;
    description?: string;
    isFirstDay: boolean;
    isLastDay: boolean;
    date?: string;
  };
  activitiesContext?: string;
};

const formatAdditionalRequirements = (
  requirement: string | undefined,
): string => {
  if (!requirement || requirement.trim() === '') return '';
  return `\nAdditional Requirements to consider:\n- ${requirement}`;
};

/**
 * Calculate the date for a specific day in the trip
 * @param startDate The start date of the trip (ISO string)
 * @param daysToAdd Number of days to add to the start date
 * @returns Formatted date string (YYYY-MM-DD)
 */
const calculateDayDate = (
  startDate: string | Date,
  daysToAdd: number,
): string => {
  const date = new Date(startDate);
  date.setDate(date.getDate() + daysToAdd);
  return date.toISOString().split('T')[0];
};

export function constructSingleDayPrompt({
  day,
  tripDetails,
  highLevelPlanContentResponse,
  model,
  trip: _trip, // Renamed to _trip to indicate it's not used
  dayInfo,
  activitiesContext,
}: SingleDayPrompt): string {
  // If dayInfo is not provided, create it with default values
  if (!dayInfo) {
    dayInfo = {
      isFirstDay: day === 1,
      isLastDay: day === tripDetails.totalDays,
      date: calculateDayDate(tripDetails.startDate, day - 1),
    };

    // Try to extract day-specific information from the high-level plan
    try {
      const parsedHighLevelPlan = JSON.parse(highLevelPlanContentResponse);
      const dayPlan = parsedHighLevelPlan.itinerary.find(
        (d: { day: number }) => d.day === day,
      );

      if (dayPlan) {
        // Extract city information
        if (dayPlan.start_city) {
          dayInfo.startCity = dayPlan.start_city;
        }

        if (dayPlan.end_city) {
          dayInfo.endCity = dayPlan.end_city;
        }

        // Extract description if available
        if (dayPlan.description) {
          dayInfo.description = dayPlan.description;
        }
      }
    } catch (error) {
      console.warn(`Error parsing high-level plan for day ${day}: ${error}`);
    }
  }



  // Create a more focused prompt with day-specific information
  let daySpecificInfo = '';
  if (
    dayInfo.startCity &&
    dayInfo.endCity &&
    dayInfo.startCity === dayInfo.endCity
  ) {
    daySpecificInfo = `For Today, i will be exploring ${dayInfo.startCity}.`;
  } else if (dayInfo.startCity && dayInfo.endCity) {
    daySpecificInfo = `For Today, i will start my day in ${dayInfo.startCity} and end in ${dayInfo.endCity}.`;
  }

  if (dayInfo.description) {
    daySpecificInfo += ` ${dayInfo.description}`;
  }

  return `Create a detailed itinerary for Day ${day} of the trip${dayInfo.date ? ` (${dayInfo.date})` : ''}. ${daySpecificInfo}
    Use the following inputs:
      ${dayInfo.isFirstDay ? `- Arrival time: ${tripDetails.arrivalTime}. (take into account the time needed after arrival before the first activity and make sure to include the transportation from the airport)` : ''}
      ${dayInfo.isLastDay ? `- Departure time: ${tripDetails.departureTime}. (don't plan any activities past departure time and make sure to include the transportation to the airport)` : ''}
      -Type of Trip: ${tripDetails.tripType}.
      -Budget Category: ${tripDetails.budget} (budget = low-cost options, normal = mid-range options, luxury = high-end options).
      -List of Interests: ${tripDetails.interests.join(',')}.
      ${tripDetails.cuisinePreferences?.length && tripDetails.cuisinePreferences?.length > 0 ? `-Cuisine Preferences: ${tripDetails.cuisinePreferences.join(', ')}.` : ''}
      ${!dayInfo.isFirstDay ? `- Preferred Wake Up Time: ${tripDetails.wakeUpTime}.` : ''}
      ${!dayInfo.isLastDay ? `- Preferred Sleep Time: ${tripDetails.sleepTime}.` : ''}
      -Number of Adults: ${tripDetails.people.adults}.
      ${tripDetails.people.children > 0 ? `-Number of Children: ${tripDetails.people.children}.` : ''}
      ${activitiesContext ? '\n' + activitiesContext : ''}
      Include:
      - Activities and attractions tailored to the user's interests and intensity level.
      - Time allocation that ensures a balance between exploration, dining, and relaxation.
      Important formatting guidelines:
      - CRITICAL: Every activity MUST include a "city" field specifying the city where the activity takes place ( for transportation between cities, use the city of the departure )
      - For location names in the response, use specific names without city. 
      - Use specific names for restaurants, and activities.
      - Do not include activity type and city name in the activity name.
      Ensure the plan is realistic with strict meal timing:
        - Breakfast: Between wake up time and 1 hour after
        - Lunch: Between 5-6 hours after wake up time
        - Dinner: Between 1 to 2 hours before sleep time
      Meal times must vary each day within these constraints and respect local dining customs.
      ${formatAdditionalRequirements(tripDetails.additionalRequirements)}
      ${model === AiModel.DEEPSEEK ? 'This is the JSON response format instructions and the response should be in JSON format:' : ''}
      ${model === AiModel.DEEPSEEK ? JSON.stringify(response_format_single_day) : ''}
      `.trim();
}
