/* eslint-disable */

import { GenerateTripDto } from '../../dto/generate-trip.dto';

export const constructOneShotPrompt = (tripDto: GenerateTripDto): string => {
  const {
    arrivalCity,
    departureCity,
    startDate,
    endDate,

    tripType,
    budget,
    intensity,
    interests,
    cuisinePreferences = ['Local', 'Traditional', 'International'],
    wakeUpTime,
    sleepTime,
    mustVisitCities,
  } = tripDto;

  return `Create a detailed and optimized travel plan for a trip based on the following inputs:

Trip Intensity: ${intensity}
Arrival Date: ${startDate}.
Departure Date: ${endDate}.
Type of Trip: ${tripType}.
Budget Category: ${budget} (budget = low-cost options, normal = mid-range options, luxury = high-end options).
List of Interests: ${interests.join(', ')}.
Cuisine Preferences: ${cuisinePreferences.join(', ')}.
Preferred Wake Up Time: ${wakeUpTime}.
Preferred Sleep Time: ${sleepTime}.
Must Visit Cities: ${mustVisitCities?.join(', ')}.

The plan should include:
- A day-by-day itinerary with activities, attractions, and meals tailored to the user's interests, trip type, intensity level, and budget and sleep and wake up time.
- For intensity level ${intensity}:
  * 0-3: Focus on relaxation, fewer activities per day, more time at each location, minimal city changes
  * 4-7: Balanced pace, moderate number of activities, some city changes if needed
  * 8-10: Action-packed schedule, multiple activities per day, frequent movement between cities
- Three meals per day (breakfast, lunch, dinner) at local restaurants matching cuisine preferences, based on the trip type and budget category (budget = affordable local spots, normal = mid-range restaurants, luxury = fine dining).
- Transportation details between cities, considering the arrival and departure methods.
- Recommendations for accommodations based on the trip type and budget category (budget = hostels/budget hotels, normal = 3-4 star hotels, luxury = 5-star hotels/resorts).
- Time allocation that ensures a balance between exploration, dining, and relaxation, respecting the preferred wake up time (${wakeUpTime}) and sleep time (${sleepTime}).
- Any additional tips or considerations (e.g., weather, local customs, packing suggestions).
- Cost breakdown should align with the budget category (budget = lower costs, normal = moderate costs, luxury = higher costs).

IMPORTANT FOR LOCATION ACCURACY:
- For all locations, ALWAYS include the country name to avoid ambiguity (e.g., "Eiffel Tower, Paris, France" NOT just "Eiffel Tower, Paris")
- Many locations share names across different countries (e.g., Paris in France vs. Paris in Texas)
- When using function calls for location validation, always provide country context
- Verify that coordinates match the expected country for the location

Ensure the plan is realistic with strict meal timing:
- Breakfast: Between ${wakeUpTime} and 1 hour after
- Lunch: Between 5-6 hours after wake up time
- Dinner: Between 1 to 2 hours before ${sleepTime}
Meal times must vary each day within these constraints and respect local dining customs.

Please provide the response in the following JSON format:
{
  "itinerary": [
    {
      "day": number,
      "activities": [
        {
          "id": string,
          "name": string,
          "type": "activity" | "meal" | "transportation" | "accommodation",
          "description": string,
          "startTime": string,
          "endTime": string,
          "location": string, // ALWAYS include the country name in the location (e.g., "Eiffel Tower, Paris, France")
          "coordinates": {
            "lat": number,
            "lng": number
          },
          "cost": number,
          "costPerNight": number,
          "completed": boolean,
          "city": string // REQUIRED: The city where this activity takes place
        }
      ]
    }
  ],
  "additionalTips": string[],
  "costBreakdown": {
    "accommodation": number,
    "transportation": number,
    "activities": number,
    "meals": number,
    "total_estimated_cost": number
  }
}`;
};
