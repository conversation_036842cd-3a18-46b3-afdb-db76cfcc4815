import { Injectable, Logger } from '@nestjs/common';
import { AiModel } from '../helpers/ai-model.type';
import {
  AiProvider,
  AiProviderConfig,
} from '../interfaces/ai-provider.interface';
import { ConfigService } from '../../config/config.service';

@Injectable()
export class AiProviderFactory {
  private readonly logger = new Logger(AiProviderFactory.name);
  private providers: Map<string, AiProvider> = new Map();

  constructor(private readonly configService: ConfigService) {}

  /**
   * Register an AI provider
   * @param model The AI model enum value
   * @param provider The provider implementation
   */
  registerProvider(model: AiModel, provider: AiProvider): void {
    this.providers.set(model, provider);
    this.logger.log(`Registered provider for model: ${model}`);
  }

  /**
   * Get an AI provider for the specified model
   * @param model The AI model enum value
   * @returns The provider implementation
   */
  getProvider(model: AiModel): AiProvider {
    const provider = this.providers.get(model);

    if (!provider) {
      throw new Error(`No provider registered for model: ${model}`);
    }

    return provider;
  }

  /**
   * Get the provider configuration for the specified model
   * @param model The AI model enum value
   * @returns Provider configuration
   */
  getProviderConfig(model: AiModel): AiProviderConfig {
    const baseConfig = {
      timeout: 60000, // Increased to 60 seconds to handle more complex prompts
      maxRetries: 3,
    };

    const aiConfig = this.configService.getAiConfig();

    switch (model) {
      case AiModel.DEEPSEEK:
        return {
          ...baseConfig,
          baseURL: 'https://api.deepseek.com/v1',
          apiKey: aiConfig.deepseekApiKey,
        };
      case AiModel.GROQ:
        return {
          ...baseConfig,
          baseURL: 'https://api.groq.com/openai/v1',
          apiKey: aiConfig.groqApiKey,
        };
      case AiModel.OPENAI:
      default:
        return {
          ...baseConfig,
          apiKey: aiConfig.openaiApiKey,
        };
    }
  }
}
