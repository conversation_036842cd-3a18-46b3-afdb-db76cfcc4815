/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { ApiKeyManagerService } from '../../api-key-manager/api-key-manager.service';
import { ApiProvider } from '../../config/api-keys';
import { AiModel } from '../helpers/ai-model.type';
import { response_format_high_level_plan } from '../helpers/response-format/high-level-plan';
import { response_format_single_day } from '../helpers/response-format/single-day';
import {
  AiProvider,
  AiProviderConfig,
  CompletionOptions,
  CompletionResponse,
  isAsyncIterable,
  Message,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class OpenAiProvider implements AiProvider {
  private readonly logger = new Logger(OpenAiProvider.name);
  private client: OpenAI;
  private apiKey: string;
  private readonly modelConfig = {
    [AiModel.OPENAI]: { model: 'gpt-4.1-mini' },
  };

  constructor(
    private readonly apiKeyManagerService?: ApiKeyManagerService,
  ) { }

  initialize(config: AiProviderConfig): void {
    this.apiKey = config.apiKey;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      timeout: config.timeout,
      maxRetries: config.maxRetries,
    });
    this.logger.log('OpenAI provider initialized');
  }

  async getCompletion(
    options: CompletionOptions,
  ): Promise<CompletionResponse | AsyncIterable<any>> {
    try {
      const result = await this.client.chat.completions.create(options as any);

      // Record successful API call if API key manager is available
      if (this.apiKeyManagerService && this.apiKey) {
        // Calculate approximate token usage
        const promptTokens = this.estimatePromptTokens(options.messages);
        const responseTokens = isAsyncIterable(result)
          ? 0
          : this.estimateResponseTokens(result as any);
        const totalTokens = promptTokens + responseTokens;

        // Record success with token usage
        await this.apiKeyManagerService.recordSuccess(
          ApiProvider.OPENAI,
          this.apiKey,
          totalTokens,
        );
      }

      // If streaming is enabled, return the stream directly
      if (isAsyncIterable(result)) {
        return result;
      }

      // Process the response and convert to standardized format
      const response = result as OpenAI.Chat.Completions.ChatCompletion;
      return {
        id: response.id,
        choices: response.choices.map((choice) => ({
          index: choice.index,
          message: choice.message
            ? {
              role: choice.message.role,
              content: choice.message.content || '',
            }
            : undefined,
          finish_reason: choice.finish_reason,
        })),
        created: response.created,
        model: response.model,
        object: response.object,
      };
    } catch (error) {
      // Record error if API key manager is available
      if (this.apiKeyManagerService && this.apiKey) {
        let errorType: 'rate_limit' | 'auth' | 'server' | 'other' = 'other';

        if (error.status === 429 || error.message?.includes('rate limit')) {
          errorType = 'rate_limit';
        } else if (
          error.status === 401 ||
          error.status === 403 ||
          error.message?.includes('authentication') ||
          error.message?.includes('invalid api key')
        ) {
          errorType = 'auth';
        } else if (
          error.status >= 500 ||
          error.message?.includes('server error')
        ) {
          errorType = 'server';
        }

        await this.apiKeyManagerService.recordError(
          ApiProvider.OPENAI,
          this.apiKey,
          errorType,
        );
      }

      // Re-throw the error
      throw error;
    }
  }

  getModelConfig(aiModel: AiModel, forSingleDay: boolean): any {
    const modelConfig =
      this.modelConfig[aiModel] || this.modelConfig[AiModel.OPENAI];

    const responseFormat = forSingleDay
      ? response_format_single_day
      : response_format_high_level_plan;

    return {
      ...modelConfig,
      ...responseFormat,
    };
  }

  /**
   * Estimate the number of tokens in the prompt messages
   * This is a simple estimation based on character count
   * @param messages The messages to estimate tokens for
   * @returns Estimated token count
   */
  private estimatePromptTokens(messages: Message[]): number {
    // Simple estimation: ~4 characters per token for English text
    const totalChars = messages.reduce(
      (sum, msg) => sum + (msg.content?.length || 0),
      0,
    );
    return Math.ceil(totalChars / 4);
  }

  /**
   * Estimate the number of tokens in the response
   * @param response The completion response
   * @returns Estimated token count
   */
  private estimateResponseTokens(response: CompletionResponse): number {
    // Get content from all choices
    const content = response.choices
      .map((choice) => choice.message?.content || '')
      .join('');

    // Simple estimation: ~4 characters per token for English text
    return Math.ceil(content.length / 4);
  }
}
