import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { MongoError } from 'mongodb';
import { DatabaseErrorHandler } from '../common/database-error.util';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    this.logger.debug('Exception caught by global filter:', exception);

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof MongoError) {
      // Use DatabaseErrorHandler for more comprehensive MongoDB error handling
      if (DatabaseErrorHandler.isDuplicateKeyError(exception)) {
        status = HttpStatus.CONFLICT;
        message = 'Duplicate entry';
      } else if (DatabaseErrorHandler.isDatabaseConnectionError(exception)) {
        status = HttpStatus.SERVICE_UNAVAILABLE;
        message = 'Database service temporarily unavailable';
        this.logger.error('Database connection error:', exception.stack);
      } else {
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message = 'Database error occurred';
        this.logger.error('Unhandled MongoDB error:', exception.stack);
      }
    } else if (DatabaseErrorHandler.isValidationError(exception)) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Invalid data provided';
    } else if (DatabaseErrorHandler.isCastError(exception)) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Invalid data format';
    } else {
      // Log unexpected errors
      this.logger.error('Unhandled exception:', exception);
    }

    response.status(status).json({
      statusCode: status,
      message,
      timestamp: new Date().toISOString(),
    });
  }
}
