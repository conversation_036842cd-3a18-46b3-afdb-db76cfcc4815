/* eslint-disable */

import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { NextFunction, Request, Response } from 'express';
import { UsersService } from '../users/users.service';
@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly userService: UsersService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = req.headers.authorization;
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const decoded = this.jwtService.verify(token.split(' ')[1]);

    if (!decoded) {
      throw new UnauthorizedException('Invalid token');
    }

    if (!decoded.sub) {
      throw new UnauthorizedException('Invalid token');
    }

    const user = await this.userService.findOne(decoded.sub);

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    req.user = user;

    next();
  }
}
