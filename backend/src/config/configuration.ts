import { registerAs } from '@nestjs/config';

/**
 * App configuration
 */
export const appConfig = registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  allowedOrigins: process.env.ALLOWED_ORIGINS?.split(','),
}));

/**
 * Database configuration
 */
export const databaseConfig = registerAs('database', () => ({
  uri: process.env.MONGODB_URI,
}));

/**
 * JWT configuration
 */
export const jwtConfig = registerAs('jwt', () => ({
  secret: process.env.JWT_SECRET,
  signOptions: {
    expiresIn: process.env.JWT_EXPIRES_IN,
    algorithm: 'HS512',
    audience: 'itrip',
    issuer: 'itrip',
  },
  verifyOptions: {
    algorithms: ['HS512'],
    audience: 'itrip',
    issuer: 'itrip',
  },
}));

/**
 * AI configuration
 */
export const aiConfig = registerAs('ai', () => ({
  // OpenAI Configuration
  openaiApiKey: process.env.OPENAI_API_KEY,
  openaiApiKeyBackup: process.env.OPENAI_API_KEY_BACKUP,
  openaiAssistantId: process.env.OPENAI_ASSISTANT_ID,
  useAssistantsApi: process.env.USE_ASSISTANTS_API === 'true',

  // Groq Configuration
  groqApiKey: process.env.GROQ_API_KEY,
  groqApiKeyBackup: process.env.GROQ_API_KEY_BACKUP,

  // DeepSeek Configuration
  deepseekApiKey: process.env.DEEPSEEK_API_KEY,
  deepseekApiKeyBackup: process.env.DEEPSEEK_API_KEY_BACKUP,

  // Generation Configuration
  generationMode: process.env.GENERATION_MODE || 'multi_round',
  modelToUse: process.env.MODEL_TO_USE || 'groq',
  maxDaysToGenerate: parseInt(process.env.MAX_DAYS_TO_GENERATE || '2', 10),

  // Streaming Configuration
  useStreaming: process.env.USE_STREAMING === 'true',

  // WebSocket Configuration
  useWebsocket: process.env.USE_WEBSOCKET === 'true',
  useSockets: process.env.USE_SOCKETS === 'true',
}));

/**
 * Google configuration
 */
export const googleConfig = registerAs('google', () => ({
  mapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
  oauth: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackUrl: process.env.GOOGLE_CALLBACK_URL,
    scope: ['email', 'profile'],
  },
  clients: {
    iosClientId: process.env.GOOGLE_IOS_CLIENT_ID,
    androidClientId: process.env.GOOGLE_ANDROID_CLIENT_ID,
    webClientId: process.env.GOOGLE_WEB_CLIENT_ID,
  },
}));

/**
 * OpenRouteService configuration
 */
export const openRouteServiceConfig = registerAs('openRouteService', () => ({
  apiKey: process.env.ORS_API_KEY,
  apiUrl: 'https://api.openrouteservice.org',
}));

/**
 * OpenCage Data configuration
 */
export const openCageConfig = registerAs('opencage', () => ({
  apiKey: process.env.OPENCAGE_API_KEY,
  apiUrl: 'https://api.opencagedata.com',
}));

/**
 * Geocode Earth configuration
 */
export const geocodeConfig = registerAs('geocode', () => ({
  apiKey: process.env.GEOCODE_API_KEY,
  apiUrl: 'https://api.geocode.earth',
}));

/**
 * Throttling configuration
 */
export const throttleConfig = registerAs('throttle', () => ({
  short: {
    ttl: 1000,
    limit: 100,
  },
  medium: {
    ttl: 10000,
    limit: 400,
  },
  long: {
    ttl: 60000,
    limit: 1200,
  },
}));

/**
 * Token balance configuration
 */
export const tokenConfig = registerAs('token', () => ({
  initialBalance: parseInt(process.env.INITIAL_TOKEN_BALANCE || '5000', 10),
  estimatedSingleDayResponse: parseInt(
    process.env.ESTIMATED_SINGLE_DAY_RESPONSE || '200',
    10,
  ),
  // Token estimation ratios
  oneShotResponseRatio: parseFloat(
    process.env.ONE_SHOT_RESPONSE_RATIO || '2.5',
  ),
  multiRoundResponseRatio: parseFloat(
    process.env.MULTI_ROUND_RESPONSE_RATIO || '1.2',
  ),
  defaultResponseRatio: parseFloat(process.env.DEFAULT_RESPONSE_RATIO || '1.5'),
  singleDayEstimate: parseInt(process.env.SINGLE_DAY_ESTIMATE || '300', 10),
}));

/**
 * Payment configuration
 */
export const paymentConfig = registerAs('payment', () => ({
  appleAppSharedSecret: process.env.APPLE_APP_SHARED_SECRET,
  googlePlayServiceAccount: process.env.GOOGLE_PLAY_SERVICE_ACCOUNT,
  allowMockPurchases: process.env.ALLOW_MOCK_PURCHASES === 'true',
}));

/**
 * Static files configuration
 */
export const staticFilesConfig = registerAs('staticFiles', () => ({
  countryFlags: {
    rootPath: 'static/country-flags-main',
    serveRoot: '/flags',
    extensions: ['png'],
  },
}));

/**
 * Trip recovery configuration
 */
export const tripRecoveryConfig = registerAs('tripRecovery', () => ({
  threshold: parseInt(process.env.TRIP_STUCK_THRESHOLD || '20', 10),
}));

/**
 * Location validation configuration
 */
export const locationValidationConfig = registerAs('locationValidation', () => ({
  proximityRadiusKm: parseInt(process.env.LOCATION_PROXIMITY_RADIUS_KM || '100', 10),
}));


/**
 * Unsplash configuration
 */
export const unsplashConfig = registerAs('unsplash', () => ({
  apiKey: process.env.UNSPLASH_API_KEY,
  cacheExpiryDays: parseInt(process.env.UNSPLASH_CACHE_EXPIRY_DAYS || '30', 10),
}));

/**
 * Resend configuration
 */
export const resendConfig = registerAs('resend', () => ({
  apiKey: process.env.RESEND_API_KEY,
}));
