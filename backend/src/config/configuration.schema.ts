import * as Jo<PERSON> from 'joi';

/**
 * Validation schema for environment variables
 * All environments use environment variables (development uses .env file)
 */
export const validationSchema = Joi.object({
  // Node environment
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),

  // Server configuration
  PORT: Joi.number().default(3000),
  ALLOWED_ORIGINS: Joi.string().default('http://localhost:3000'),

  // MongoDB configuration
  MONGODB_URI: Joi.string().required().description('MongoDB connection string'),

  // JWT configuration
  JWT_SECRET: Joi.string().required().description('JWT secret key'),
  JWT_EXPIRES_IN: Joi.string()
    .default('30d')
    .description('JWT expiration time'),

  // OpenAI configuration
  OPENAI_API_KEY: Joi.string().description('OpenAI API key'),
  OPENAI_API_KEY_BACKUP: Joi.string().description('Backup OpenAI API key'),
  OPENAI_ASSISTANT_ID: Joi.string().description('OpenAI Assistant ID'),
  USE_ASSISTANTS_API: Joi.boolean()
    .default(false)
    .description('Whether to use OpenAI Assistants API'),

  // Groq configuration
  GROQ_API_KEY: Joi.string().description('Groq API key'),
  GROQ_API_KEY_BACKUP: Joi.string().description('Backup Groq API key'),

  // DeepSeek configuration
  DEEPSEEK_API_KEY: Joi.string().description('DeepSeek API key'),
  DEEPSEEK_API_KEY_BACKUP: Joi.string().description('Backup DeepSeek API key'),

  // Generation configuration
  GENERATION_MODE: Joi.string()
    .valid('multi_round', 'one_shot')
    .default('multi_round')
    .description('Generation mode'),
  MODEL_TO_USE: Joi.string()
    .valid('openai', 'groq', 'deepseek')
    .default('groq')
    .description('AI model to use'),
  MAX_DAYS_TO_GENERATE: Joi.number()
    .default(2)
    .description('Maximum number of days to generate'),

  // Streaming configuration
  USE_STREAMING: Joi.string()
    .valid('true', 'false')
    .default(false)
    .description('Whether to use AI streaming'),

  // WebSocket configuration
  USE_WEBSOCKET: Joi.string()
    .valid('true', 'false')
    .default(true)
    .description('Whether to use WebSocket for client notifications'),
  USE_SOCKETS: Joi.string()
    .valid('true', 'false')
    .default(true)
    .description('Legacy socket setting - will be removed'),

  // Google configuration
  GOOGLE_MAPS_API_KEY: Joi.string().description('Google Maps API key'),
  GOOGLE_CLIENT_ID: Joi.string().description('Google OAuth client ID'),
  GOOGLE_CLIENT_SECRET: Joi.string().description('Google OAuth client secret'),
  GOOGLE_CALLBACK_URL: Joi.string()
    .default('http://localhost:3000/auth/google/callback')
    .description('Google OAuth callback URL'),
  GOOGLE_IOS_CLIENT_ID: Joi.string().description('Google OAuth iOS client ID'),
  GOOGLE_ANDROID_CLIENT_ID: Joi.string().description(
    'Google OAuth Android client ID',
  ),
  GOOGLE_WEB_CLIENT_ID: Joi.string().description('Google OAuth web client ID'),

  // OpenRouteService configuration
  ORS_API_KEY: Joi.string().description('OpenRouteService API key'),

  // OpenCage Data configuration
  OPENCAGE_API_KEY: Joi.string().description('OpenCage Data API key'),

  // Geocode Earth configuration
  GEOCODE_API_KEY: Joi.string().description('Geocode Earth API key'),

  // Location validation configuration
  LOCATION_PROXIMITY_RADIUS_KM: Joi.number()
    .default(100)
    .description('Maximum allowed distance in kilometers for location proximity validation'),

  // Token balance configuration
  INITIAL_TOKEN_BALANCE: Joi.number()
    .default(5000)
    .description('Initial token balance for new users'),
  ESTIMATED_SINGLE_DAY_RESPONSE: Joi.number()
    .default(200)
    .description('Estimated token usage for a single day response'),

  // Token estimation ratios
  ONE_SHOT_RESPONSE_RATIO: Joi.number()
    .default(2.5)
    .description('Response ratio for one-shot trip generation'),
  MULTI_ROUND_RESPONSE_RATIO: Joi.number()
    .default(1.2)
    .description('Response ratio for multi-round trip generation'),
  DEFAULT_RESPONSE_RATIO: Joi.number()
    .default(1.5)
    .description('Default response ratio for token estimation'),
  SINGLE_DAY_ESTIMATE: Joi.number()
    .default(300)
    .description('Estimated tokens for single day generation'),

  // Payment configuration
  APPLE_APP_SHARED_SECRET: Joi.string().description(
    'Apple App Store shared secret for receipt validation',
  ),
  GOOGLE_PLAY_SERVICE_ACCOUNT: Joi.string().description(
    'Google Play service account JSON for receipt validation',
  ),
  ALLOW_MOCK_PURCHASES: Joi.string()
    .valid('true', 'false')
    .default(false)
    .description('Whether to allow mock purchases in development mode'),

  // Trip recovery configuration
  TRIP_STUCK_THRESHOLD: Joi.number()
    .default(20)
    .description('Threshold for trip recovery in minutes'),

  // Unsplash configuration
  UNSPLASH_API_KEY: Joi.string().description('Unsplash API key'),
  UNSPLASH_CACHE_EXPIRY_DAYS: Joi.number()
    .default(30)
    .description('Number of days to cache Unsplash images'),

  // Resend configuration
  RESEND_API_KEY: Joi.string().description('Resend API key for email sending'),
});
