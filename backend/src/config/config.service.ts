/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { JwtModuleOptions } from '@nestjs/jwt';
import { ServeStaticModuleOptions } from '@nestjs/serve-static';
import { ThrottlerModuleOptions } from '@nestjs/throttler';
import { StrategyOptions } from 'passport-google-oauth20';
import { join } from 'path';
import { AiModel, GenerationMode } from './ai.config';

/**
 * Centralized configuration service that provides type-safe access to all configuration values
 * Supports environment variable management for all environments
 */
@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);

  constructor(private configService: NestConfigService) {
    this.initializeConfigurationService();
  }

  /**
   * Initialize the configuration service with environment information
   */
  private initializeConfigurationService(): void {
    const nodeEnv = this.get<string>('app.nodeEnv', 'development');
    const configSource = this.determineConfigurationSource();

    this.logger.log(
      `Configuration service initialized for ${nodeEnv} environment`,
    );
    this.logger.log(`Configuration source: ${configSource}`);

    // Log configuration summary (without sensitive data)
    this.logConfigurationSummary();
  }

  /**
   * Determine the configuration source being used
   */
  private determineConfigurationSource(): string {
    const nodeEnv = process.env.NODE_ENV || 'development';

    if (nodeEnv === 'development') {
      return '.env file';
    } else {
      return 'Environment variables';
    }
  }

  /**
   * Log configuration summary for debugging (without sensitive values)
   */
  private logConfigurationSummary(): void {
    const summary = {
      nodeEnv: this.get<string>('app.nodeEnv'),
      port: this.get<number>('app.port'),
      databaseConfigured: !!this.get<string>('database.uri'),
      jwtConfigured: !!this.get<string>('jwt.secret'),
      aiProvidersConfigured: {
        openai: !!this.get<string>('ai.openaiApiKey'),
        groq: !!this.get<string>('ai.groqApiKey'),
        deepseek: !!this.get<string>('ai.deepseekApiKey'),
      },
    };
  }

  /**
   * Get a configuration value by key with type safety
   * @param key The configuration key
   * @param defaultValue Optional default value if the key is not found
   * @returns The configuration value
   */
  get<T>(key: string, defaultValue?: T): T {
    try {
      // Use the NestJS ConfigService to get the value
      const value = this.configService.get(key);

      // If the value is undefined and a default value is provided, return the default value
      if (value === undefined) {
        if (defaultValue === undefined) {
          this.logger.warn(
            `Configuration key "${key}" not found and no default value provided`,
          );
        }
        return defaultValue as T;
      }

      // Return the value
      return value as T;
    } catch (error) {
      this.logger.error(
        `Error getting configuration key "${key}": ${error.message}`,
      );
      return defaultValue as T;
    }
  }

  /**
   * Get the MongoDB connection URI
   * @returns The MongoDB connection URI
   */
  getMongoDbUri(): string {
    return this.get<string>('database.uri');
  }

  /**
   * Get the JWT module options for NestJS JWT module
   * @returns JWT module options
   */
  getJwtConfig(): JwtModuleOptions {
    return {
      secret: this.get<string>('jwt.secret'),
      signOptions: this.get('jwt.signOptions'),
      verifyOptions: this.get('jwt.verifyOptions'),
    };
  }

  /**
   * Get the Google OAuth strategy options
   * @returns Google OAuth strategy options
   */
  getGoogleOAuthConfig(): StrategyOptions {
    return {
      clientID: this.get<string>('google.oauth.clientId'),
      clientSecret: this.get<string>('google.oauth.clientSecret'),
      callbackURL: this.get<string>('google.oauth.callbackUrl'),
      scope: this.get<string[]>('google.oauth.scope'),
    };
  }

  /**
   * Get the Google client configuration for mobile apps
   * @returns Google client configuration
   */
  getGoogleClientConfig() {
    return {
      iosClientId: this.get<string>('google.clients.iosClientId'),
      androidClientId: this.get<string>('google.clients.androidClientId'),
      webClientId: this.get<string>('google.clients.webClientId'),
    };
  }

  /**
   * Get the throttler module options
   * @returns Throttler module options
   */
  getThrottlerConfig(): ThrottlerModuleOptions {
    return [
      {
        name: 'short',
        ttl: this.get<number>('throttle.short.ttl'),
        limit: this.get<number>('throttle.short.limit'),
      },
      {
        name: 'medium',
        ttl: this.get<number>('throttle.medium.ttl'),
        limit: this.get<number>('throttle.medium.limit'),
      },
      {
        name: 'long',
        ttl: this.get<number>('throttle.long.ttl'),
        limit: this.get<number>('throttle.long.limit'),
      },
    ];
  }

  /**
   * Get the static files module options
   * @returns Static files module options
   */
  getStaticFilesConfig(): ServeStaticModuleOptions {
    // Define the type for country flags configuration
    interface CountryFlagsConfig {
      rootPath: string;
      serveRoot: string;
      extensions: string[];
    }

    const countryFlags = this.get<CountryFlagsConfig>(
      'staticFiles.countryFlags',
    );

    if (!countryFlags) {
      this.logger.warn(
        'Static files configuration not found, using default values',
      );
      return {
        rootPath: join('static/country-flags-main'),
        serveRoot: '/flags',
        serveStaticOptions: {
          extensions: ['png'],
        },
      };
    }

    return {
      rootPath: join(countryFlags.rootPath),
      serveRoot: countryFlags.serveRoot,
      serveStaticOptions: {
        extensions: countryFlags.extensions,
      },
    };
  }

  /**
   * Get the CORS configuration
   * @returns CORS configuration
   */
  getCorsConfig() {
    return {
      origin: this.get<string[]>('app.allowedOrigins'),
      methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
      credentials: true,
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-CSRF-Token',
      ],
      exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining'],
      maxAge: 3600,
    };
  }

  /**
   * Get the initial token balance for new users
   * @returns Initial token balance
   */
  getInitialTokenBalance(): number {
    return this.get<number>('token.initialBalance');
  }

  /**
   * Get the estimated token usage for a single day response
   * @returns Estimated token usage
   */
  getEstimatedSingleDayResponse(): number {
    return this.get<number>('token.estimatedSingleDayResponse');
  }

  /**
   * Get token estimation configuration
   * @returns Token estimation configuration
   */
  getTokenEstimationConfig() {
    return {
      oneShotResponseRatio: this.get<number>('token.oneShotResponseRatio'),
      multiRoundResponseRatio: this.get<number>(
        'token.multiRoundResponseRatio',
      ),
      defaultResponseRatio: this.get<number>('token.defaultResponseRatio'),
      singleDayEstimate: this.get<number>('token.singleDayEstimate'),
      estimatedSingleDayResponse: this.get<number>(
        'token.estimatedSingleDayResponse',
      ),
    };
  }

  /**
   * Get the OpenRouteService configuration
   * @returns OpenRouteService configuration
   */
  getOpenRouteServiceConfig() {
    return {
      apiKey: this.get<string>('openRouteService.apiKey'),
      apiUrl: this.get<string>('openRouteService.apiUrl'),
    };
  }

  /**
   * Get the Google Maps API key
   * @returns Google Maps API key
   */
  getGoogleMapsApiKey() {
    return this.get<string>('google.mapsApiKey');
  }

  /**
   * Get the OpenCage Data API key
   * @returns OpenCage Data API key
   */
  getOpenCageConfig() {
    return {
      apiKey: this.get<string>('opencage.apiKey'),
      apiUrl: this.get<string>('opencage.apiUrl'),
    };
  }

  /**
   * Get the Geocode Earth API configuration
   * @returns Geocode Earth API configuration
   */
  getGeocodeConfig() {
    return {
      apiKey: this.get<string>('geocode.apiKey'),
      apiUrl: this.get<string>('geocode.apiUrl'),
    };
  }

  /**
   * Get ai config
   * @returns ai config
   */
  getAiConfig() {
    return {
      openaiApiKey: this.get<string>('ai.openaiApiKey'),
      openaiApiKeyBackup: this.get<string>('ai.openaiApiKeyBackup'),
      openaiAssistantId: this.get<string>('ai.openaiAssistantId'),
      useAssistantsApi: this.get<boolean>('ai.useAssistantsApi'),
      groqApiKey: this.get<string>('ai.groqApiKey'),
      groqApiKeyBackup: this.get<string>('ai.groqApiKeyBackup'),
      deepseekApiKey: this.get<string>('ai.deepseekApiKey'),
      deepseekApiKeyBackup: this.get<string>('ai.deepseekApiKeyBackup'),
    };
  }

  /**
   * Get location validation configuration
   * @returns Location validation configuration
   */
  getLocationValidationConfig() {
    return {
      proximityRadiusKm: this.get<number>('locationValidation.proximityRadiusKm', 100),
    };
  }

  /**
   * Get the generation configuration
   * @returns Generation configuration
   */
  getGenerationConfig() {
    return {
      generationMode: this.get<string>('ai.generationMode') as GenerationMode,
      modelToUse: this.get<string>('ai.modelToUse') as AiModel,
      maxDaysToGenerate: this.get<number>('ai.maxDaysToGenerate'),
      useStreaming: this.get<boolean>('ai.useStreaming'),
    };
  }

  /**
   * Get the WebSocket configuration
   * @returns WebSocket configuration
   */
  getWebsocketConfig() {
    return {
      useWebsocket: this.get<boolean>('ai.useWebsocket'),
      useSockets: this.get<boolean>('ai.useSockets'),
    };
  }

  /**
   * Get the payment configuration
   * @returns Payment configuration
   */
  getPaymentConfig() {
    return {
      appleAppSharedSecret: this.get<string>('payment.appleAppSharedSecret'),
      googlePlayServiceAccount: this.get<string>(
        'payment.googlePlayServiceAccount',
      ),
      allowMockPurchases: this.get<boolean>(
        'payment.allowMockPurchases',
        false,
      ),
    };
  }

  /**
   * Get the trip recovery configuration
   * @returns Trip recovery configuration
   */
  getTripRecoveryConfig() {
    return {
      threshold: this.get<number>('tripRecovery.threshold'),
    };
  }

  /**
   * Get the Unsplash configuration
   * @returns Unsplash configuration
   */
  getUnsplashConfig() {
    return {
      apiKey: this.get<string>('unsplash.apiKey'),
      cacheExpiryDays: this.get<number>('unsplash.cacheExpiryDays'),
    };
  }

  /**
   * Get the Resend configuration
   * @returns Resend configuration
   */
  getResendConfig() {
    return {
      apiKey: this.get<string>('resend.apiKey'),
    };
  }

  /**
   * Get configuration source information
   * @returns Configuration source details
   */
  getConfigurationSource(): {
    source: string;
    nodeEnv: string;
  } {
    const nodeEnv = this.get<string>('app.nodeEnv', 'development');

    let source = 'Environment variables';
    if (nodeEnv === 'development') {
      source = '.env file';
    }

    return {
      source,
      nodeEnv,
    };
  }

  /**
   * Validate that all required configuration is present
   * @returns Validation result
   */
  validateConfiguration(): {
    isValid: boolean;
    missingRequired: string[];
    warnings: string[];
  } {
    const requiredConfigs = ['database.uri', 'jwt.secret'];

    const optionalButRecommended = [
      'ai.openaiApiKey',
      'ai.groqApiKey',
      'google.mapsApiKey',
      'openRouteService.apiKey',
      'opencage.apiKey',
      'geocode.apiKey',
    ];

    const missingRequired = requiredConfigs.filter((key) => !this.get(key));
    const warnings = optionalButRecommended.filter((key) => !this.get(key));

    return {
      isValid: missingRequired.length === 0,
      missingRequired,
      warnings,
    };
  }

  /**
   * Get a safe configuration summary (without sensitive data)
   * @returns Safe configuration summary
   */
  getConfigurationSummary(): Record<string, any> {
    const configSource = this.getConfigurationSource();
    const validation = this.validateConfiguration();

    return {
      source: configSource,
      validation,
      services: {
        database: !!this.get('database.uri'),
        jwt: !!this.get('jwt.secret'),
        ai: {
          openai: !!this.get('ai.openaiApiKey'),
          groq: !!this.get('ai.groqApiKey'),
          deepseek: !!this.get('ai.deepseekApiKey'),
        },
        google: {
          maps: !!this.get('google.mapsApiKey'),
          oauth: !!this.get('google.clientId'),
        },
        openRouteService: !!this.get('openRouteService.apiKey'),
        opencage: !!this.get('opencage.apiKey'),
        geocode: !!this.get('geocode.apiKey'),
        payments: {
          apple: !!this.get('payment.appleAppSharedSecret'),
          google: !!this.get('payment.googlePlayServiceAccount'),
        },
        resend: !!this.get('resend.apiKey'),
      },
      app: {
        nodeEnv: this.get('app.nodeEnv'),
        port: this.get('app.port'),
      },
    };
  }
}
