import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { ConfigService } from './config.service';
import { validationSchema } from './configuration.schema';
import {
  appConfig,
  databaseConfig,
  jwtConfig,
  aiConfig,
  googleConfig,
  throttleConfig,
  tokenConfig,
  staticFilesConfig,
  openRouteServiceConfig,
  paymentConfig,
  tripRecoveryConfig,
  openCageConfig,
  geocodeConfig,
  locationValidationConfig,
  unsplashConfig,
  resendConfig,
} from './configuration';

/**
 * Configuration module that provides centralized access to all configuration values
 */
@Module({})
export class ConfigModule {
  static forRoot(): DynamicModule {
    return {
      module: ConfigModule,
      global: true,
      imports: [
        NestConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
          validationSchema,
          load: [
            appConfig,
            databaseConfig,
            jwtConfig,
            aiConfig,
            googleConfig,
            throttleConfig,
            tokenConfig,
            staticFilesConfig,
            openRouteServiceConfig,
            paymentConfig,
            tripRecoveryConfig,
            openCageConfig,
            geocodeConfig,
            locationValidationConfig,
            unsplashConfig,
            resendConfig,
          ],
          validationOptions: {
            allowUnknown: true,
            abortEarly: false,
          },
        }),
      ],
      providers: [ConfigService],
      exports: [ConfigService],
    };
  }
}
