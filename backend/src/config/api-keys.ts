/**
 * API Key configuration
 *
 * This file contains the configuration for API keys used in the application.
 * Keys are organized by provider and can have different weights and rate limits.
 */

export enum ApiProvider {
  OPENAI = 'openai',
  GROQ = 'groq',
  DEEPSEEK = 'deepseek',
}

export interface ApiKeyConfig {
  key: string;
  provider: ApiProvider;
  weight: number;
  rateLimit: {
    requestsPerMinute: number;
    tokensPerMinute?: number;
  };
  isActive: boolean;
  description?: string;
}

// Configuration for the API key manager
export const apiKeyManagerConfig = {
  // How often to reset usage counters (in milliseconds)
  usageResetInterval: 600000, // 1 hour

  // Maximum number of consecutive errors before marking a key as problematic
  maxConsecutiveErrors: 3,

  // Time to wait before retrying a problematic key (in milliseconds)
  problematicKeyRetryDelay: 300000, // 5 minutes

  // Whether to automatically disable keys that exceed error thresholds
  autoDisableProblematicKeys: false,

  // Whether to log key usage statistics
  logUsageStatistics: true,

  // How often to log usage statistics (in milliseconds)
  usageStatisticsLogInterval: 300000, // 5 minutes
};
