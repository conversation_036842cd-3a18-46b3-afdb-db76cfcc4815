/* eslint-disable */

import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { ConfigService } from '../../config/config.service';

interface JwtPayload {
  sub: string;
  email: string;
  iat: number;
  exp: number;
}

@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);
  private readonly isDevelopment: boolean;

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    this.isDevelopment =
      this.configService.get<string>('app.nodeEnv') === 'development';
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      const token = this.extractTokenFromHeader(client);

      if (!token) {
        if (this.isDevelopment) {
          client['user'] = { sub: 'dev-user-id', email: '<EMAIL>' };
        }
        return this.isDevelopment;
      }

      try {
        const payload = this.jwtService.verify(token);
        client['user'] = payload;
        return true;
      } catch (error) {
        if (this.isDevelopment) {
          client['user'] = { sub: 'dev-user-id', email: '<EMAIL>' };
          return true; // Allow access in development even with invalid token
        }
        this.logger.error(error);
        throw new WsException('Invalid token');
      }
    } catch (err) {
      if (this.isDevelopment) {
        const client: Socket = context.switchToWs().getClient();
        // Even in development, we need to attach a mock user for room joining
        client['user'] = { sub: 'dev-user-id', email: '<EMAIL>' };
        return true; // Allow access in development
      }
      this.logger.error(err);
      throw new WsException(err.message);
    }
  }

  private extractTokenFromHeader(client: Socket): string | undefined {
    const auth = client.handshake?.auth?.token;
    if (!auth) {
      if (this.isDevelopment) {
        return;
      }
    }
    return auth;
  }
}
