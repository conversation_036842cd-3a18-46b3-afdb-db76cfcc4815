/* eslint-disable */

import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerRequest } from '@nestjs/throttler';

@Injectable()
export class WsThrottlerGuard extends ThrottlerGuard {
  private readonly storage = new Map<string, number[]>();
  private readonly logger = new Logger(WsThrottlerGuard.name);

  protected async getTracker(req: Record<string, any>): Promise<string> {
    const ip = req.handshake?.address || 'unknown';
    return ip;
  }

  getRequestResponse(context: ExecutionContext): {
    req: Record<string, any>;
    res: Record<string, any>;
  } {
    const ctx = context.switchToWs();
    const client = ctx.getClient();
    return {
      req: client,
      res: {
        setHeader: () => {},
        end: () => {},
        statusCode: 200,
      },
    };
  }

  async handleRequest(requestProps: ThrottlerRequest): Promise<boolean> {
    const { context, limit, ttl, throttler, blockDuration, generateKey } =
      requestProps;
    const client = context.switchToWs().getClient();
    const tracker = await this.getTracker(client);
    const key = generateKey(context, tracker, throttler.name!);

    const { totalHits, timeToExpire, isBlocked, timeToBlockExpire } =
      await this.storageService.increment(
        key,
        ttl,
        limit,
        blockDuration,
        throttler?.name!,
      );

    if (isBlocked) {
      await this.throwThrottlingException(context, {
        limit,
        ttl,
        key,
        tracker,
        totalHits,
        timeToExpire,
        isBlocked,
        timeToBlockExpire,
      });
    }

    return true;
  }
}
