import { Module, forwardRef } from '@nestjs/common';
import { StreamGateway } from './stream.gateway';
import { WsAuthGuard } from './guards/ws-auth.guard';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.getJwtConfig(),
    }),
  ],
  providers: [StreamGateway, WsAuthGuard],
  exports: [StreamGateway, WsAuthGuard],
})
export class WebSocketModule {}
