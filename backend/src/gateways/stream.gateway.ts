/* eslint-disable */

import { Logger, UseGuards } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { WsAuthGuard } from './guards/ws-auth.guard';
import { WsThrottlerGuard } from './guards/ws-throttler.guard';

interface RoomPayload {
  roomId: string;
}

export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  INFO = 'info',
  INSUFFICIENT_BALANCE = 'insufficient_balance',
}

export interface StreamChunk {
  type: string;
  content: string;
  progress?: number;
  elapsedTime?: number;
  notificationType?: NotificationType;
  // Balance information for insufficient balance notifications
  available?: number;
  needed?: number;
  // Allow for additional properties that might be needed in the future
  [key: string]: any;
}

export interface NotificationMessage {
  title: string;
  body: string;
  type: NotificationType;
  // Additional data that might be useful for the client
  data?: {
    available?: number;
    needed?: number;
    [key: string]: any;
  };
}

@WebSocketGateway({
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
    ],
    methods: ['GET', 'POST'],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  },
  transports: ['websocket'],
  pingTimeout: 30000,
  pingInterval: 10000,
  maxHttpBufferSize: 1e6, // 1MB max message size
  allowEIO3: false,
  namespace: '/',
  path: '/socket.io/',
  serveClient: false,
})
@UseGuards(WsThrottlerGuard)
export class StreamGateway {
  @WebSocketServer()
  server: Server;
  private readonly logger = new Logger(StreamGateway.name);
  private clients: Map<string, Socket> = new Map();
  private rooms: Map<string, Set<string>> = new Map();

  @UseGuards(WsAuthGuard)
  handleConnection(client: Socket) {
    this.clients.set(client.id, client);
    client.emit('connection', { message: 'Welcome to the server!' });
    // If user is authenticated, join user room
    if (client['user'] && client['user'].sub) {
      this.joinUserRoom(client, client['user'].sub);
    }
  }

  handleDisconnect(client: Socket) {
    this.clients.delete(client.id);

    // Remove client from all rooms
    this.rooms.forEach((clients, roomId) => {
      if (clients.has(client.id)) {
        clients.delete(client.id);
        if (clients.size === 0) {
          this.rooms.delete(roomId);
        }
      }
    });
  }

  private joinUserRoom(client: Socket, userId: string) {
    const userRoomId = userId;
    if (!this.rooms.has(userRoomId)) {
      this.rooms.set(userRoomId, new Set());
    }
    this.rooms.get(userRoomId)?.add(client.id);
    client.join(userRoomId);
  }

  @SubscribeMessage('join-room')
  @UseGuards(WsAuthGuard)
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: string | RoomPayload,
  ) {
    const roomId = typeof payload === 'string' ? payload : payload.roomId;
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set());
    }

    this.rooms.get(roomId)?.add(client.id);
    client.join(roomId);
    this.server.to(client.id).emit('room-joined', { roomId });

    // Emit back to confirm room joined
    client.emit('room-joined', { roomId });
    return { event: 'join-room', data: { roomId } };
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: string | RoomPayload,
  ) {
    const roomId = typeof payload === 'string' ? payload : payload.roomId;

    const room = this.rooms.get(roomId);
    if (room) {
      room.delete(client.id);
      if (room.size === 0) {
        this.rooms.delete(roomId);
      }
    }

    client.leave(roomId);

    // Emit back to confirm room left
    client.emit('room-left', { roomId });
    return { event: 'leave-room', data: { roomId } };
  }

  sendStreamChunk(tripId: string, chunk: StreamChunk) {
    this.logger.log(
      `Sending stream chunk to room ${tripId}: ${JSON.stringify(chunk)}`,
    );

    // Check if room exists
    const room = this.rooms.get(tripId);
    if (!room || room.size === 0) {
      this.logger.warn(
        `No clients in room ${tripId}, stream chunk will not be received`,
      );
    } else {
      this.logger.log(`Room ${tripId} has ${room.size} clients`);
    }

    this.server.to(tripId).emit(`stream-chunk-${tripId}`, chunk);
  }

  sendNotification(userId: string, notification: NotificationMessage) {
    const userRoomId = userId;
    this.logger.log(
      `Sending notification to user ${userId}: ${JSON.stringify(notification)}`,
    );

    // Check if user room exists
    const room = this.rooms.get(userRoomId);
    if (!room || room.size === 0) {
      this.logger.warn(
        `No clients in user room ${userRoomId}, notification will not be received`,
      );
    } else {
      this.logger.log(`User room ${userRoomId} has ${room.size} clients`);
    }

    this.server.to(userRoomId).emit('notification', notification);
  }

  sendTripUpdate(tripId: string, update: any) {
    this.logger.log(`Sending trip update to room ${tripId}`);

    // Check if room exists
    const room = this.rooms.get(tripId);
    if (!room || room.size === 0) {
      this.logger.warn(
        `No clients in room ${tripId}, trip update will not be received`,
      );
    } else {
      this.logger.log(`Room ${tripId} has ${room.size} clients`);
    }

    this.server.to(tripId).emit(`trip-update-${tripId}`, update);
  }

  getClient(clientId: string) {
    return this.clients.get(clientId);
  }

  getRoomClients(roomId: string): Set<string> | undefined {
    return this.rooms.get(roomId);
  }
}
