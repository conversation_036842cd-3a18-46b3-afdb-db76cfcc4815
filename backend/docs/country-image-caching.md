# Country Image Caching System

## Overview

The Country Image Caching System is designed to reduce API requests to Unsplash by implementing a database-first approach for country images. This system caches previously fetched images and serves them from the database before making new API calls.

## Features

- **Database-first search**: Always checks the database for cached images before calling the Unsplash API
- **Configurable cache expiry**: Set cache expiration time via environment variables
- **Automatic cleanup**: Scheduled task to remove expired cache entries
- **Management endpoints**: REST API endpoints for cache management and monitoring

## Architecture

### Components

1. **CountryImage Schema** (`country-image.schema.ts`)
   - MongoDB schema for storing cached country images
   - Includes image metadata, source information, and expiry dates

2. **CountryImageCacheService** (`country-image-cache.service.ts`)
   - Core service for cache operations (get, set, delete, cleanup)
   - Scheduled cleanup task runs daily at 2 AM
   - Configurable cache expiry time

3. **Updated UnsplashService** (`unsplash.service.ts`)
   - Modified to check cache first before API calls
   - Automatically caches new images from Unsplash API
   - Maintains backward compatibility

4. **CountryImageCacheController** (`country-image-cache.controller.ts`)
   - REST endpoints for cache management
   - Statistics, cleanup, and individual country operations

## Configuration

### Environment Variables

```bash
# Unsplash Configuration
UNSPLASH_API_KEY=your_unsplash_api_key_here
UNSPLASH_CACHE_EXPIRY_DAYS=30  # Default: 30 days
```

### Cache Expiry

The cache expiry time can be configured via the `UNSPLASH_CACHE_EXPIRY_DAYS` environment variable. Default is 30 days.

## API Endpoints

### Cache Statistics
```
GET /country-image-cache/stats
```
Returns cache statistics including total entries and expired entries.

### Manual Cleanup
```
DELETE /country-image-cache/cleanup
```
Manually trigger cleanup of expired cache entries.

### Country-specific Operations
```
GET /country-image-cache/country/:country
DELETE /country-image-cache/country/:country
```
Get or delete cached image for a specific country.

## Database Schema

```typescript
{
  country: string;           // Normalized country name (unique)
  imageUrl: string;          // Cached image URL
  unsplashImageId?: string;  // Original Unsplash image ID
  altDescription?: string;   // Alt text from Unsplash
  description?: string;      // Description from Unsplash
  photographer?: string;     // Photographer name
  photographerUsername?: string; // Photographer username
  source: string;           // Source (default: 'unsplash')
  lastUpdated: Date;        // Last update timestamp
  expiresAt?: Date;         // Cache expiry date
  createdAt: Date;          // Auto-generated
  updatedAt: Date;          // Auto-generated
}
```

## Usage Flow

1. **Trip Creation**: When a trip is created, `getDestinationImageUrl()` is called
2. **Cache Check**: System first checks database for cached country image
3. **Cache Hit**: If found and not expired, returns cached image URL
4. **Cache Miss**: If not found or expired, calls Unsplash API
5. **Cache Update**: New images from Unsplash are automatically cached
6. **Cleanup**: Expired entries are cleaned up daily via scheduled task

## Benefits

- **Reduced API Calls**: Significantly reduces requests to Unsplash API
- **Improved Performance**: Faster response times for cached images
- **Cost Savings**: Reduces API usage costs and rate limiting issues
- **Reliability**: Fallback to cache when API is unavailable
- **Monitoring**: Built-in statistics and management endpoints

## Monitoring

Use the cache statistics endpoint to monitor:
- Total cached entries
- Number of expired entries
- Cache hit/miss ratios (can be added in future)

## Maintenance

The system includes automatic maintenance via:
- Daily scheduled cleanup of expired entries
- Manual cleanup endpoints for immediate action
- Individual country cache management

## Future Enhancements

- Cache hit/miss ratio tracking
- Image optimization and resizing cache
- Multiple image sizes caching
- Cache warming strategies
- Analytics and usage reporting
