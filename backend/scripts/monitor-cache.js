#!/usr/bin/env node

/**
 * Country Image Cache Monitor
 * 
 * This script helps you monitor the progress of country image caching
 * Run with: node scripts/monitor-cache.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = 'itrip'; // Adjust if your database name is different

async function monitorCache() {
  if (!MONGODB_URI) {
    console.error('❌ MONGODB_URI not found in environment variables');
    process.exit(1);
  }

  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db(DB_NAME);
    const collection = db.collection('countryimages');

    // Basic Statistics
    console.log('\n📊 CACHE OVERVIEW');
    console.log('==================');
    
    const totalCount = await collection.countDocuments();
    const expiredCount = await collection.countDocuments({
      expiresAt: { $lt: new Date() }
    });
    const activeCount = totalCount - expiredCount;

    console.log(`Total Countries Cached: ${totalCount}`);
    console.log(`Active Cache Entries: ${activeCount}`);
    console.log(`Expired Entries: ${expiredCount}`);

    if (totalCount === 0) {
      console.log('\n🚀 Cache is empty - ready to start caching as users generate trips!');
      return;
    }

    // Recently Added Countries
    console.log('\n🆕 RECENTLY CACHED COUNTRIES');
    console.log('==============================');
    
    const recentCountries = await collection
      .find({}, { country: 1, lastUpdated: 1, photographer: 1 })
      .sort({ lastUpdated: -1 })
      .limit(10)
      .toArray();

    recentCountries.forEach((country, index) => {
      const date = country.lastUpdated.toLocaleDateString();
      const time = country.lastUpdated.toLocaleTimeString();
      console.log(`${index + 1}. ${country.country.toUpperCase()} - ${date} ${time}`);
      if (country.photographer) {
        console.log(`   📸 Photo by: ${country.photographer}`);
      }
    });

    // Popular Countries Check
    console.log('\n🌟 POPULAR DESTINATIONS STATUS');
    console.log('===============================');
    
    const popularCountries = [
      'france', 'italy', 'spain', 'japan', 'usa', 'united states',
      'uk', 'united kingdom', 'germany', 'brazil', 'australia',
      'canada', 'mexico', 'thailand', 'greece', 'turkey', 'egypt',
      'india', 'china', 'south korea', 'netherlands', 'portugal'
    ];

    for (const country of popularCountries) {
      const cached = await collection.findOne({ country: country });
      const status = cached ? '✅' : '❌';
      const date = cached ? ` (${cached.lastUpdated.toLocaleDateString()})` : '';
      console.log(`${status} ${country.toUpperCase()}${date}`);
    }

    // Cache Health
    console.log('\n🏥 CACHE HEALTH');
    console.log('================');
    
    const oldestEntry = await collection
      .findOne({}, { country: 1, lastUpdated: 1 })
      .sort({ lastUpdated: 1 });
    
    const newestEntry = await collection
      .findOne({}, { country: 1, lastUpdated: 1 })
      .sort({ lastUpdated: -1 });

    if (oldestEntry && newestEntry) {
      console.log(`Oldest Entry: ${oldestEntry.country.toUpperCase()} (${oldestEntry.lastUpdated.toLocaleDateString()})`);
      console.log(`Newest Entry: ${newestEntry.country.toUpperCase()} (${newestEntry.lastUpdated.toLocaleDateString()})`);
      
      const daysDiff = Math.floor((newestEntry.lastUpdated - oldestEntry.lastUpdated) / (1000 * 60 * 60 * 24));
      console.log(`Cache Age Range: ${daysDiff} days`);
    }

    // Progress Estimation
    console.log('\n📈 PROGRESS ESTIMATION');
    console.log('=======================');
    
    const estimatedTotalCountries = 195; // Approximate number of countries in the world
    const progressPercentage = Math.round((totalCount / estimatedTotalCountries) * 100);
    
    console.log(`Estimated Progress: ${progressPercentage}% (${totalCount}/${estimatedTotalCountries} countries)`);
    
    if (progressPercentage < 25) {
      console.log('🌱 Early stage - Cache is building up');
    } else if (progressPercentage < 50) {
      console.log('🌿 Growing stage - Good progress');
    } else if (progressPercentage < 75) {
      console.log('🌳 Mature stage - Most popular destinations cached');
    } else {
      console.log('🎯 Near complete - Excellent coverage!');
    }

    // API Usage Prediction
    const apiSavingsPercentage = Math.min(95, progressPercentage);
    console.log(`Estimated API Savings: ~${apiSavingsPercentage}% of requests now served from cache`);

  } catch (error) {
    console.error('❌ Error monitoring cache:', error.message);
  } finally {
    await client.close();
    console.log('\n✅ Monitoring complete');
  }
}

// Run the monitor
monitorCache().catch(console.error);
