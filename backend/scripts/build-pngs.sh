#!/bin/bash

# <PERSON>ript to convert SVG files to PNG files using a two-step approach
# This preserves SVG dimensions and quality without scaling issues

# Usage information function
show_usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -i, --input-dir DIR     Input directory containing SVG files (default: current directory)"
    echo "  -o, --output-dir DIR    Output directory for PNG files (default: ./png_output)"
    echo "  -q, --quality VALUE     PNG quality (1-100, default: 100)"
    echo "  -r, --resolution VALUE  Output resolution in DPI (default: 300)"
    echo "  -b, --background COLOR  Background color (default: none)"
    echo "  -h, --help              Show this help message"
    echo
    echo "Example: $0 -i ./svg_files -o ./png_files -r 300"
}

# Default values
INPUT_DIR="."
OUTPUT_DIR="./png_output"
QUALITY=100
RESOLUTION=300
BACKGROUND="none"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        -i|--input-dir)
            INPUT_DIR="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -q|--quality)
            QUALITY="$2"
            shift 2
            ;;
        -r|--resolution)
            RESOLUTION="$2"
            shift 2
            ;;
        -b|--background)
            BACKGROUND="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if required tools are installed
if ! command -v inkscape &> /dev/null; then
    echo "Warning: Inkscape is not installed. Will use ImageMagick as fallback."
    
    if ! command -v magick &> /dev/null && ! command -v convert &> /dev/null; then
        echo "Error: Neither Inkscape nor ImageMagick is installed."
        echo "Please install Inkscape with: brew install inkscape"
        echo "Or ImageMagick with: brew install imagemagick"
        exit 1
    fi
    
    USE_INKSCAPE=false
else
    USE_INKSCAPE=true
    echo "Using Inkscape for SVG conversion (recommended for best quality)"
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Count SVG files
SVG_COUNT=$(find "$INPUT_DIR" -type f -name "*.svg" | wc -l | tr -d ' ')
if [ "$SVG_COUNT" -eq 0 ]; then
    echo "No SVG files found in $INPUT_DIR"
    exit 1
fi

echo "Found $SVG_COUNT SVG files to convert"
echo "Converting SVGs to PNG at $RESOLUTION DPI"

# Process each SVG file
COUNT=0
ERRORS=0

for svg_file in "$INPUT_DIR"/*.svg; do
    if [ -f "$svg_file" ]; then
        filename=$(basename "$svg_file")
        name_no_ext="${filename%.*}"
        output_file="$OUTPUT_DIR/${name_no_ext}.png"
        
        echo -n "Converting $filename to PNG... "
        
        # Use Inkscape for better SVG rendering if available
        if [ "$USE_INKSCAPE" = true ]; then
            # Convert using Inkscape (best quality, preserves dimensions)
            inkscape --export-filename="$output_file" \
                     --export-dpi=$RESOLUTION \
                     --export-background=$BACKGROUND \
                     --export-background-opacity=0 \
                     "$svg_file"
                     
            RESULT=$?
        else
            # Extract viewBox and dimensions from SVG
            VIEWBOX=$(grep -o 'viewBox="[^"]*"' "$svg_file" | head -1 | sed 's/viewBox="\([^"]*\)"/\1/')
            WIDTH=$(grep -o 'width="[^"]*"' "$svg_file" | head -1 | sed 's/width="\([^"]*\)"/\1/')
            HEIGHT=$(grep -o 'height="[^"]*"' "$svg_file" | head -1 | sed 's/height="\([^"]*\)"/\1/')
            
            # Fallback to ImageMagick with improved settings
            if command -v magick &> /dev/null; then
                CONVERT_CMD="magick"
            else
                CONVERT_CMD="convert"
            fi
            
            # If no explicit dimensions found, use viewBox for better accuracy
            if [ -z "$WIDTH" ] || [ -z "$HEIGHT" ]; then
                if [ ! -z "$VIEWBOX" ]; then
                    # Extract viewBox dimensions (format: "x y width height")
                    VB_ARRAY=($VIEWBOX)
                    if [ ${#VB_ARRAY[@]} -ge 4 ]; then
                        WIDTH=${VB_ARRAY[2]}
                        HEIGHT=${VB_ARRAY[3]}
                    fi
                fi
            else
                # Strip units (px, pt, etc.) if present
                WIDTH=$(echo "$WIDTH" | sed 's/[^0-9.]*$//')
                HEIGHT=$(echo "$HEIGHT" | sed 's/[^0-9.]*$//')
            fi
            
            # Calculate density based on resolution and dimensions
            if [ ! -z "$WIDTH" ] && [ ! -z "$HEIGHT" ]; then
                # Two-step conversion approach:
                # 1. Convert to PNG at high resolution
                $CONVERT_CMD \
                    -background "$BACKGROUND" \
                    -density $(($RESOLUTION * 4)) \
                    "$svg_file" \
                    -quality $QUALITY \
                    "${output_file}.temp.png"
                
                # 2. Resize to target dimensions while maintaining quality
                $CONVERT_CMD \
                    "${output_file}.temp.png" \
                    -resize "${WIDTH}x${HEIGHT}" \
                    -quality $QUALITY \
                    "$output_file"
                
                # Remove temporary file
                rm "${output_file}.temp.png"
            else
                # Direct conversion if dimensions can't be determined
                $CONVERT_CMD \
                    -background "$BACKGROUND" \
                    -density $RESOLUTION \
                    "$svg_file" \
                    -quality $QUALITY \
                    "$output_file"
            fi
            
            RESULT=$?
        fi
        
        # Check if conversion was successful
        if [ $RESULT -eq 0 ]; then
            echo "Done!"
            COUNT=$((COUNT+1))
        else
            echo "Failed!"
            ERRORS=$((ERRORS+1))
        fi
    fi
done

echo "Conversion complete!"
echo "Successfully converted: $COUNT"
if [ "$ERRORS" -gt 0 ]; then
    echo "Failed conversions: $ERRORS"
fi
echo "PNG files saved to: $OUTPUT_DIR"