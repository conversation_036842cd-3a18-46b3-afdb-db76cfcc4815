import { parse } from 'csv-parse';
import { config } from 'dotenv';
import { createReadStream } from 'fs';
import { connect } from 'mongoose';
import { join } from 'path';
import { LocationSchema } from '../src/locations/location.schema';

// Load environment variables
config();

async function importLocations() {
    try {
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/itrip';
        const connection = await connect(mongoUri);

        const Location = connection.model('Location', LocationSchema);

        // Create a compound unique index for name and country
        await Location.collection.createIndex({ name: 1, country: 1 }, { unique: true });

        // Clear existing data
        await Location.deleteMany({});

        const csvPath = join(process.cwd(), '.data', 'worldcitiespop.csv');
        let processedCount = 0;
        let errorCount = 0;
        let batch: any[] = [];
        const BATCH_SIZE = 500;

        const parser = parse({
            columns: true,
            skip_empty_lines: true,
            bom: true,
            cast: true,
            skip_records_with_error: true
        });

        const stream = createReadStream(csvPath, {
            encoding: 'utf-8',
            highWaterMark: 64 * 1024 // 64KB chunks
        });

        const processBatch = async (documents: any[]) => {
            if (documents.length === 0) return;

            try {
                await Location.insertMany(documents, {
                    ordered: false,
                    rawResult: true
                }).catch(error => {
                    // Handle bulk write errors, but continue processing
                    if (error.writeErrors) {
                        errorCount += error.writeErrors.length;
                        processedCount += error.insertedCount;
                    }
                });

                console.log(`Processed ${processedCount} locations (Errors: ${errorCount})`);
            } catch (error) {
                console.error('Batch processing error:', error.message);
                errorCount++;
            }
        };

        return new Promise((resolve, reject) => {
            stream.pipe(parser)
                .on('data', (data: any) => {
                    if (!data.AccentCity || !data.Country) return;

                    const location = {
                        name: data.AccentCity,
                        country: data.Country,
                        type: 'city',
                        coordinates: {
                            lat: parseFloat(data.Latitude),
                            lng: parseFloat(data.Longitude),
                        },
                        population: data.Population ? parseInt(data.Population) : undefined,
                    };

                    batch.push(location);

                    // When batch is full, pause stream and process batch
                    if (batch.length >= BATCH_SIZE) {
                        parser.pause(); // Pause parsing while we process the batch

                        processBatch(batch)
                            .then(() => {
                                batch = []; // Clear the batch
                                parser.resume(); // Resume parsing
                            })
                            .catch(error => {
                                console.error('Error processing batch:', error);
                                parser.resume(); // Resume parsing even on error
                            });
                    }
                })
                .on('end', async () => {
                    // Process any remaining documents
                    if (batch.length > 0) {
                        await processBatch(batch);
                    }

                    console.log('Import completed');
                    console.log(`Total locations processed: ${processedCount}`);
                    console.log(`Total errors: ${errorCount}`);

                    // Create other indexes
                    console.log('Creating remaining indexes...');
                    await Location.createIndexes();
                    console.log('All indexes created');

                    resolve(true);
                })
                .on('error', (error) => {
                    console.error('Error during import:', error);
                    reject(error);
                });
        });

    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

// Run the import
importLocations()
    .then(() => process.exit(0))
    .catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });

// Increase Node.js memory limit to 10GB
if (process.env.NODE_OPTIONS?.includes('--max-old-space-size=')) {
    console.warn('NODE_OPTIONS already contains max-old-space-size setting');
} else {
    process.env.NODE_OPTIONS = `${process.env.NODE_OPTIONS || ''} --max-old-space-size=10240`;
} 