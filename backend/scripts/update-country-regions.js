/**
 * <PERSON><PERSON><PERSON> to update country-regions.json with accurate data from Google Maps API
 *
 * This script:
 * 1. Reads the existing country-regions.json file
 * 2. For each country, uses the Google Maps Geocoding API to get its boundaries and center coordinates
 * 3. Calculates the latitudeDelta and longitudeDelta values
 * 4. Updates the country-regions.json file with the accurate data including lat and lng
 *
 * Usage:
 * - Set GOOGLE_MAPS_API_KEY in .env file or as environment variable
 * - Run: node scripts/update-country-regions.js
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const { getCountryDataList } = require('countries-list')

const listCountries = getCountryDataList();

// Configuration
const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const COUNTRY_REGIONS_PATH = path.join(__dirname, '..', 'src', 'locations', 'data', 'country-regions.json');
const DELAY_BETWEEN_REQUESTS_MS = 1000; // Delay to avoid hitting API rate limits

// Validate API key
if (!GOOGLE_MAPS_API_KEY) {
  console.error('Error: GOOGLE_MAPS_API_KEY is not set. Please set it in .env file or as environment variable.');
  process.exit(1);
}

/**
 * Get country boundaries from Google Maps API
 * @param {string} countryName - The name of the country
 * @param {string} countryCode - The ISO 3166-1 alpha-2 country code
 * @returns {Promise<Object>} - The country boundaries
 */
async function getCountryBoundaries(countryName, countryCode) {
  try {
    // Use the Geocoding API to get country boundaries
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(countryName)}&components=country:${countryCode}&key=${GOOGLE_MAPS_API_KEY}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.status !== 'OK' || !data.results || data.results.length === 0) {
      console.error(`No results found for country: ${countryName} (${countryCode})`);
      return null;
    }

    const result = data.results[0];

    // Get the viewport boundaries
    if (result.geometry && result.geometry.viewport) {
      const { northeast, southwest } = result.geometry.viewport;

      // Calculate the center coordinates
      const centerLat = (northeast.lat + southwest.lat) / 2;
      const centerLng = (northeast.lng + southwest.lng) / 2;

      // Calculate the deltas
      const latitudeDelta = Math.abs(northeast.lat - southwest.lat);
      const longitudeDelta = Math.abs(northeast.lng - southwest.lng);

      return {
        name: countryName,
        code: countryCode,
        lat: centerLat,
        lng: centerLng,
        region: {
          latitudeDelta,
          longitudeDelta
        },
        // Additional data for debugging
        bounds: {
          northeast,
          southwest,
          center: { lat: centerLat, lng: centerLng }
        }
      };
    }

    console.error(`No viewport found for country: ${countryName} (${countryCode})`);
    return null;
  } catch (error) {
    console.error(`Error getting boundaries for ${countryName} (${countryCode}):`, error.message);
    return null;
  }
}

/**
 * Sleep for a specified number of milliseconds
 * @param {number} ms - The number of milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Main function to update country regions
 */
async function updateCountryRegions() {
  try {
    // Read the existing country-regions.json file
    const fileContent = await readFile(COUNTRY_REGIONS_PATH, 'utf8');
    const countryRegions = JSON.parse(fileContent);

    // Create a new array to store updated regions
    const updatedRegions = [];

    // Process each country
    for (const country of listCountries) {
      console.log(`Processing country: ${country.name} (${country.iso2})...`);

      // Get country boundaries from Google Maps API
      const updatedCountry = await getCountryBoundaries(country.name, country.iso2);

      if (updatedCountry) {
        // Add the updated country to the array with lat and lng
        updatedRegions.push({
          name: country.name,
          code: country.iso2,
          lat: updatedCountry.lat,
          lng: updatedCountry.lng,
          region: updatedCountry.region
        });

        console.log(`Updated ${country.name}: lat=${updatedCountry.lat.toFixed(6)}, lng=${updatedCountry.lng.toFixed(6)}, latitudeDelta=${updatedCountry.region.latitudeDelta.toFixed(2)}, longitudeDelta=${updatedCountry.region.longitudeDelta.toFixed(2)}`);
      } else {
        // If we couldn't get updated data, keep the existing data
        updatedRegions.push(country);
        console.log(`Keeping existing data for ${country.name}`);
      }

      // Add a delay to avoid hitting API rate limits
      await sleep(DELAY_BETWEEN_REQUESTS_MS);
    }

    // Update the country-regions.json file
    const updatedContent = JSON.stringify({ regions: updatedRegions }, null, 2);
    await writeFile(COUNTRY_REGIONS_PATH, updatedContent);

    console.log('Country regions updated successfully!');
  } catch (error) {
    console.error('Error updating country regions:', error);
    process.exit(1);
  }
}

// Run the script
updateCountryRegions();
