/**
 * <PERSON>ript to update country-regions.json with accurate data from OpenRouteService API
 *
 * This script:
 * 1. Reads the existing country-regions.json file
 * 2. For each country, uses the OpenRouteService API to get its boundaries and center coordinates
 * 3. Calculates the latitudeDelta and longitudeDelta values
 * 4. Updates the country-regions.json file with the accurate data including lat and lng
 *
 * Usage:
 * - Set ORS_API_KEY in .env file or as environment variable
 * - Run: node scripts/update-with-ors.js
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const { getCountryDataList } = require('countries-list');
const listCountries = getCountryDataList();

// Configuration
const ORS_API_KEY = process.env.ORS_API_KEY;
const ORS_API_URL = 'https://api.openrouteservice.org';
const COUNTRY_REGIONS_PATH = path.join(__dirname, '..', 'src', 'locations', 'data', 'country-regions.json');
const DELAY_BETWEEN_REQUESTS_MS = 1000; // Delay to avoid hitting API rate limits

// Note: This script uses the ORS_API_KEY directly from the .env file
// In the application, this is managed through the ConfigService

// Get country codes from command line arguments
const countryCodeArgs = process.argv.slice(2);
const updateSpecific = countryCodeArgs.length > 0;

// Validate API key
if (!ORS_API_KEY) {
  console.error('Error: ORS_API_KEY is not set. Please set it in .env file or as environment variable.');
  process.exit(1);
}

/**
 * Get country boundaries from OpenRouteService API
 * @param {string} countryName - The name of the country
 * @param {string} countryCode - The ISO 3166-1 alpha-2 country code
 * @returns {Promise<Object>} - The country boundaries
 */
async function getCountryBoundaries(countryName, countryCode) {
  try {
    // Use the ORS Geocoding API to get country boundaries
    const headers = new Headers();
    headers.set('Authorization', ORS_API_KEY);
    headers.set('Accept', 'application/json');

    const url = `${ORS_API_URL}/geocode/search?text=${encodeURIComponent(countryName)}&layers=country&size=1`;

    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Geocoding failed: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.features || data.features.length === 0) {
      console.error(`No results found for country: ${countryName} (${countryCode})`);
      return null;
    }

    const feature = data.features[0];

    // Get the bounding box
    if (feature.bbox) {
      const [minLng, minLat, maxLng, maxLat] = feature.bbox;

      // Calculate the center coordinates
      const centerLat = (minLat + maxLat) / 2;
      const centerLng = (minLng + maxLng) / 2;

      // Calculate the deltas
      const latitudeDelta = Math.abs(maxLat - minLat);
      const longitudeDelta = Math.abs(maxLng - minLng);

      return {
        name: countryName,
        code: countryCode,
        lat: centerLat,
        lng: centerLng,
        region: {
          latitudeDelta,
          longitudeDelta
        },
        // Additional data for debugging
        bounds: {
          minLat,
          maxLat,
          minLng,
          maxLng,
          center: { lat: centerLat, lng: centerLng }
        }
      };
    }

    console.error(`No bounding box found for country: ${countryName} (${countryCode})`);
    return null;
  } catch (error) {
    console.error(`Error getting boundaries for ${countryName} (${countryCode}):`, error.message);
    return null;
  }
}

/**
 * Sleep for a specified number of milliseconds
 * @param {number} ms - The number of milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Main function to update country regions
 */
async function updateCountryRegions() {
  try {
    // Read the existing country-regions.json file
    const fileContent = await readFile(COUNTRY_REGIONS_PATH, 'utf8');
    const countryRegions = JSON.parse(fileContent);

    // Create a map of country codes to update if specific countries are provided
    const countryCodeSet = updateSpecific
      ? new Set(countryCodeArgs.map(code => code.toUpperCase()))
      : null;

    // Process each country
    for (let i = 0; i < listCountries.length; i++) {
      const country = listCountries[i];

      // Skip countries that are not in the list to update if specific countries are provided
      if (updateSpecific && !countryCodeSet.has(country.iso2)) {
        continue;
      }

      console.log(`Processing country: ${country.name} (${country.iso2})...`);

      // Get country boundaries from ORS API
      const updatedCountry = await getCountryBoundaries(country.name, country.iso2);

      if (updatedCountry) {
        // Update the country in the original array with lat and lng
        countryRegions.regions[i] = {
          name: country.name,
          code: country.iso2,
          lat: updatedCountry.lat,
          lng: updatedCountry.lng,
          region: updatedCountry.region
        };

        console.log(`Updated ${country.name}: lat=${updatedCountry.lat.toFixed(6)}, lng=${updatedCountry.lng.toFixed(6)}, latitudeDelta=${updatedCountry.region.latitudeDelta.toFixed(2)}, longitudeDelta=${updatedCountry.region.longitudeDelta.toFixed(2)}`);
      } else {
        console.log(`Keeping existing data for ${country.name}`);
      }

      // Add a delay to avoid hitting API rate limits
      await sleep(DELAY_BETWEEN_REQUESTS_MS);
    }

    // Update the country-regions.json file
    const updatedContent = JSON.stringify(countryRegions, null, 2);
    await writeFile(COUNTRY_REGIONS_PATH, updatedContent);

    console.log('Country regions updated successfully!');
  } catch (error) {
    console.error('Error updating country regions:', error);
    process.exit(1);
  }
}

// Run the script
updateCountryRegions();
