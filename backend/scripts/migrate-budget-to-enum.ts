import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface TripDocument {
  _id: any;
  tripDetails: {
    budget: number | string;
    [key: string]: any;
  };
  [key: string]: any;
}



/**
 * Migration script to convert budget from number to enum values
 * Budget ranges:
 * - budget: < 1000
 * - normal: 1000 - 5000
 * - luxury: > 5000
 */
async function migrateBudgetToEnum() {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/itrip';

  console.log('Connecting to MongoDB...');
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const tripsCollection = db.collection('trips');

    // Find all trips with numeric budget values
    const trips = await tripsCollection.find({
      'tripDetails.budget': { $type: 'number' }
    }).toArray() as TripDocument[];

    console.log(`Found ${trips.length} trips with numeric budget values`);

    let migratedCount = 0;

    for (const trip of trips) {
      const currentBudget = trip.tripDetails.budget as number;
      let newBudgetCategory: string;

      // Convert numeric budget to category
      if (currentBudget < 1000) {
        newBudgetCategory = 'budget';
      } else if (currentBudget <= 5000) {
        newBudgetCategory = 'normal';
      } else {
        newBudgetCategory = 'luxury';
      }

      // Update the document
      await tripsCollection.updateOne(
        { _id: trip._id },
        {
          $set: {
            'tripDetails.budget': newBudgetCategory
          }
        }
      );

      console.log(`Migrated trip ${trip._id}: ${currentBudget} -> ${newBudgetCategory}`);
      migratedCount++;
    }

    console.log(`Migration completed. ${migratedCount} trips updated.`);

    // Verify migration
    const remainingNumericBudgets = await tripsCollection.countDocuments({
      'tripDetails.budget': { $type: 'number' }
    });

    if (remainingNumericBudgets === 0) {
      console.log('✅ Migration successful! All budget values converted to enum.');
    } else {
      console.log(`⚠️  Warning: ${remainingNumericBudgets} trips still have numeric budget values.`);
    }

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateBudgetToEnum()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateBudgetToEnum };
