# Node Environment
NODE_ENV=development  # development, production, or test
PORT=3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=1d

# MongoDB Configuration
MONGODB_URI=***************************************************************

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_KEY_BACKUP=your_backup_openai_api_key_here
OPENAI_ASSISTANT_ID=your_openai_assistant_id_here
USE_ASSISTANTS_API=false

# Groq Configuration
GROQ_API_KEY=your_groq_api_key_here
GROQ_API_KEY_BACKUP=your_backup_groq_api_key_here

# DeepSeek Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_KEY_BACKUP=your_backup_deepseek_api_key_here

# Generation Configuration
GENERATION_MODE=multi_round  # multi_round or one_shot
MODEL_TO_USE=groq  # openai, groq, or deepseek
MAX_DAYS_TO_GENERATE=2

# Streaming Configuration
USE_STREAMING=false  # Controls AI streaming only

# WebSocket Configuration
USE_WEBSOCKET=true   # Controls socket notifications to clients
USE_SOCKETS=true     # Legacy - will be removed

# Google Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id_here
GOOGLE_ANDROID_CLIENT_ID=your_google_android_client_id_here
GOOGLE_WEB_CLIENT_ID=your_google_web_client_id_here

# OpenRouteService Configuration
ORS_API_KEY=your_openrouteservice_api_key_here

# OpenCage Data Configuration
OPENCAGE_API_KEY=your_opencage_api_key_here

# Unsplash Configuration
UNSPLASH_API_KEY='your_unsplash_api_key_here'
UNSPLASH_CACHE_EXPIRY_DAYS=30

# Token Balance Configuration
INITIAL_TOKEN_BALANCE=5000
ESTIMATED_SINGLE_DAY_RESPONSE=500

# Payment Configuration
APPLE_APP_SHARED_SECRET=your_apple_app_shared_secret_here
GOOGLE_PLAY_SERVICE_ACCOUNT={"type":"service_account","project_id":"your-project-id","private_key_id":"your-key-id","private_key":"your-private-key","client_email":"*****************","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your-cert-url"}
ALLOW_MOCK_PURCHASES=true

# Trip Recovery Configuration in minutes (default 20)
TRIP_STUCK_THRESHOLD=20

# Location Validation Configuration
LOCATION_PROXIMITY_RADIUS_KM=100

# Resend Configuration
RESEND_API_KEY=your_resend_api_key_here