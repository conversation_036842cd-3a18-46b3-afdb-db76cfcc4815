#!/bin/bash

# This script is designed to be run after a git pull to ensure Swift compatibility fixes are applied
# It can be set up as a git post-merge hook or run manually

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Check if node_modules exists and contains the files we need to patch
if [ -d "$PROJECT_ROOT/node_modules/expo-modules-core" ]; then
  echo "Checking if Swift compatibility fixes need to be applied..."
  
  # Check if any of the files we need to patch have been modified by git
  ANYCHILDSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift"
  AUTOSIZINGSTACKSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift"
  COREMODULESWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift"
  SWIFTUIHOSTINGVIEWSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift"
  
  # Check if any of the files contain the patterns we need to fix
  if grep -q "extension ExpoSwiftUI {" "$ANYCHILDSWIFT" && grep -q "public protocol AnyChild:" "$ANYCHILDSWIFT"; then
    echo "Swift compatibility issues detected. Applying fixes..."
    "$SCRIPT_DIR/apply-swift-fixes.sh"
  elif grep -q "onGeometryChange" "$AUTOSIZINGSTACKSWIFT"; then
    echo "Swift compatibility issues detected. Applying fixes..."
    "$SCRIPT_DIR/apply-swift-fixes.sh"
  elif grep -q "Constant(\"expoModulesCoreVersion\") {" "$COREMODULESWIFT"; then
    echo "Swift compatibility issues detected. Applying fixes..."
    "$SCRIPT_DIR/apply-swift-fixes.sh"
  elif grep -q "public protocol WithHostingView" "$SWIFTUIHOSTINGVIEWSWIFT"; then
    echo "Swift compatibility issues detected. Applying fixes..."
    "$SCRIPT_DIR/apply-swift-fixes.sh"
  else
    echo "No Swift compatibility issues detected or fixes already applied."
  fi
else
  echo "node_modules directory not found or Expo modules not installed."
  echo "Run 'npm install' first, then run this script again."
fi
