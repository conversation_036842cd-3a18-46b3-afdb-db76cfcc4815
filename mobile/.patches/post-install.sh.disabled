#!/bin/bash

# This script is designed to be run after npm install to ensure Swift compatibility fixes are applied

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Check if node_modules exists and contains the files we need to patch
if [ -d "$PROJECT_ROOT/node_modules/expo-modules-core" ]; then
  echo "Checking if Swift compatibility fixes need to be applied..."

  # Check if any of the files we need to patch have been modified by npm install
  ANYCHILDSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift"
  AUTOSIZINGSTACKSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift"
  COREMODULESWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift"
  SWIFTUIHOSTINGVIEWSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift"

  # Check if any of the files contain the patterns we need to fix
  NEEDS_FIXING=false

  if [ -f "$ANYCHILDSWIFT" ] && grep -q "extension ExpoSwiftUI {" "$ANYCHILDSWIFT" && grep -q "public protocol AnyChild:" "$ANYCHILDSWIFT"; then
    NEEDS_FIXING=true
  elif [ -f "$AUTOSIZINGSTACKSWIFT" ] && grep -q "onGeometryChange" "$AUTOSIZINGSTACKSWIFT"; then
    NEEDS_FIXING=true
  elif [ -f "$COREMODULESWIFT" ] && grep -q "Constant(\"expoModulesCoreVersion\") {" "$COREMODULESWIFT"; then
    NEEDS_FIXING=true
  elif [ -f "$SWIFTUIHOSTINGVIEWSWIFT" ] && grep -q "public protocol WithHostingView" "$SWIFTUIHOSTINGVIEWSWIFT"; then
    NEEDS_FIXING=true
  fi

  # Check notification module files
  CATEGORIESMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift"
  PRESENTATIONMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift"
  SCHEDULERMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift"

  if [ -f "$CATEGORIESMODULESWIFT" ] && grep -q 'AsyncFunction("getNotificationCategoriesAsync") {' "$CATEGORIESMODULESWIFT"; then
    NEEDS_FIXING=true
  elif [ -f "$PRESENTATIONMODULESWIFT" ] && grep -q 'AsyncFunction("getPresentedNotificationsAsync") {' "$PRESENTATIONMODULESWIFT"; then
    NEEDS_FIXING=true
  elif [ -f "$SCHEDULERMODULESWIFT" ] && grep -q 'AsyncFunction("getAllScheduledNotificationsAsync") {' "$SCHEDULERMODULESWIFT"; then
    NEEDS_FIXING=true
  fi

  if [ "$NEEDS_FIXING" = true ]; then
    echo "Swift compatibility issues detected. Applying fixes..."
    "$SCRIPT_DIR/apply-swift-fixes.sh"
  else
    echo "No Swift compatibility issues detected or fixes already applied."
  fi
else
  echo "node_modules directory not found or Expo modules not installed."
  echo "Run 'npm install' first, then run this script again."
fi
