#!/bin/bash

# <PERSON>ript to check if Swift compatibility fixes are needed
# This script only checks and reports, it does not apply any fixes

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

echo "🔍 Checking for Swift compatibility issues..."

# Check if node_modules exists and contains the files we need to patch
if [ ! -d "$PROJECT_ROOT/node_modules/expo-modules-core" ]; then
  echo "❌ node_modules directory not found or Expo modules not installed."
  echo "   Run 'npm install' first."
  exit 1
fi

# Define file paths
ANYCHILDSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift"
AUTOSIZINGSTACKSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift"
COREMODULESWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift"
SWIFTUIHOSTINGVIEWSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift"
CATEGORIESMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift"
PRESENTATIONMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift"
SCHEDULERMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift"

ISSUES_FOUND=false

# Check each file for issues
if [ -f "$ANYCHILDSWIFT" ] && grep -q "extension ExpoSwiftUI {" "$ANYCHILDSWIFT" && grep -q "public protocol AnyChild:" "$ANYCHILDSWIFT"; then
  echo "⚠️  AnyChild.swift needs fixing (protocol nesting issue)"
  ISSUES_FOUND=true
fi

if [ -f "$AUTOSIZINGSTACKSWIFT" ] && grep -q "onGeometryChange" "$AUTOSIZINGSTACKSWIFT"; then
  echo "⚠️  AutoSizingStack.swift needs fixing (onGeometryChange issue)"
  ISSUES_FOUND=true
fi

# Check CoreModule.swift - needs fixing if it has duplicate return types OR missing return type
if [ -f "$COREMODULESWIFT" ]; then
  if grep -q 'Constant("expoModulesCoreVersion") { () -> \[String: Any\] in () -> \[String: Any\] in' "$COREMODULESWIFT"; then
    echo "⚠️  CoreModule.swift needs fixing (duplicate return type)"
    ISSUES_FOUND=true
  elif grep -q 'Constant("expoModulesCoreVersion") {[^}]*$' "$COREMODULESWIFT" && ! grep -q 'Constant("expoModulesCoreVersion") { () -> \[String: Any\] in' "$COREMODULESWIFT"; then
    echo "⚠️  CoreModule.swift needs fixing (missing return type)"
    ISSUES_FOUND=true
  fi
fi

if [ -f "$SWIFTUIHOSTINGVIEWSWIFT" ] && grep -q "public protocol WithHostingView" "$SWIFTUIHOSTINGVIEWSWIFT"; then
  echo "⚠️  SwiftUIHostingView.swift needs fixing (protocol nesting issue)"
  ISSUES_FOUND=true
fi

# Check notification modules - need fixing if they don't have explicit return types
if [ -f "$CATEGORIESMODULESWIFT" ] && grep -q 'AsyncFunction("getNotificationCategoriesAsync") {' "$CATEGORIESMODULESWIFT" && ! grep -q 'AsyncFunction("getNotificationCategoriesAsync") { () -> \[CategoryRecord\] in' "$CATEGORIESMODULESWIFT"; then
  echo "⚠️  CategoriesModule.swift needs fixing (closure type inference issue)"
  ISSUES_FOUND=true
fi

if [ -f "$PRESENTATIONMODULESWIFT" ] && grep -q 'AsyncFunction("getPresentedNotificationsAsync") {' "$PRESENTATIONMODULESWIFT" && ! grep -q 'AsyncFunction("getPresentedNotificationsAsync") { () -> \[\[String: Any\]\] in' "$PRESENTATIONMODULESWIFT"; then
  echo "⚠️  PresentationModule.swift needs fixing (closure type inference issue)"
  ISSUES_FOUND=true
fi

if [ -f "$SCHEDULERMODULESWIFT" ] && grep -q 'AsyncFunction("getAllScheduledNotificationsAsync") {' "$SCHEDULERMODULESWIFT" && ! grep -q 'AsyncFunction("getAllScheduledNotificationsAsync") { () -> \[\[String: Any\]\] in' "$SCHEDULERMODULESWIFT"; then
  echo "⚠️  SchedulerModule.swift needs fixing (closure type inference issue)"
  ISSUES_FOUND=true
fi

if [ "$ISSUES_FOUND" = true ]; then
  echo ""
  echo "🚨 Swift compatibility issues detected!"
  echo "   Run 'npm run fix-swift' to apply the fixes."
  exit 1
else
  echo "✅ No Swift compatibility issues detected."
  echo "   All files are either already fixed or don't need patching."
  exit 0
fi
