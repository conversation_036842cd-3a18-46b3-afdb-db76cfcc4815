# Swift Compatibility Patches

This directory contains patches and scripts to fix Swift compatibility issues in Expo modules. These patches are designed to survive git pulls and npm installs.

## How It Works

1. The `.patches` directory is preserved in git (not ignored)
2. The patches are applied manually when needed using the `npm run fix-swift` command
3. The script checks if the Swift files need patching and applies the fixes if needed

## Issues Fixed

1. **Protocol Nesting Issues**: Swift doesn't allow protocols to be nested inside other declarations.

   - Fixed in `AnyChild.swift` and `SwiftUIHostingView.swift`

2. **Closure Type Inference**: Swift requires explicit return types for closures with multiple statements.

   - Fixed in `CoreModule.swift` and notification module files

3. **Missing Methods**: Replaced `onGeometryChange` with standard `GeometryReader` and `onAppear`.
   - Fixed in `AutoSizingStack.swift`

## Manual Application

To check if patches are needed:

```bash
# From the mobile directory
npm run check-swift
```

To apply the patches when needed:

```bash
# From the mobile directory
npm run fix-swift
```

Or run the scripts directly:

```bash
# From the mobile directory
./.patches/check-swift-issues.sh  # Check only
./.patches/apply-swift-fixes.sh   # Apply fixes
```

## When to Apply

These fixes are needed when you encounter Swift compilation errors like:

- `protocol 'AnyChild' cannot be nested inside other declaration`
- `cannot infer return type for closure with multiple statements; add explicit type to disambiguate`
- `value of type 'some View' has no member 'onGeometryChange'`

These errors typically occur after:

- Running `npm install`
- Updating Expo packages
- Pulling changes from git
- Running `npm run prebuild:ios` or `npm run prebuild:dev`

**Note**: The patches are no longer applied automatically. Run `npm run fix-swift` manually when you encounter these Swift compilation errors.

## Updating the Patches

If you need to update these patches:

1. Make your changes to the Swift files
2. Create a new patch file:
   ```bash
   cd mobile
   diff -u node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift.orig node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift > .patches/new-patch.txt
   ```
3. Update the `swift-compatibility-fixes.patch` file with your new changes
