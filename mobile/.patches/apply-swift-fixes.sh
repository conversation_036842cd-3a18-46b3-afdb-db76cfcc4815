#!/bin/bash

# <PERSON>ript to apply Swift compatibility fixes to Expo modules
# This fixes issues with Swift protocol nesting and closure type inference
#
# This script is run MANUALLY when Swift compilation errors occur.
# It is no longer triggered automatically after npm install.

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

echo "🔧 Manually applying Swift compatibility fixes to Expo modules..."

# Apply the patches directly to each file instead of using the patch command
# This is more reliable and doesn't prompt for input

# 1. Fix AnyChild.swift
ANYCHILDSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift"
if [ -f "$ANYCHILDSWIFT" ]; then
  if grep -q "extension ExpoSwiftUI {" "$ANYCHILDSWIFT" && grep -q "public protocol AnyChild:" "$ANYCHILDSWIFT"; then
    echo "Fixing AnyChild.swift..."
    # Create a backup
    cp "$ANYCHILDSWIFT" "$ANYCHILDSWIFT.bak"
    # Apply the fix
    sed -i.tmp '
      /extension ExpoSwiftUI {/,/public protocol AnyChild: SwiftUI.View {/ {
        /extension ExpoSwiftUI {/c\
// Define the protocol outside the extension\
public protocol ExpoSwiftUIAnyChild: SwiftUI.View {\
  associatedtype ChildViewType: SwiftUI.View\
  var childView: ChildViewType { get }\
  var id: ObjectIdentifier { get }\
}\
\
// Then extend it in the ExpoSwiftUI namespace\
extension ExpoSwiftUI {\
  public typealias AnyChild = ExpoSwiftUIAnyChild
      }
      /public protocol AnyChild: SwiftUI.View {/,/var id: ObjectIdentifier { get }/ d
    }' "$ANYCHILDSWIFT"
    rm -f "$ANYCHILDSWIFT.tmp"
  else
    echo "AnyChild.swift already fixed or has a different structure."
  fi
else
  echo "AnyChild.swift not found."
fi

# 2. Fix AutoSizingStack.swift
AUTOSIZINGSTACKSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift"
if [ -f "$AUTOSIZINGSTACKSWIFT" ]; then
  if grep -q "onGeometryChange" "$AUTOSIZINGSTACKSWIFT"; then
    echo "Fixing AutoSizingStack.swift..."
    # Create a backup
    cp "$AUTOSIZINGSTACKSWIFT" "$AUTOSIZINGSTACKSWIFT.bak"
    # Apply the fix
    sed -i.tmp '
      /\.onGeometryChange/,/})/ {
        s/\.onGeometryChange.*{/\.background(\
                GeometryReader { proxy in\
                  Color.clear.onAppear {/
        s/var size = size/var size = proxy.size/
        s/proxy\.setViewSize/self.proxy.setViewSize/
        s/})$/}\
                }\
              )/
      }
    ' "$AUTOSIZINGSTACKSWIFT"
    rm -f "$AUTOSIZINGSTACKSWIFT.tmp"
  else
    echo "AutoSizingStack.swift already fixed or has a different structure."
  fi
else
  echo "AutoSizingStack.swift not found."
fi

# 3. Fix CoreModule.swift
COREMODULESWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift"
if [ -f "$COREMODULESWIFT" ]; then
  # Check if it needs fixing (has the problematic pattern)
  if grep -q 'Constant("expoModulesCoreVersion") { () -> \[String: Any\] in () -> \[String: Any\] in' "$COREMODULESWIFT"; then
    echo "Fixing CoreModule.swift (removing duplicate return type)..."
    # Create a backup
    cp "$COREMODULESWIFT" "$COREMODULESWIFT.bak"
    # Fix duplicated return type
    sed -i.tmp 's/Constant("expoModulesCoreVersion") { () -> \[String: Any\] in () -> \[String: Any\] in.*/Constant("expoModulesCoreVersion") { () -> [String: Any] in/' "$COREMODULESWIFT"
    rm -f "$COREMODULESWIFT.tmp"
  elif grep -q 'Constant("expoModulesCoreVersion") {[^}]*$' "$COREMODULESWIFT" && ! grep -q 'Constant("expoModulesCoreVersion") { () -> \[String: Any\] in' "$COREMODULESWIFT"; then
    echo "Fixing CoreModule.swift (adding return type)..."
    # Create a backup
    cp "$COREMODULESWIFT" "$COREMODULESWIFT.bak"
    # Normal fix - add return type
    sed -i.tmp 's/Constant("expoModulesCoreVersion") {/Constant("expoModulesCoreVersion") { () -> [String: Any] in/' "$COREMODULESWIFT"
    rm -f "$COREMODULESWIFT.tmp"
  else
    echo "CoreModule.swift already fixed or has a different structure."
  fi
else
  echo "CoreModule.swift not found."
fi

# 4. Fix SwiftUIHostingView.swift
SWIFTUIHOSTINGVIEWSWIFT="$PROJECT_ROOT/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift"
if [ -f "$SWIFTUIHOSTINGVIEWSWIFT" ]; then
  if grep -q "public protocol WithHostingView" "$SWIFTUIHOSTINGVIEWSWIFT"; then
    echo "Fixing SwiftUIHostingView.swift..."
    # Create a backup
    cp "$SWIFTUIHOSTINGVIEWSWIFT" "$SWIFTUIHOSTINGVIEWSWIFT.bak"

    # First, add the protocol definition outside the extension
    sed -i.tmp '/internal protocol AnyExpoSwiftUIHostingView/a\
\
/**\
 For a SwiftUI view to self-contain a HostingView, it can conform to the WithHostingView protocol.\
 */\
public protocol ExpoSwiftUIWithHostingView {\
}' "$SWIFTUIHOSTINGVIEWSWIFT"

    # Then, replace the protocol inside the extension with a typealias
    sed -i.tmp2 '/extension ExpoSwiftUI {/,/public protocol WithHostingView {/ {
      /public protocol WithHostingView {/,/}/ {
        /public protocol WithHostingView {/c\
  public typealias WithHostingView = ExpoSwiftUIWithHostingView
        /}/d
      }
      /\/\*\*/,/\*\// {
        /\/\*\*/,/For a SwiftUI view to self-contain a HostingView/d
        /\*\//d
      }
    }' "$SWIFTUIHOSTINGVIEWSWIFT"

    rm -f "$SWIFTUIHOSTINGVIEWSWIFT.tmp" "$SWIFTUIHOSTINGVIEWSWIFT.tmp2"
  else
    echo "SwiftUIHostingView.swift already fixed or has a different structure."
  fi
else
  echo "SwiftUIHostingView.swift not found."
fi

# 5. Fix notification module files
# CategoriesModule.swift
CATEGORIESMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Categories/CategoriesModule.swift"
if [ -f "$CATEGORIESMODULESWIFT" ]; then
  if grep -q 'AsyncFunction("getNotificationCategoriesAsync") {' "$CATEGORIESMODULESWIFT"; then
    echo "Fixing CategoriesModule.swift..."
    # Create a backup
    cp "$CATEGORIESMODULESWIFT" "$CATEGORIESMODULESWIFT.bak"
    # Apply the fix - make sure we don't duplicate the return type
    if grep -q 'AsyncFunction("getNotificationCategoriesAsync") { () -> \[CategoryRecord\] in () -> \[CategoryRecord\] in' "$CATEGORIESMODULESWIFT"; then
      # Fix duplicated return type
      sed -i.tmp 's/AsyncFunction("getNotificationCategoriesAsync") { () -> \[CategoryRecord\] in () -> \[CategoryRecord\] in.*/AsyncFunction("getNotificationCategoriesAsync") { () -> [CategoryRecord] in/' "$CATEGORIESMODULESWIFT"
    else
      # Normal fix
      sed -i.tmp 's/AsyncFunction("getNotificationCategoriesAsync") {/AsyncFunction("getNotificationCategoriesAsync") { () -> [CategoryRecord] in/' "$CATEGORIESMODULESWIFT"
    fi
    rm -f "$CATEGORIESMODULESWIFT.tmp"
  else
    echo "CategoriesModule.swift already fixed or has a different structure."
  fi
else
  echo "CategoriesModule.swift not found."
fi

# PresentationModule.swift
PRESENTATIONMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Presenting/PresentationModule.swift"
if [ -f "$PRESENTATIONMODULESWIFT" ]; then
  if grep -q 'AsyncFunction("getPresentedNotificationsAsync") {' "$PRESENTATIONMODULESWIFT"; then
    echo "Fixing PresentationModule.swift..."
    # Create a backup
    cp "$PRESENTATIONMODULESWIFT" "$PRESENTATIONMODULESWIFT.bak"
    # Apply the fix - make sure we don't duplicate the return type
    if grep -q 'AsyncFunction("getPresentedNotificationsAsync") { () -> \[\[String: Any\]\] in () -> \[\[String: Any\]\] in' "$PRESENTATIONMODULESWIFT"; then
      # Fix duplicated return type
      sed -i.tmp 's/AsyncFunction("getPresentedNotificationsAsync") { () -> \[\[String: Any\]\] in () -> \[\[String: Any\]\] in.*/AsyncFunction("getPresentedNotificationsAsync") { () -> [[String: Any]] in/' "$PRESENTATIONMODULESWIFT"
    else
      # Normal fix
      sed -i.tmp 's/AsyncFunction("getPresentedNotificationsAsync") {/AsyncFunction("getPresentedNotificationsAsync") { () -> [[String: Any]] in/' "$PRESENTATIONMODULESWIFT"
    fi
    rm -f "$PRESENTATIONMODULESWIFT.tmp"
  else
    echo "PresentationModule.swift already fixed or has a different structure."
  fi
else
  echo "PresentationModule.swift not found."
fi

# SchedulerModule.swift
SCHEDULERMODULESWIFT="$PROJECT_ROOT/node_modules/expo-notifications/ios/EXNotifications/Notifications/Scheduling/SchedulerModule.swift"
if [ -f "$SCHEDULERMODULESWIFT" ]; then
  if grep -q 'AsyncFunction("getAllScheduledNotificationsAsync") {' "$SCHEDULERMODULESWIFT"; then
    echo "Fixing SchedulerModule.swift..."
    # Create a backup
    cp "$SCHEDULERMODULESWIFT" "$SCHEDULERMODULESWIFT.bak"
    # Apply the fix - make sure we don't duplicate the return type
    if grep -q 'AsyncFunction("getAllScheduledNotificationsAsync") { () -> \[\[String: Any\]\] in () -> \[\[String: Any\]\] in' "$SCHEDULERMODULESWIFT"; then
      # Fix duplicated return type
      sed -i.tmp 's/AsyncFunction("getAllScheduledNotificationsAsync") { () -> \[\[String: Any\]\] in () -> \[\[String: Any\]\] in.*/AsyncFunction("getAllScheduledNotificationsAsync") { () -> [[String: Any]] in/' "$SCHEDULERMODULESWIFT"
    else
      # Normal fix
      sed -i.tmp 's/AsyncFunction("getAllScheduledNotificationsAsync") {/AsyncFunction("getAllScheduledNotificationsAsync") { () -> [[String: Any]] in/' "$SCHEDULERMODULESWIFT"
    fi
    rm -f "$SCHEDULERMODULESWIFT.tmp"
  else
    echo "SchedulerModule.swift already fixed or has a different structure."
  fi
else
  echo "SchedulerModule.swift not found."
fi

echo "✅ Swift compatibility fixes applied successfully!"
echo "📱 You can now run 'npm run ios' to build and run the app."
echo "💡 Note: These fixes were applied manually and will need to be reapplied if you encounter Swift errors again after updating dependencies."
