const IS_DEV = process.env.APP_VARIANT === "development";
const IS_PREVIEW = process.env.APP_VARIANT === "preview";
import { version } from './package.json'

const getUniqueIdentifier = () => {
  return "com.aiplanmytrip.tripitineraryplanner";
};

const getAppName = () => {
  if (IS_DEV) {
    return "AiPlanMyTrip (Dev)";
  }

  if (IS_PREVIEW) {
    return "AiPlanMyTrip (Preview)";
  }

  return "AiPlanMyTrip";
};

const appIconBadgeConfig = {
  enabled: process.env.NODE_ENV !== 'production',
  badges: [
    {
      text: process.env.APP_VARIANT,
      type: 'banner',
      color: 'white',
    },
    {
      text: version,
      type: 'ribbon',
      color: 'white',
    },
  ],
};

const iosUrlScheme = () => {
  return "com.googleusercontent.apps.994695515433-tqti7rlm7h5s2kmkpb8d3ja765jnn76f";
}

export default ({ config }) => ({
  ...config,
  name: getAppName(),
  slug: "aiplanmytrip",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/logo.png",
  scheme: getUniqueIdentifier(),
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  ios: {
    supportsTablet: true,
    bundleIdentifier: getUniqueIdentifier(),
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      NSLocationWhenInUseUsageDescription: "This app uses your location to show relevant travel suggestions near you.",
      NSLocationAlwaysAndWhenInUseUsageDescription: "This app uses your location to offer features even when the app is in the background."
    },
    usesAppleSignIn: true,
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff",
    },
    package: getUniqueIdentifier(),
    edgeToEdgeEnabled: true,
  },
  web: {
    bundler: "metro",
    output: "server",
    favicon: "./assets/images/favicon.png",
  },
  plugins: [
    [
      "@react-native-google-signin/google-signin",
      {
        iosUrlScheme: iosUrlScheme(),
      },
    ],
    [
      "@rnmapbox/maps",
      {
        RNMapboxMapsDownloadToken: "*****************************************************************************************",
      },
    ],
    [
      "react-native-iap",
      {
        paymentProvider: "both",
      },
    ],
    [
      "expo-router",
      {
        origin: "https://aiplanmytrip.com/",
      },
    ],
    [
      "expo-splash-screen",
      {
        image: "./assets/images/splash-icon.png",
        imageWidth: 200,
        resizeMode: "contain",
        backgroundColor: "#2196F3",
        dark: {
          backgroundColor: "#2196F3",
          image: "./assets/images/splash-icon.png",
        }
      },
    ],
    [
      "expo-notifications",
      {
        icon: "./assets/images/icon.png",
        color: "#ffffff",
        sounds: ["./assets/sounds/notification.mp3"],
      },
    ],
    ['app-icon-badge', appIconBadgeConfig],
    "expo-apple-authentication",
  ],
  experiments: {
    typedRoutes: true,
  },
  "extra": {
    "eas": {
      "projectId": "a2af2661-912a-45c1-a6ca-4bd74ac3f2b3"
    }
  },
  "updates": {
    "url": "https://u.expo.dev/a2af2661-912a-45c1-a6ca-4bd74ac3f2b3"
  },
  runtimeVersion: {
    policy: "appVersion",
  },
});
