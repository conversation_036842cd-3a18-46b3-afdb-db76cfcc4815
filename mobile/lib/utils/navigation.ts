import { router } from "expo-router";

/**
 * Navigate to the trip view screen
 * @param tripId Trip ID
 * @param viewMode View mode (map or list)
 */
export const navigateToTripView = (
  tripId: string,
  viewMode: "map" | "list" = "map",
) => {
  router.dismissAll();
  router.replace({
    pathname: "/trip-view",
    params: { tripId, viewMode },
  });
};

/**
 * Navigate to the home screen
 */
export const navigateToHome = () => {
  router.replace("/home");
};

/**
 * Navigate to the trip details screen
 */
export const navigateToTripDetails = () => {
  router.replace("/trip-details");
};
