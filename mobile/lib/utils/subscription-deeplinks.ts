import { Alert, Linking, Platform } from 'react-native';
import { deepLinkToSubscriptions } from 'react-native-iap';

/**
 * Subscription management deeplink URLs for iOS and Android
 */
const SUBSCRIPTION_MANAGEMENT_URLS = {
  ios: 'https://apps.apple.com/account/subscriptions',
  android: 'https://play.google.com/store/account/subscriptions',
};

/**
 * Opens the platform-specific subscription management page
 * This allows users to cancel or modify their subscriptions
 */
export const openSubscriptionManagement = async (): Promise<void> => {
  try {

    deepLinkToSubscriptions({});
  } catch (error) {
    console.error('Error opening subscription management:', error);

    // Fallback alert with manual instructions
    const instructions = Platform.select({
      ios: 'Go to Settings > [Your Name] > Subscriptions to manage your subscription.',
      android: 'Go to Google Play Store > Menu > Subscriptions to manage your subscription.',
    });

    Alert.alert(
      'Subscription Management',
      `Unable to open subscription management automatically. ${instructions}`,
      [{ text: 'OK' }]
    );
  }
};

/**
 * Shows confirmation dialog for downgrading to free plan
 * Explains the process and provides deeplink to subscription management
 */
export const showBackToFreeConfirmation = (): Promise<boolean> => {
  return new Promise((resolve) => {
    Alert.alert(
      'Back to Free Plan',
      'To switch back to the Free Plan, you need to cancel your current subscription. We\'ll redirect you to your subscription management page where you can cancel.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve(false),
        },
        {
          text: 'Manage Subscription',
          onPress: async () => {
            await openSubscriptionManagement();
            resolve(true);
          },
        },
      ]
    );
  });
};
