/**
 * Format a date string to a human-readable format
 * @param date ISO date string
 * @param includeYear Whether to include the year in the formatted date
 * @returns Formatted date string (e.g., "Jan 1" or "Jan 1, 2023")
 */
export const formatDate = (date: string, includeYear: boolean = false) => {
  if (!date) return "Select date";

  const dateObj = new Date(date);
  return dateObj.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    ...(includeYear && { year: "numeric" }),
  });
};

export const timeAgo = (date: string) => {
  const finishedAt = new Date(date);
  const now = new Date();
  const diffInSeconds = Math.floor(
    (now.getTime() - finishedAt.getTime()) / 1000,
  );

  if (diffInSeconds < 60) {
    return `less than a minute ago`;
  }

  const diffInDays = Math.floor(diffInSeconds / (60 * 60 * 24));
  if (diffInDays === 0) {
    const diffInHours = Math.floor(diffInSeconds / (60 * 60));
    if (diffInHours === 0) {
      const diffInMinutes = Math.floor(diffInSeconds / 60);
      return `${diffInMinutes} mins ago`;
    }
    return `${diffInHours} hours ago`;
  }

  return `${diffInDays} days ago`;
};

export const formatTime = (time: string) => {
  if (!time) return "";
  try {
    let [hours, minutes] = time.split(":").map((num) => parseInt(num));
    if (isNaN(hours) || isNaN(minutes)) return "";

    if (hours >= 24) {
      hours = hours % 24;
    }

    const period = hours >= 12 ? "pm" : "am";
    hours = hours % 12;
    hours = hours === 0 ? 12 : hours;

    return `${String(hours).padStart(1, "0")}:${String(minutes).padStart(2, "0")}${period}`;
  } catch (error) {
    return "";
  }
};

export const timeToMinutes = (time: string) => {
  if (!time) return 0;
  const [hours, minutes] = time.split(":").map(Number);
  return isNaN(hours) || isNaN(minutes) ? 0 : hours * 60 + minutes;
};

export const minutesToTime = (totalMinutes: number) => {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
};

/**
 * Convert time to 24-hour format
 * @param time Time string in HH:MM format
 * @returns Time string in 24-hour format (e.g., "09:00")
 */
export const convertTo24HourFormat = (time: string) => {
  if (!time) return "00:00";
  const [hours, minutes] = time.split(":").map(Number);
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
};

/**
 * Check if one time is after another
 * @param time1 First time string in HH:MM format
 * @param time2 Second time string in HH:MM format
 * @returns True if time1 is after time2
 */
export const isTimeAfter = (time1: string, time2: string) => {
  const [hours1, minutes1] = time1.split(":").map(Number);
  const [hours2, minutes2] = time2.split(":").map(Number);
  return hours1 > hours2 || (hours1 === hours2 && minutes1 > minutes2);
};

/**
 * Calculate the number of days between two dates (inclusive)
 * @param startDate Start date string
 * @param endDate End date string
 * @returns Number of days (inclusive of both start and end dates)
 */
export const calculateDays = (startDate: string, endDate: string): number => {
  if (!startDate || !endDate) return 0;

  const start = new Date(startDate);
  const end = new Date(endDate);

  // Set time to midnight UTC to avoid timezone issues
  start.setUTCHours(0, 0, 0, 0);
  end.setUTCHours(0, 0, 0, 0);

  // Calculate the difference in milliseconds
  const diffTime = end.getTime() - start.getTime();

  // Convert to days using Math.floor to avoid rounding issues
  // Add 1 to include both the start and end date (inclusive counting)
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;

  // Ensure minimum of 1 day (same day trips)
  return Math.max(1, diffDays);
};

/**
 * Get a date string for a specific day in a trip
 * @param startDate Start date of the trip
 * @param dayNumber Day number (1-based)
 * @returns Date string for the specified day
 */
export const getDateForDay = (startDate: string, dayNumber: number): string => {
  if (!startDate) return "";

  const start = new Date(startDate);
  const day = new Date(start);
  day.setDate(start.getDate() + dayNumber - 1);

  return day.toISOString().split("T")[0];
};

/**
 * Format a date range as a string
 * @param startDate Start date string
 * @param endDate End date string
 * @returns Formatted date range string (e.g., "Jan 1 - Jan 5, 2023")
 */
export const formatDateRange = (startDate: string, endDate: string): string => {
  if (!startDate || !endDate) return "";

  const start = new Date(startDate);
  const end = new Date(endDate);

  const startMonth = start.toLocaleDateString("en-US", { month: "short" });
  const endMonth = end.toLocaleDateString("en-US", { month: "short" });
  const startDay = start.getDate();
  const endDay = end.getDate();
  const endYear = end.getFullYear();

  if (start.getFullYear() !== end.getFullYear()) {
    return `${startMonth} ${startDay}, ${start.getFullYear()} - ${endMonth} ${endDay}, ${endYear}`;
  }

  if (startMonth !== endMonth) {
    return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${endYear}`;
  }

  return `${startMonth} ${startDay} - ${endDay}, ${endYear}`;
};
