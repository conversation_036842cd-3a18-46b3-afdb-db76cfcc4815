import { ErrorCode } from "react-native-iap";
/**
 * All possible react-native-iap error codes
 */
export type IAPErrorCode = 
  | 'E_ACTIVITY_UNAVAILABLE'
  | 'E_ALREADY_OWNED'
  | 'E_ALREADY_PREPARED'
  | 'E_BILLING_RESPONSE_JSON_PARSE_ERROR'
  | 'E_CONNECTION_CLOSED'
  | 'E_DEFERRED_PAYMENT'
  | 'E_DEVELOPER_ERROR'
  | 'E_IAP_NOT_AVAILABLE'
  | 'E_INTERRUPTED'
  | 'E_ITEM_UNAVAILABLE'
  | 'E_NETWORK_ERROR'
  | 'E_NOT_ENDED'
  | 'E_NOT_PREPARED'
  | 'E_PENDING'
  | 'E_PURCHASE_ERROR'
  | 'E_RECEIPT_FAILED'
  | 'E_RECEIPT_FINISHED_FAILED'
  | 'E_REMOTE_ERROR'
  | 'E_SERVICE_ERROR'
  | 'E_SYNC_ERROR'
  | 'E_TRANSACTION_VALIDATION_FAILED'
  | 'E_UNKNOWN'
  | 'E_USER_CANCELLED'
  | 'E_USER_ERROR';

/**
 * Get user-friendly error message for react-native-iap error codes
 * @param errorCode The IAP error code or error message
 * @returns User-friendly error message, empty string for user cancellation
 */
export const getIAPErrorMessage = (errorCode: string): string => {
  // Extract error code if it's embedded in a longer message
  const iapErrorMatch = errorCode.match(/E_[A-Z_]+/);
  const code = iapErrorMatch ? iapErrorMatch[0] : errorCode;

  switch (code) {
    case 'E_ACTIVITY_UNAVAILABLE':
      return 'Payment service is temporarily unavailable. Please try again later.';
    
    case 'E_ALREADY_OWNED':
      return 'You already own this item. Check your purchases or try restoring them.';
    
    case 'E_ALREADY_PREPARED':
      return 'A purchase is already in progress. Please wait for it to complete.';
    
    case 'E_BILLING_RESPONSE_JSON_PARSE_ERROR':
      return 'There was an error processing your payment. Please try again.';
    
    case 'E_CONNECTION_CLOSED':
      return 'Connection to payment service was lost. Please check your internet connection and try again.';
    
    case 'E_DEFERRED_PAYMENT':
      return 'Your payment is pending approval. You\'ll receive access once it\'s approved.';
    
    case 'E_DEVELOPER_ERROR':
      return 'There\'s a configuration issue with this purchase. Please contact support.';
    
    case 'E_IAP_NOT_AVAILABLE':
      return 'In-app purchases are not available on this device. Please check your device settings.';
    
    case 'E_INTERRUPTED':
      return 'The purchase was interrupted. Please try again.';
    
    case 'E_ITEM_UNAVAILABLE':
      return 'This item is currently unavailable for purchase. Please try again later.';
    
    case 'E_NETWORK_ERROR':
      return 'Network error occurred. Please check your internet connection and try again.';
    
    case 'E_NOT_ENDED':
      return 'Previous purchase is still processing. Please wait a moment and try again.';
    
    case 'E_NOT_PREPARED':
      return 'Payment service is not ready. Please try again in a moment.';
    
    case 'E_PENDING':
      return 'Your purchase is being processed. Please wait for confirmation.';
    
    case 'E_PURCHASE_ERROR':
      return 'Purchase failed. Please try again or contact support if the problem persists.';
    
    case 'E_RECEIPT_FAILED':
      return 'Failed to verify your purchase receipt. Please try again or contact support.';
    
    case 'E_RECEIPT_FINISHED_FAILED':
      return 'Purchase completed but verification failed. Please contact support with your receipt.';
    
    case 'E_REMOTE_ERROR':
      return 'Server error occurred. Please try again later.';
    
    case 'E_SERVICE_ERROR':
      return 'Payment service error. Please try again later.';
    
    case 'E_SYNC_ERROR':
      return 'Failed to sync your purchase. Please try again or contact support.';
    
    case 'E_TRANSACTION_VALIDATION_FAILED':
      return 'Purchase validation failed. Please contact support if you were charged.';
    
    case 'E_UNKNOWN':
      return 'An unexpected error occurred. Please try again or contact support.';
    
    case 'E_USER_CANCELLED':
      return ''; // Don't show error for user cancellation
    
    case 'E_USER_ERROR':
      return 'Invalid purchase request. Please try again or contact support.';
    
    default:
      return 'An unexpected error occurred during purchase. Please try again.';
  }
};

/**
 * Check if an error code represents a user cancellation
 * @param errorCode The IAP error code
 * @returns True if the error represents user cancellation
 */
export const isUserCancellation = (errorCode: string): boolean => {
  const iapErrorMatch = errorCode.match(/E_[A-Z_]+/);
  const code = iapErrorMatch ? iapErrorMatch[0] : errorCode;
  
  return code === 'E_USER_CANCELLED' || 
         errorCode.includes('cancelled') || 
         errorCode.includes('canceled');
};

/**
 * Check if an error is recoverable (user can retry)
 * @param errorCode The IAP error code
 * @returns True if the error is recoverable
 */
export const isRecoverableError = (errorCode: string): boolean => {
  const iapErrorMatch = errorCode.match(/E_[A-Z_]+/);
  const code = iapErrorMatch ? iapErrorMatch[0] : errorCode;
  
  const nonRecoverableErrors = [
    'E_DEVELOPER_ERROR',
    'E_IAP_NOT_AVAILABLE',
    'E_USER_ERROR',
    'E_ALREADY_OWNED'
  ];
  
  return !nonRecoverableErrors.includes(code);
};

/**
 * Get error category for analytics/logging purposes
 * @param errorCode The IAP error code
 * @returns Error category
 */
export const getErrorCategory = (errorCode: string): 'network' | 'user' | 'system' | 'billing' | 'unknown' => {
  const iapErrorMatch = errorCode.match(/E_[A-Z_]+/);
  const code = iapErrorMatch ? iapErrorMatch[0] : errorCode;
  
  const networkErrors = ['E_NETWORK_ERROR', 'E_CONNECTION_CLOSED', 'E_REMOTE_ERROR'];
  const userErrors = ['E_USER_CANCELLED', 'E_USER_ERROR'];
  const systemErrors = ['E_DEVELOPER_ERROR', 'E_IAP_NOT_AVAILABLE', 'E_SERVICE_ERROR'];
  const billingErrors = ['E_BILLING_RESPONSE_JSON_PARSE_ERROR', 'E_PURCHASE_ERROR', 'E_RECEIPT_FAILED'];
  
  if (networkErrors.includes(code)) return 'network';
  if (userErrors.includes(code)) return 'user';
  if (systemErrors.includes(code)) return 'system';
  if (billingErrors.includes(code)) return 'billing';
  
  return 'unknown';
};
