import { API_URL } from "../constants";

interface LocationSuggestion {
  name: string;
  country?: string;
  type: "country" | "city";
  coordinates?: {
    lat: number;
    lng: number;
  };
  population?: number;
}

// Get location suggestions from backend API
export async function getLocationSuggestions(
  query: string,
  type: "country" | "city" | "both" = "both",
  signal?: AbortSignal,
): Promise<LocationSuggestion[]> {
  try {
    if (!query || query.length < 2) return [];

    const response = await fetch(
      `${API_URL}/locations/search?query=${encodeURIComponent(query)}&type=${type}`,
      {
        headers: {
          Accept: "application/json",
        },
        signal,
      },
    );

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    const suggestions: LocationSuggestion[] = await response.json();
    return suggestions;
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }
    console.error("Error getting location suggestions:", error);
    return [];
  }
}

// Get country suggestions
export async function getCountrySuggestions(
  query: string,
  signal?: AbortSignal,
): Promise<LocationSuggestion[]> {
  try {
    if (!query || query.length < 2) return [];

    const response = await fetch(
      `${API_URL}/locations/countries?keyword=${encodeURIComponent(query)}`,
      {
        headers: {
          Accept: "application/json",
        },
        signal,
      },
    );

    if (!response.ok) {
      throw new Error(`Country search failed: ${response.statusText}`);
    }

    const suggestions: LocationSuggestion[] = await response.json();
    return suggestions;
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }
    console.error("Error getting country suggestions:", error);
    return [];
  }
}

// Get city suggestions for a specific country
export async function getCitySuggestions(
  query: string,
  country?: string,
  signal?: AbortSignal,
): Promise<LocationSuggestion[]> {
  try {
    if (!query || query.length < 2) return [];

    const url = new URL(`${API_URL}/locations/cities`);
    url.searchParams.append("keyword", query);
    if (country) {
      url.searchParams.append("country", country);
    }

    const response = await fetch(url.toString(), {
      headers: {
        Accept: "application/json",
      },
      signal,
    });

    if (!response.ok) {
      throw new Error(`City search failed: ${response.statusText}`);
    }

    const suggestions: LocationSuggestion[] = await response.json();
    return suggestions;
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }
    console.error("Error getting city suggestions:", error);
    return [];
  }
}

// Validate a location exists and get its details
export async function validateLocation(
  name: string,
  type: "country" | "city",
  signal?: AbortSignal,
): Promise<LocationSuggestion | null> {
  try {
    const response = await fetch(
      `${API_URL}/locations/validate?name=${encodeURIComponent(name)}&type=${type}`,
      {
        headers: {
          Accept: "application/json",
        },
        signal,
      },
    );

    if (!response.ok) {
      throw new Error(`Validation failed: ${response.statusText}`);
    }

    const result: LocationSuggestion | null = await response.json();
    return result;
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }
    console.error("Error validating location:", error);
    return null;
  }
}

// Interface for country region data
export interface CountryRegion {
  name: string;
  code: string;
  lat: number;
  lng: number;
  region: {
    latitudeDelta: number;
    longitudeDelta: number;
  };
}

export interface CountryRegionsResponse {
  regions: CountryRegion[];
}

// Get all country regions for map view
export async function getCountryRegions(
  signal?: AbortSignal,
): Promise<CountryRegionsResponse> {
  try {
    const response = await fetch(`${API_URL}/locations/country-regions`, {
      headers: {
        Accept: "application/json",
      },
      signal,
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch country regions: ${response.statusText}`,
      );
    }

    const result: CountryRegionsResponse = await response.json();
    return result;
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      throw error;
    }
    console.error("Error fetching country regions:", error);
    return { regions: [] };
  }
}

// Find a country region by name
export async function findCountryRegionByName(
  countryName: string,
  signal?: AbortSignal,
): Promise<CountryRegion | null> {
  try {
    const regions = await getCountryRegions(signal);
    const region = regions.regions.find(
      (r) => r.name.toLowerCase() === countryName.toLowerCase(),
    );
    return region || null;
  } catch (error) {
    console.error("Error finding country region:", error);
    return null;
  }
}
