import { Activity } from "../types";

/**
 * Get icon name based on activity type
 * @param activity Activity object
 * @returns Icon name for the activity
 */
export const getActivityIcon = (activity: Activity) => {
  switch (activity.type) {
    case "meal":
      return activity.mealType === "breakfast"
        ? "cafe-outline"
        : "restaurant-outline";
    default:
      return "location-outline";
  }
};

/**
 * Get color based on activity type
 * @param type Activity type
 * @returns Color for the activity type
 */
export const getActivityColor = (type: Activity["type"]) => {
  switch (type) {
    case "meal":
      return "#4CAF50";
    default:
      return "#2196F3";
  }
};
