import { Platform } from "react-native";

// Conditional MMKV import to avoid web platform issues
let MMKV: any = null;

if (Platform.OS !== 'web') {
  const MMKVModule = require("react-native-mmkv");
  MMKV = MMKVModule.MMKV;
}

/**
 * Storage domains for different parts of the application
 * Each domain has its own isolated storage instance
 */
export enum StorageDomain {
  APP_SETTINGS = "app-settings",
  USER_PREFERENCES = "user-preferences",
  TUTORIAL = "tutorial",
  CACHE = "cache",
  AUTH = "auth",
  TRIPS = "trips",
}

// Web storage interface that mimics MMKV
interface WebStorageInterface {
  set(key: string, value: string | number | boolean): void;
  getString(key: string): string | undefined;
  getNumber(key: string): number | undefined;
  getBoolean(key: string): boolean | undefined;
  delete(key: string): void;
  contains(key: string): boolean;
  clearAll(): void;
  getAllKeys(): string[];
}

// Web storage implementation using localStorage with domain prefixing
class WebStorage implements WebStorageInterface {
  private domain: string;

  constructor(domain: string) {
    this.domain = domain;
  }

  private getKey(key: string): string {
    return `${this.domain}:${key}`;
  }

  set(key: string, value: string | number | boolean): void {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.setItem(this.getKey(key), String(value));
    }
  }

  getString(key: string): string | undefined {
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage.getItem(this.getKey(key)) || undefined;
    }
    return undefined;
  }

  getNumber(key: string): number | undefined {
    const value = this.getString(key);
    return value ? Number(value) : undefined;
  }

  getBoolean(key: string): boolean | undefined {
    const value = this.getString(key);
    return value ? value === 'true' : undefined;
  }

  delete(key: string): void {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.removeItem(this.getKey(key));
    }
  }

  contains(key: string): boolean {
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage.getItem(this.getKey(key)) !== null;
    }
    return false;
  }

  clearAll(): void {
    if (typeof window !== 'undefined' && window.localStorage) {
      const keysToRemove: string[] = [];
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key && key.startsWith(`${this.domain}:`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => window.localStorage.removeItem(key));
    }
  }

  getAllKeys(): string[] {
    if (typeof window !== 'undefined' && window.localStorage) {
      const keys: string[] = [];
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key && key.startsWith(`${this.domain}:`)) {
          keys.push(key.substring(this.domain.length + 1));
        }
      }
      return keys;
    }
    return [];
  }
}

/**
 * Storage utility class that provides a wrapper around MMKV (native) or localStorage (web)
 * with type safety and domain isolation
 */
export class Storage {
  private static instances: Record<StorageDomain, any> = {} as Record<
    StorageDomain,
    any
  >;
  private storage: any;

  /**
   * Get a Storage instance for a specific domain
   * @param domain The storage domain
   * @returns A Storage instance for the specified domain
   */
  public static getInstance(domain: StorageDomain): Storage {
    if (!this.instances[domain]) {
      if (Platform.OS === 'web') {
        this.instances[domain] = new WebStorage(domain);
      } else if (MMKV) {
        this.instances[domain] = new MMKV({
          id: domain,
          encryptionKey:
            domain === StorageDomain.AUTH ? "secure-storage-key" : undefined,
        });
      } else {
        // Fallback for when MMKV is not available
        this.instances[domain] = new WebStorage(domain);
      }
    }

    return new Storage(this.instances[domain]);
  }

  /**
   * Get the domain name from a storage instance
   * @returns The domain name for this storage instance
   */
  public getDomain(): string {
    // Find the domain name by comparing storage instances
    for (const [domain, instance] of Object.entries(Storage.instances)) {
      if (instance === this.storage) {
        return domain;
      }
    }
    return "unknown";
  }

  private constructor(storage: MMKV) {
    this.storage = storage;
  }

  /**
   * Set a string value in storage
   * @param key The storage key
   * @param value The string value to store
   */
  public setString(key: string, value: string): void {
    this.storage.set(key, value);
  }

  /**
   * Get a string value from storage
   * @param key The storage key
   * @param defaultValue The default value to return if the key doesn't exist
   * @returns The stored string value or the default value
   */
  public getString(key: string, defaultValue: string = ""): string {
    const value = this.storage.getString(key);
    return value !== undefined ? value : defaultValue;
  }

  /**
   * Set a number value in storage
   * @param key The storage key
   * @param value The number value to store
   */
  public setNumber(key: string, value: number): void {
    this.storage.set(key, value);
  }

  /**
   * Get a number value from storage
   * @param key The storage key
   * @param defaultValue The default value to return if the key doesn't exist
   * @returns The stored number value or the default value
   */
  public getNumber(key: string, defaultValue: number = 0): number {
    const value = this.storage.getNumber(key);
    return value !== undefined ? value : defaultValue;
  }

  /**
   * Set a boolean value in storage
   * @param key The storage key
   * @param value The boolean value to store
   */
  public setBoolean(key: string, value: boolean): void {
    this.storage.set(key, value);
  }

  /**
   * Get a boolean value from storage
   * @param key The storage key
   * @param defaultValue The default value to return if the key doesn't exist
   * @returns The stored boolean value or the default value
   */
  public getBoolean(key: string, defaultValue: boolean = false): boolean {
    const value = this.storage.getBoolean(key);
    return value !== undefined ? value : defaultValue;
  }

  /**
   * Set an object value in storage (serialized as JSON)
   * @param key The storage key
   * @param value The object to store
   */
  public setObject<T extends object>(key: string, value: T): void {
    this.storage.set(key, JSON.stringify(value));
  }

  /**
   * Get an object value from storage
   * @param key The storage key
   * @param defaultValue The default value to return if the key doesn't exist
   * @returns The stored object or the default value
   */
  public getObject<T extends object>(key: string, defaultValue: T): T {
    const value = this.storage.getString(key);
    if (value === undefined) {
      return defaultValue;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error(`Error parsing stored object for key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Delete a value from storage
   * @param key The storage key to delete
   */
  public delete(key: string): void {
    this.storage.delete(key);
  }

  /**
   * Check if a key exists in storage
   * @param key The storage key to check
   * @returns True if the key exists, false otherwise
   */
  public contains(key: string): boolean {
    return this.storage.contains(key);
  }

  /**
   * Clear all values in this storage domain
   */
  public clearAll(): void {
    this.storage.clearAll();
  }

  /**
   * Get all keys in this storage domain
   * @returns Array of all keys
   */
  public getAllKeys(): string[] {
    return this.storage.getAllKeys();
  }
}

/**
 * Convenience function to get a storage instance for a specific domain
 * @param domain The storage domain
 * @returns A Storage instance for the specified domain
 */
export const getStorage = (domain: StorageDomain): Storage => {
  return Storage.getInstance(domain);
};
