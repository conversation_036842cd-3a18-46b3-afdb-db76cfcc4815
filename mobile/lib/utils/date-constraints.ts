import { DaysBalance } from "../services/days-balance.service";

/**
 * Maximum days allowed for onboarding users
 */
export const ONBOARDING_MAX_DAYS = 35;

/**
 * Check if user is in onboarding mode
 * For now, we'll consider users with basic plan and low usage as onboarding users
 * This can be enhanced later with a dedicated onboarding flag
 */
export const isUserInOnboarding = (balance: DaysBalance | null): boolean => {
  if (!balance) return true; // New users without balance are in onboarding

  // Consider users with basic plan and very low usage as onboarding users
  return (
    balance.currentPlan === "free" &&
    balance.totalDaysUsed <= 1
  );
};

/**
 * Calculate maximum allowed trip duration based on user balance and onboarding status
 */
export const calculateMaxTripDuration = (balance: DaysBalance | null): number => {
  // If user is in onboarding, limit to 5 days
  if (isUserInOnboarding(balance)) {
    return ONBOARDING_MAX_DAYS;
  }

  // If no balance, default to onboarding limit
  if (!balance) {
    return ONBOARDING_MAX_DAYS;
  }

  // For regular users, use their available balance
  const totalAvailableDays = balance.totalDays || (balance.subscriptionDays + balance.packDays);

  // Ensure minimum of 1 day
  return Math.max(1, totalAvailableDays);
};

/**
 * Calculate maximum departure date based on arrival date and user constraints
 */
export const calculateMaxDepartureDate = (
  arrivalDate: Date,
  balance: DaysBalance | null
): Date => {
  const maxDuration = calculateMaxTripDuration(balance);
  const maxDepartureDate = new Date(arrivalDate);
  // Since we include both arrival and departure dates, we add (maxDuration - 1) days
  maxDepartureDate.setDate(arrivalDate.getDate() + maxDuration - 1);
  return maxDepartureDate;
};

/**
 * Calculate minimum departure date (must be at least 1 day after arrival)
 */
export const calculateMinDepartureDate = (arrivalDate: Date): Date => {
  const minDepartureDate = new Date(arrivalDate);
  minDepartureDate.setDate(arrivalDate.getDate() + 1);
  return minDepartureDate;
};

/**
 * Calculate trip duration in days (inclusive of both start and end dates)
 *
 * Examples:
 * - Jan 1 to Jan 1 = 1 day
 * - Jan 1 to Jan 5 = 5 days
 * - Jan 1 to Jan 2 = 2 days
 */
export const calculateTripDuration = (startDate: string, endDate: string): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Set both dates to midnight UTC to avoid timezone issues
  start.setUTCHours(0, 0, 0, 0);
  end.setUTCHours(0, 0, 0, 0);

  // Calculate difference in days and add 1 to include both start and end dates
  const diffInMs = end.getTime() - start.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // Add 1 to include both first and last day, ensure minimum of 1 day
  return Math.max(1, diffInDays + 1);
};

/**
 * Validate if selected trip duration is within allowed limits
 */
export const validateTripDuration = (
  startDate: string,
  endDate: string,
  balance: DaysBalance | null,
  context: 'onboarding' | 'trip-details' = 'trip-details'
): { isValid: boolean; error?: string; maxDays: number } => {
  const duration = calculateTripDuration(startDate, endDate);
  const maxDays = calculateMaxTripDuration(balance);

  if (duration > maxDays) {
    let errorMessage: string;

    if (context === 'onboarding') {
      // For onboarding screen, always show the 5-day limit message
      errorMessage = `Trip duration cannot exceed ${ONBOARDING_MAX_DAYS} days for your first trip`;
    } else {
      // For trip details screen, show appropriate message based on user status
      const isOnboarding = isUserInOnboarding(balance);
      if (isOnboarding) {
        errorMessage = `Trip duration cannot exceed ${maxDays} days`;
      } else {
        errorMessage = `Trip duration cannot exceed your available balance of ${maxDays} days`;
      }
    }

    return {
      isValid: false,
      error: errorMessage,
      maxDays,
    };
  }

  if (duration < 1) {
    return {
      isValid: false,
      error: "Trip must be at least 1 day long",
      maxDays,
    };
  }

  return {
    isValid: true,
    maxDays,
  };
};
