import { DbTrip as Trip } from "../types";

/**
 * Format a date string to a localized date string
 * @param dateString Date string
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString();
};

/**
 * Get the total number of activities in a trip
 * @param itinerary Trip itinerary
 * @returns Total number of activities
 */
export const getTotalActivities = (
  itinerary: Trip["itinerary"] | undefined,
): number => {
  if (!itinerary) return 0;
  return itinerary.reduce((acc, day) => acc + day.activities.length, 0);
};

/**
 * Generate a share message based on trip details
 * @param trip Trip object
 * @param shareUrl Share URL
 * @returns Share message
 */
export const getShareMessage = (
  trip: Trip | null,
  shareUrl: string,
): string => {
  if (!trip) return "";

  const tripDetails = trip.tripDetails;
  const itinerary = trip.itinerary;

  return (
    `Check out my trip to ${tripDetails?.destination}!\n` +
    `From ${formatDate(tripDetails?.startDate)} to ${formatDate(tripDetails?.endDate)}\n` +
    `${itinerary?.length} days • ${getTotalActivities(itinerary)} activities\n\n` +
    `View my full itinerary: ${shareUrl}`
  );
};
