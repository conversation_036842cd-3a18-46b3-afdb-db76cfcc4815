import * as Haptics from "expo-haptics";

/**
 * Utility functions for haptic feedback
 */

/**
 * Trigger light haptic feedback (for minor interactions)
 */
export const lightHapticFeedback = () => {
  try {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger medium haptic feedback (for standard interactions)
 */
export const mediumHapticFeedback = () => {
  try {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger heavy haptic feedback (for significant interactions)
 */
export const heavyHapticFeedback = () => {
  try {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger success haptic feedback (for successful operations)
 */
export const successHapticFeedback = () => {
  try {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger warning haptic feedback (for warning operations)
 */
export const warningHapticFeedback = () => {
  try {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger error haptic feedback (for error operations)
 */
export const errorHapticFeedback = () => {
  try {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};

/**
 * Trigger selection haptic feedback (for selection changes)
 */
export const selectionHapticFeedback = () => {
  try {
    Haptics.selectionAsync();
  } catch (error) {
    console.warn("Haptic feedback failed:", error);
  }
};
