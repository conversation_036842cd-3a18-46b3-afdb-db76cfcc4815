/**
 * Extract required days from an error message
 * @param errorMessage Error message
 * @param currentBalance Current balance
 * @returns Required days
 */
export const extractRequiredDays = (
  errorMessage: string,
  currentBalance?: number,
): number => {
  // Check for properly formatted balance message with "Required:" pattern
  if (errorMessage.includes("Required:")) {
    const requiredMatch = errorMessage.match(/Required:\s*(\d+)/i);
    if (requiredMatch && requiredMatch[1]) {
      return parseInt(requiredMatch[1], 10);
    }
  }

  // Check for properly formatted balance message with "Needed:" pattern
  if (errorMessage.includes("Needed:")) {
    const neededMatch = errorMessage.match(/Needed:\s*(\d+)/i);
    if (neededMatch && neededMatch[1]) {
      return parseInt(neededMatch[1], 10);
    }
  }

  // Check for other insufficient balance messages
  const dayMatch = errorMessage.match(/need (\d+) more days/i);
  if (dayMatch && dayMatch[1]) {
    return parseInt(dayMatch[1], 10);
  }

  // Check for direct day amount mentions
  const directDayMatch = errorMessage.match(/(\d+) days/i);
  if (directDayMatch && directDayMatch[1]) {
    return parseInt(directDayMatch[1], 10);
  }

  // Default required amount (conservative estimate)
  return 1;
};

/**
 * Check if an error message indicates insufficient balance
 * @param errorMessage Error message
 * @returns Whether the error message indicates insufficient balance
 * @deprecated Use context.isInsufficientBalance from socket handlers instead
 */
export const isInsufficientBalanceError = (errorMessage: string): boolean => {
  // Only match very specific backend formatted error messages
  // This is kept as a fallback but should not be the primary detection method
  return (
    errorMessage.includes("Available:") && errorMessage.includes("Needed:")
  );
};
