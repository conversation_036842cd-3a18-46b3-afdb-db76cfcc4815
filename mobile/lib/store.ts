import { create } from "zustand";
import { persist } from "./hooks/use-storage";
import { AuthState, AuthStore, TripDetails, TripStore } from "./types";
import { INTERESTS } from "./constants";

// Re-export payment error store
export { usePaymentErrorStore } from "./store/payment-error.store";

// Global modal state interface
interface GlobalModalState {
  showDaysQuotaModal: boolean;
  requiredDays: number;
  currentBalance: number;
}

interface GlobalModalStore extends GlobalModalState {
  setShowDaysQuotaModal: (show: boolean) => void;
  setDaysQuotaModalData: (requiredDays: number, currentBalance: number) => void;
  showInsufficientDaysModal: (requiredDays: number, currentBalance: number) => void;
  hideDaysQuotaModal: () => void;
}

const initialAuthState: AuthState = {
  status: "idle",
  loadingType: undefined,
  user: undefined,
  token: undefined,
  error: undefined,
};
const startDate = new Date();
const endDate = new Date();
endDate.setDate(startDate.getDate() + 3);

const tripDetails: TripDetails = {
  destination: "Spain",
  startDate: startDate.toISOString(),
  intensity: 8,
  endDate: endDate.toISOString(),
  budget: "normal",
  travelType: "Adventure",
  mustVisitCities: [],
  additionalRequirements: "",
  cuisinePreferences: [],
  arrivalCity: "Madrid",
  departureCity: "Barcelona",
  arrivalMode: "Land" as const,
  departureMode: "Land" as const,
  arrivalTime: "10:00",
  departureTime: "16:00",
  wakeUpTime: "07:00",
  sleepTime: "23:00",
  totalDays: 10,
  interests: [],
  people: {
    adults: 2,
    children: 0,
  },
};

const initialState = {
  tripDetails,
  customInterests: [],
};

export const useTripStore = create<TripStore>((set) => ({
  ...initialState,
  setTripDetails: (details) =>
    set((state) => ({
      tripDetails: { ...state.tripDetails, ...details },
    })),

  addInterest: (interest) =>
    set((state) => ({
      tripDetails: {
        ...state.tripDetails,
        interests: [
          ...state.tripDetails.interests.filter((i) => i !== interest.name),
          interest.name,
        ],
      },
      customInterests: interest.id.startsWith("custom-")
        ? [
          ...state.customInterests.filter((i) => i.id !== interest.id),
          interest,
        ]
        : state.customInterests,
    })),
  removeInterest: (interestId) =>
    set((state) => {
      const interestToRemove = [...INTERESTS, ...state.customInterests].find(
        (i) => i.id === interestId,
      );
      return {
        tripDetails: {
          ...state.tripDetails,
          interests: state.tripDetails.interests.filter(
            (i) => i !== interestToRemove?.name,
          ),
        },
        customInterests: state.customInterests.filter(
          (i) => i.id !== interestId,
        ),
      };
    }),

  reset: () => set({ ...initialState }),
}));

export const useAuthStore = create<AuthStore>((set) => ({
  auth: initialAuthState,
  setAuth: (auth) => {
    set({ auth });
    if (auth.token) {
      persist("token", auth.token);
    }
  },
}));

export const getToken = () => useAuthStore.getState().auth.token;
export const getUser = () => useAuthStore.getState().auth.user;
export const getStatus = () => useAuthStore.getState().auth.status;
export const getError = () => useAuthStore.getState().auth.error;
export const setAuth = (auth: AuthState) =>
  useAuthStore.getState().setAuth(auth);

// Global modal store
export const useGlobalModalStore = create<GlobalModalStore>((set) => ({
  showDaysQuotaModal: false,
  requiredDays: 0,
  currentBalance: 0,

  setShowDaysQuotaModal: (show: boolean) =>
    set({ showDaysQuotaModal: show }),

  setDaysQuotaModalData: (requiredDays: number, currentBalance: number) =>
    set({ requiredDays, currentBalance }),

  showInsufficientDaysModal: (requiredDays: number, currentBalance: number) =>
    set({
      showDaysQuotaModal: true,
      requiredDays,
      currentBalance
    }),

  hideDaysQuotaModal: () =>
    set({ showDaysQuotaModal: false }),
}));
