import { API_URL } from "../constants";
import { CommonService } from "./common.service";

export interface DaysBalance {
  _id: string;
  userId: string;
  subscriptionDays: number;
  packDays: number; // Changed from refillDays to match backend
  totalDaysUsed: number;
  totalDaysAdded: number;
  currentPlan: "free" | "pro" | "premium"; // Changed from subscriptionPlan to match backend
  subscriptionStatus: "active" | "inactive" | "pending" | "cancelled";
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  lastResetDate: string;
  nextBillingDate: string; // Changed from subscriptionRenewsAt to match backend
  totalDays: number; // Added field from backend response
  pendingPlanChange?: {
    newPlan: "free" | "pro" | "premium";
    oldPlan: "free" | "pro" | "premium";
    changeDate: string;
    effectiveDate: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface DaysTransaction {
  _id: string;
  userId: string;
  type: "debit" | "credit";
  amount: number;
  balanceAfter: number;
  source:
  | "trip_generation"
  | "manual_addition"
  | "initial_grant"
  | "subscription"
  | "day_refill";
  tripId?: string;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export class DaysBalanceService extends CommonService {
  private static instance: DaysBalanceService;

  private constructor() {
    super();
  }

  static getInstance(): DaysBalanceService {
    if (!DaysBalanceService.instance) {
      DaysBalanceService.instance = new DaysBalanceService();
    }
    return DaysBalanceService.instance;
  }

  /**
   * Get the user's days balance
   * @returns DaysBalance object
   */
  async getBalance(): Promise<DaysBalance> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/balance`, {
        headers,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch days balance");
      }

      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Get the user's days transaction history
   * @param limit Number of transactions to return (default: 20)
   * @param page Page number for pagination (default: 1)
   * @returns PaginatedResponse with DaysTransaction objects and pagination metadata
   */
  async getTransactionHistory(
    limit: number = 20,
    page: number = 1,
  ): Promise<PaginatedResponse<DaysTransaction>> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(
        `${API_URL}/balance/transactions?limit=${limit}&page=${page}`,
        {
          headers,
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch transaction history");
      }

      const jsonResponse = await response.json();

      // Handle both formats: direct array or paginated response
      // This ensures backward compatibility if the backend hasn't been updated yet
      if (Array.isArray(jsonResponse)) {
        // If the backend returns just an array, create a paginated response
        return {
          data: jsonResponse,
          pagination: {
            totalItems: jsonResponse.length,
            totalPages: 1,
            currentPage: page,
            hasNextPage: false,
            hasPreviousPage: page > 1,
          },
        };
      }

      // If the backend already returns a paginated response, use it directly
      return jsonResponse;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Add days to the user's balance (for testing/development purposes)
   * In production, this would be handled by a payment processor
   * @param amount Number of days to add
   * @param description Optional description for the transaction
   * @returns Updated DaysBalance
   */
  async addDays(
    amount: number,
    description: string = "Manual day addition",
  ): Promise<DaysBalance> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/balance/add`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          amount,
          description,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to add days");
      }

      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Get total available days (uses backend-calculated total)
   * @param balance DaysBalance object
   * @returns Total available days
   */
  getTotalAvailableDays(balance: DaysBalance): number {
    return balance.totalDays;
  }
}
