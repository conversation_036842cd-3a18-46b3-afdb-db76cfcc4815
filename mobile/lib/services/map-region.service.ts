import { API_URL } from "../constants";
import { Region } from "../../ui/map/MapUtils";
import { CommonService } from "./common.service";

/**
 * Interface for map region response from the backend
 */
interface MapRegionResponse {
  mapRegion: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  tripId: string;
}

/**
 * Interface for daily map region response from the backend
 */
interface DailyMapRegionResponse extends MapRegionResponse {
  dayNumber: number;
}

/**
 * Interface for country map region response from the backend
 */
interface CountryMapRegionResponse {
  region: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  country: string;
}

/**
 * Service for fetching map region data from the backend
 */
export class MapRegionService extends CommonService {
  private static instance: MapRegionService | null = null;

  constructor() {
    super();
  }

  static getInstance(): MapRegionService {
    if (!MapRegionService.instance) {
      MapRegionService.instance = new MapRegionService();
    }
    return MapRegionService.instance;
  }
  /**
   * Get map region for a trip
   * @param tripId Trip ID
   * @returns Map region data
   */
  async getTripMapRegion(tripId: string): Promise<Region | null> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/trips/${tripId}/map-region`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch trip map region: ${response.statusText}`,
        );
      }

      const data: MapRegionResponse = await response.json();
      return data.mapRegion;
    } catch (error) {
      console.error("Error fetching trip map region:", error);
      return null;
    }
  }

  /**
   * Get map region for a specific day of a trip
   * @param tripId Trip ID
   * @param dayNumber Day number
   * @returns Map region data
   */
  async getDailyMapRegion(
    tripId: string,
    dayNumber: number,
  ): Promise<Region | null> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(
        `${API_URL}/trips/${tripId}/daily-map-region/${dayNumber}`,
        {
          headers,
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch daily map region: ${response.statusText}`,
        );
      }

      const data: DailyMapRegionResponse = await response.json();
      return data.mapRegion;
    } catch (error) {
      console.error("Error fetching daily map region:", error);
      return null;
    }
  }

  /**
   * Get map region for a specific city of a trip
   * @param city City name
   * @Param tripId Trip ID
   * @param day Optional day number
   * @returns Map region data
   */
  async getCityMapRegion(
    tripId: string,
    city: string,
    day?: number,
  ): Promise<Region | null> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(
        `${API_URL}/trips/${tripId}/city-map-region/${city}?day=${day}`,
        {
          headers,
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch city map region: ${response.statusText}`,
        );
      }

      const data: MapRegionResponse = await response.json();
      return data.mapRegion;
    } catch (error) {
      console.error("Error fetching city map region:", error);
      return null;
    }
  }

  /**
   * Get map region for a country
   * @param countryName Country name
   * @returns Map region data
   */
  async getCountryMapRegion(countryName: string): Promise<Region | null> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(
        `${API_URL}/map-regions/country/${encodeURIComponent(countryName)}`,
        {
          headers,
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch country map region: ${response.statusText}`,
        );
      }

      const data: CountryMapRegionResponse = await response.json();
      return data.region;
    } catch (error) {
      console.error("Error fetching country map region:", error);
      return null;
    }
  }
}
