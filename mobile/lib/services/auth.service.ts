import * as <PERSON><PERSON>rowser from "expo-web-browser";
import { API_URL } from "../constants";

WebBrowser.maybeCompleteAuthSession();

export class AuthService {
  private static instance: AuthService;

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async requestVerificationCode(email: string) {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      throw new Error("Login failed");
    }

    return response.json();
  }

  async loginWithGoogle({ token }: { token: string }) {
    try {
      // Send the ID token to your backend
      const response = await fetch(`${API_URL}/auth/google`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        throw new Error("Google authentication failed");
      }

      const data = await response.json();
      return {
        access_token: data.access_token,
        user: data.user,
      };
    } catch (error) {
      console.error("Google Sign In Error:", error);
      throw error;
    }
  }

  async loginWithApple({ identityToken, user }: {
    identityToken: string;
    user?: { name?: { firstName?: string; lastName?: string } }
  }) {
    try {
      // Send the identity token to your backend
      const response = await fetch(`${API_URL}/auth/apple`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ identityToken, user }),
      });

      if (!response.ok) {
        throw new Error("Apple authentication failed");
      }

      const data = await response.json();
      return {
        access_token: data.access_token,
        user: data.user,
      };
    } catch (error) {
      console.error("Apple Sign In Error:", error);
      throw error;
    }
  }

  async logout() {
    const response = await fetch(`${API_URL}/auth/logout`, {
      method: "POST",
    });
    return response.ok;
  }

  async checkToken(token: string) {
    const response = await fetch(`${API_URL}/auth/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token }),
    });
    if (!response.ok) {
      throw new Error("Token check failed");
    }
    return response.json();
  }

  async sendVerificationCode(email: string, code: string) {
    const response = await fetch(`${API_URL}/auth/verify-code`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, code }),
    });
    if (!response.ok) {
      throw new Error("Verification code failed");
    }
    return response.json();
  }
}
