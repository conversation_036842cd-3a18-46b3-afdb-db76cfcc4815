import { API_URL } from "../constants";

import { DayProgress, DbTrip } from "../types";
import { CommonService } from "./common.service";

export class TripService extends CommonService {
  private static instance: TripService | null = null;

  private constructor() {
    super();
  }

  static getInstance(): TripService {
    if (!TripService.instance) {
      TripService.instance = new TripService();
    }
    return TripService.instance;
  }

  async fetchTrips(
    includeArchived: boolean = false,
    includeExamples: boolean = true
  ): Promise<DbTrip[]> {
    try {
      const headers = await this.setTokenInHeaders();
      const queryParams = new URLSearchParams();

      if (includeArchived) {
        queryParams.append("includeArchived", "true");
      }

      if (includeExamples) {
        queryParams.append("includeExamples", "true");
      }

      const queryString = queryParams.toString();
      const url = `${API_URL}/trips${queryString ? `?${queryString}` : ""}`;

      const response = await fetch(url, {
        headers,
      });
      if (!response.ok) {
        throw new Error("Failed to fetch trips");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Fetch trips with filters
   * @param filters Object containing filter criteria
   * @returns Filtered trips
   */
  async fetchFilteredTrips(filters: {
    searchQuery?: string;
    favorite?: boolean;
    generating?: boolean;
    ready?: boolean;
    includeArchived?: boolean;
  }): Promise<DbTrip[]> {
    try {
      const headers = await this.setTokenInHeaders();

      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters.searchQuery) {
        queryParams.append("search", filters.searchQuery);
      }

      if (filters.favorite) {
        queryParams.append("favorite", "true");
      }

      if (filters.generating) {
        queryParams.append("status", "in_progress");
      }

      if (filters.ready) {
        queryParams.append("status", "ready");
      }

      if (filters.includeArchived) {
        queryParams.append("includeArchived", "true");
      }

      const queryString = queryParams.toString();
      const url = `${API_URL}/trips${queryString ? `?${queryString}` : ""}`;

      const response = await fetch(url, {
        headers,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch filtered trips");
      }

      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Fetch all cities for a trip
   * @param tripId Trip ID
   * @returns Array of city names
   */
  async fetchTripCities(tripId: string): Promise<string[]> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/trips/${tripId}/cities`, {
        headers,
      });
      if (!response.ok) {
        throw new Error("Failed to fetch trip cities");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Fetch activities grouped by city for a trip
   * @param tripId Trip ID
   * @param city Optional city filter
   * @returns Object with city names as keys and arrays of activities as values
   */
  async fetchActivitiesByCity(
    tripId: string,
    city?: string,
  ): Promise<Record<string, any[]>> {
    try {
      const headers = await this.setTokenInHeaders();
      const url = city
        ? `${API_URL}/trips/${tripId}/activities-by-city?city=${encodeURIComponent(city)}`
        : `${API_URL}/trips/${tripId}/activities-by-city`;

      const response = await fetch(url, {
        headers,
      });
      if (!response.ok) {
        throw new Error("Failed to fetch activities by city");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  /**
   * Fetch filtered activities for a trip
   * @param tripId Trip ID
   * @param filters Filter options
   * @returns Array of filtered activities
   */
  async fetchFilteredActivities(
    tripId: string,
    filters: {
      day: number;
      city: string;
      status: "all" | "completed" | "pending";
    },
  ): Promise<any[]> {
    try {
      const headers = await this.setTokenInHeaders();
      const params = new URLSearchParams();

      if (filters.day !== undefined) {
        params.append("day", filters.day.toString());
      }
      if (filters.city !== undefined) {
        params.append("city", filters.city);
      }
      if (filters.status !== undefined) {
        params.append("status", filters.status);
      }

      const url = `${API_URL}/trips/${tripId}/activities/filtered?${params.toString()}`;

      const response = await fetch(url, {
        headers,
      });
      if (!response.ok) {
        throw new Error("Failed to fetch filtered activities");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async fetchTripById(id: string): Promise<DbTrip | null> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}`, {
        headers: await this.setTokenInHeaders(),
      });
      if (!response.ok) {
        throw new Error("Failed to fetch trip");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async createTrip(tripData: Omit<DbTrip, "_id">): Promise<DbTrip | null> {
    try {
      const response = await fetch(`${API_URL}/trips/`, {
        method: "POST",
        headers: await this.setTokenInHeaders(),
        body: JSON.stringify(tripData),
      });
      if (!response.ok) {
        throw new Error("Failed to create trip");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async updateTrip(
    id: string,
    tripData: Partial<DbTrip>,
  ): Promise<DbTrip | null> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}`, {
        method: "PUT",
        headers: await this.setTokenInHeaders(),
        body: JSON.stringify(tripData),
      });
      if (!response.ok) {
        throw new Error("Failed to update trip");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async deleteTrip(
    id: string,
  ): Promise<{ success: boolean; message: string; tripName?: string }> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}`, {
        method: "DELETE",
        headers: await this.setTokenInHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.message || "Failed to delete trip";
        throw new Error(errorMessage);
      }

      // Get the deleted trip data from the response
      const deletedTrip = await response.json();
      const tripName = deletedTrip?.name || "Trip";

      return {
        success: true,
        message: `${tripName} has been deleted successfully`,
        tripName,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      return {
        success: false,
        message: errorMessage,
      };
    }
  }

  async updateActivityCompletion(
    tripId: string,
    activityId: string,
    completed: boolean,
  ): Promise<DbTrip | null> {
    try {
      const response = await fetch(
        `${API_URL}/trips/${tripId}/activity/${activityId}/completion`,
        {
          method: "PATCH",
          headers: await this.setTokenInHeaders(),
          body: JSON.stringify({ completed }),
        },
      );
      if (!response.ok) {
        throw new Error("Failed to update activity completion status");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async toggleFavorite(tripId: string): Promise<DbTrip | null> {
    try {
      const response = await fetch(`${API_URL}/trips/${tripId}/favorite`, {
        method: "PATCH",
        headers: await this.setTokenInHeaders(),
      });
      if (!response.ok) {
        throw new Error("Failed to toggle favorite status");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async archiveTrip(
    id: string,
  ): Promise<{ success: boolean; message: string; tripName?: string }> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}/archive`, {
        method: "PATCH",
        headers: await this.setTokenInHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.message || "Failed to archive trip";
        throw new Error(errorMessage);
      }

      // Get the archived trip data from the response
      const archivedTrip = await response.json();
      const tripName = archivedTrip?.name || "Trip";

      return {
        success: true,
        message: `${tripName} has been archived successfully`,
        tripName,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      return {
        success: false,
        message: errorMessage,
      };
    }
  }

  async cancelGeneration(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}/cancel`, {
        method: "POST",
        headers: await this.setTokenInHeaders(),
      });
      if (!response.ok) {
        throw new Error("Failed to cancel generation");
      }
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async generateItineraryForDay(id: string, day: number): Promise<boolean> {
    try {
      const response = await fetch(
        `${API_URL}/trips/${id}/generate-itinerary?day=${day}`,
        {
          method: "PATCH",
          headers: await this.setTokenInHeaders(),
        },
      );
      if (!response.ok) {
        throw new Error("Failed to generate itinerary");
      }
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async getMissingDays(id: string): Promise<DayProgress[]> {
    try {
      const response = await fetch(`${API_URL}/trips/${id}/missing-days`, {
        headers: await this.setTokenInHeaders(),
      });
      if (!response.ok) {
        throw new Error("Failed to get missing days");
      }
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }

  async resumeGeneration(id: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${API_URL}/trips/${id}/continue-generation`,
        {
          method: "PATCH",
          headers: await this.setTokenInHeaders(),
        },
      );
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.message || "Failed to resume generation";

        // Create an error object with status code for proper error detection
        const error = new Error(errorMessage) as any;
        error.status = response.status;
        error.response = { status: response.status };

        throw error;
      }
      return true;
    } catch (error) {
      // Re-throw the error with status code preserved
      throw error;
    }
  }

  async generateTrip(params: any): Promise<DbTrip | null> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/trips/generate`, {
        method: "POST",
        body: JSON.stringify(params),
        headers,
      });
      const responseJSON = await response.json();
      if (!response.ok) {
        // Create an error object with status code for proper error detection
        const error = new Error(responseJSON.message || "Failed to generate trip") as any;
        error.status = response.status;
        error.response = { status: response.status };

        throw error;
      }

      return responseJSON;
    } catch (error) {
      // Re-throw the error with status code preserved
      throw error;
    }
  }

  /**
   * Mark or unmark a trip as an example
   * @param tripId Trip ID
   * @param isExample Whether to mark as example
   * @returns Updated trip
   */
  async updateExampleStatus(tripId: string, isExample: boolean): Promise<DbTrip> {
    try {
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/trips/${tripId}/example`, {
        method: "PATCH",
        body: JSON.stringify({ isExample }),
        headers,
      });

      if (!response.ok) {
        throw new Error("Failed to update example status");
      }

      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(errorMessage);
    }
  }
}
