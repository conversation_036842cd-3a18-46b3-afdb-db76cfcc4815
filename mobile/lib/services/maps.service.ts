import { Platform, Linking, Alert } from "react-native";
import { Activity } from "../types";

/**
 * Service for opening activities in native maps applications
 * Supports Google Maps (Android), Apple Maps (iOS), and fallback options
 */
export class MapsService {
  /**
   * Open an activity location in the device's default maps application
   * @param activity Activity object containing location and coordinates
   * @param preferredApp Optional preferred maps app ('google', 'apple', 'waze')
   */
  static async openInMaps(
    activity: Activity,
    preferredApp?: "google" | "apple" | "waze"
  ): Promise<void> {
    try {
      // Validate activity has location data
      if (!activity.coordinates && !activity.location) {
        Alert.alert(
          "Location Unavailable",
          "This activity doesn't have location information available."
        );
        return;
      }

      const { lat, lng } = activity.coordinates || { lat: null, lng: null };
      const locationName = activity.name || activity.location || "Location";
      const address = activity.location || "";

      // Build the maps URL based on platform and preference
      const mapsUrl = this.buildMapsUrl({
        lat,
        lng,
        locationName,
        address,
        preferredApp,
      });

      // Try to open the maps URL
      const canOpen = await Linking.canOpenURL(mapsUrl);

      if (canOpen) {
        await Linking.openURL(mapsUrl);
      } else {
        // Fallback to web-based maps if native app is not available
        await this.openWebMaps({ lat, lng, locationName, address });
      }
    } catch (error) {
      console.error("Error opening maps:", error);
      Alert.alert(
        "Error",
        "Unable to open maps application. Please try again."
      );
    }
  }

  /**
   * Build the appropriate maps URL based on platform and preferences
   */
  private static buildMapsUrl({
    lat,
    lng,
    locationName,
    address,
    preferredApp,
  }: {
    lat: number | null;
    lng: number | null;
    locationName: string;
    address: string;
    preferredApp?: "google" | "apple" | "waze";
  }): string {
    // If coordinates are available, use them for better accuracy
    if (lat && lng) {
      return this.buildCoordinateBasedUrl({
        lat,
        lng,
        locationName,
        preferredApp,
      });
    }

    // Fallback to address-based search
    return this.buildAddressBasedUrl({ address, locationName, preferredApp });
  }

  /**
   * Build URL using coordinates (more accurate)
   */
  private static buildCoordinateBasedUrl({
    lat,
    lng,
    locationName,
    preferredApp,
  }: {
    lat: number;
    lng: number;
    locationName: string;
    preferredApp?: "google" | "apple" | "waze";
  }): string {
    const encodedName = encodeURIComponent(locationName);

    // Handle specific app preferences
    if (preferredApp === "google") {
      return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=${encodedName}`;
    }

    if (preferredApp === "waze") {
      return `https://waze.com/ul?ll=${lat},${lng}&navigate=yes&zoom=17`;
    }

    if (preferredApp === "apple" && Platform.OS === "ios") {
      return `http://maps.apple.com/?q=${encodedName}&ll=${lat},${lng}`;
    }

    // Platform-specific defaults
    if (Platform.OS === "ios") {
      // iOS: Try Apple Maps first, then Google Maps
      return `http://maps.apple.com/?q=${encodedName}&ll=${lat},${lng}`;
    } else {
      // Android: Try Google Maps first
      return `geo:${lat},${lng}?q=${lat},${lng}(${encodedName})`;
    }
  }

  /**
   * Build URL using address search (fallback)
   */
  private static buildAddressBasedUrl({
    address,
    locationName,
    preferredApp,
  }: {
    address: string;
    locationName: string;
    preferredApp?: "google" | "apple" | "waze";
  }): string {
    const searchQuery = address || locationName;
    const encodedQuery = encodeURIComponent(searchQuery);

    // Handle specific app preferences
    if (preferredApp === "google") {
      return `https://www.google.com/maps/search/?api=1&query=${encodedQuery}`;
    }

    if (preferredApp === "waze") {
      return `https://waze.com/ul?q=${encodedQuery}&navigate=yes`;
    }

    if (preferredApp === "apple" && Platform.OS === "ios") {
      return `http://maps.apple.com/?q=${encodedQuery}`;
    }

    // Platform-specific defaults
    if (Platform.OS === "ios") {
      return `http://maps.apple.com/?q=${encodedQuery}`;
    } else {
      return `geo:0,0?q=${encodedQuery}`;
    }
  }

  /**
   * Fallback to web-based maps when native apps are not available
   */
  private static async openWebMaps({
    lat,
    lng,
    locationName,
    address,
  }: {
    lat: number | null;
    lng: number | null;
    locationName: string;
    address: string;
  }): Promise<void> {
    let webUrl: string;

    if (lat && lng) {
      webUrl = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
    } else {
      const searchQuery = address || locationName;
      const encodedQuery = encodeURIComponent(searchQuery);
      webUrl = `https://www.google.com/maps/search/?api=1&query=${encodedQuery}`;
    }

    try {
      await Linking.openURL(webUrl);
    } catch (error) {
      console.error("Error opening web maps:", error);
      Alert.alert(
        "Error",
        "Unable to open maps. Please check your internet connection."
      );
    }
  }

  /**
   * Get available maps applications on the device
   * @returns Promise<string[]> Array of available maps apps
   */
  static async getAvailableMapsApps(): Promise<string[]> {
    const availableApps: string[] = [];

    const appsToCheck = [
      { name: "google", url: "comgooglemaps://" },
      { name: "waze", url: "waze://" },
    ];

    // Add Apple Maps for iOS
    if (Platform.OS === "ios") {
      appsToCheck.push({ name: "apple", url: "http://maps.apple.com/" });
    }

    for (const app of appsToCheck) {
      try {
        const canOpen = await Linking.canOpenURL(app.url);
        if (canOpen) {
          availableApps.push(app.name);
        }
      } catch (error) {
        console.error(`Error checking ${app.name} maps availability:`, error);
      }
    }

    return availableApps;
  }

  /**
   * Show a selection dialog for choosing maps application
   * @param activity Activity to open in maps
   */
  static async showMapsAppSelector(activity: Activity): Promise<void> {
    try {
      const availableApps = await this.getAvailableMapsApps();

      if (availableApps.length === 0) {
        // No native apps available, use web fallback
        await this.openInMaps(activity);
        return;
      }

      if (availableApps.length === 1) {
        // Only one app available, use it directly
        await this.openInMaps(activity, availableApps[0] as any);
        return;
      }

      // Multiple apps available, show selection dialog
      const appNames = {
        google: "Google Maps",
        apple: "Apple Maps",
        waze: "Waze",
      };

      const buttons = availableApps.map((app) => ({
        text: appNames[app as keyof typeof appNames] || app,
        onPress: () => this.openInMaps(activity, app as any),
      }));

      buttons.push({
        text: "Cancel",
        onPress: () => Promise.resolve(),
      });

      Alert.alert(
        "Open in Maps",
        `Choose which app to open ${activity.name || "this location"} in:`,
        buttons
      );
    } catch (error) {
      console.error("Error showing maps app selector:", error);
      // Fallback to default behavior
      await this.openInMaps(activity);
    }
  }
}
