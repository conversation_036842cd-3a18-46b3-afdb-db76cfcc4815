import { io, Socket } from "socket.io-client";
import { API_URL } from "../constants";

export interface SocketConfig {
  auth?: {
    token?: string;
  };
  autoConnect?: boolean;
}

/**
 * Base Socket Service for handling WebSocket connections
 * Note: This service is used by TripSocketService for handling trip-related socket events
 */
export class SocketService {
  private static instance: SocketService;
  private socket: Socket | null = null;
  private joinedRooms: Set<string> = new Set();
  private constructor() {}

  static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  connect(config: SocketConfig = {}): Socket | null {
    if (!config.auth?.token) {
      return null;
    }

    try {
      if (!this.socket) {
        this.socket = io(API_URL, {
          transports: ["websocket"],
          auth: config.auth,
          autoConnect: config.autoConnect ?? true,
          reconnection: true,
          reconnectionAttempts: Infinity,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          timeout: 20000,
        });

        this.setupDefaultListeners();
      } else {
        this.socket.auth = config.auth;
        if (!this.socket.connected) {
          this.socket.disconnect().connect();
        }
      }
      return this.socket;
    } catch (error) {
      return null;
    }
  }

  private setupDefaultListeners(): void {
    if (!this.socket) return;

    this.socket.on("connect", () => {
      // Clear joined rooms on reconnect to ensure clean state
      const previousRooms = [...this.joinedRooms];
      this.joinedRooms.clear();

      // Rejoin rooms that were previously joined
      if (previousRooms.length > 0) {
        previousRooms.forEach((roomId) => {
          this.joinRoom(roomId);
        });
      }
    });

    this.socket.on("connection", (data) => {
      console.log("Received connection message:", data);
    });

    this.socket.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
      // Don't clear joined rooms on disconnect to remember which rooms to rejoin
    });

    this.socket.on("error", (error) => {
      console.error("Socket error:", error);
    });

    this.socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
    });

    this.socket.on("room-joined", (data) => {
      this.joinedRooms.add(data.roomId);
    });

    this.socket.on("room-left", (data) => {
      this.joinedRooms.delete(data.roomId);
    });

    // Debug logging is disabled to prevent excessive console output
    // To enable debug logging, uncomment the following code:
    /*
    if (this.socket && __DEV__) {
      this.socket.onAny((event, ...args) => {
        console.log(`Received event: ${event}`, args);
      });
    }
    */
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Join a room to receive events for that room
   * Used by TripSocketService to join trip rooms
   */
  joinRoom(roomId: string): void {
    if (!this.socket) {
      return;
    }

    if (!this.socket.connected) {
      return;
    }

    // Check if already joined to avoid duplicate joins
    if (this.joinedRooms.has(roomId)) {
      return;
    }

    // Add to joined rooms immediately to prevent duplicate join attempts
    this.joinedRooms.add(roomId);
    this.socket.emit("join-room", { roomId });
  }

  /**
   * Get the socket instance
   * Used by TripSocketService to access the socket instance
   */
  getSocket(): Socket | null {
    return this.socket;
  }

  /**
   * Check if already joined a room
   * Used by TripSocketService to check if already joined a trip room
   */
  hasJoined(roomId: string): boolean {
    return this.joinedRooms.has(roomId);
  }

  /**
   * Leave a room to stop receiving events for that room
   * Used by TripSocketService to leave trip rooms
   */
  leaveRoom(roomId: string): void {
    if (!this.socket?.connected) {
      // Remove from joined rooms anyway to maintain consistent state
      if (this.joinedRooms.has(roomId)) {
        this.joinedRooms.delete(roomId);
      }
      return;
    }

    // Check if actually joined before leaving
    if (!this.joinedRooms.has(roomId)) {
      return;
    }

    // Remove from joined rooms immediately to prevent duplicate leave attempts
    this.joinedRooms.delete(roomId);
    this.socket.emit("leave-room", { roomId });
  }

  /**
   * Subscribe to trip updates for a specific trip
   * @deprecated Use TripSocketService instead
   */
  subscribeToTripUpdates(
    tripId: string,
    callback: (data: any) => void,
  ): () => void {
    if (!this.socket) {
      return () => {};
    }

    const eventName = `trip-update-${tripId}`;
    this.socket.on(eventName, callback);

    return () => {
      this.socket?.off(eventName, callback);
    };
  }

  /**
   * Subscribe to stream chunks for a specific trip
   * Used by TripSocketService to receive stream updates
   */
  subscribeToStreamChunks(
    tripId: string,
    callback: (data: any) => void,
  ): () => void {
    if (!this.socket) {
      return () => {};
    }

    const eventName = `stream-chunk-${tripId}`;
    this.socket.on(eventName, callback);

    return () => {
      this.socket?.off(eventName, callback);
    };
  }

  /**
   * Subscribe to notifications
   * @deprecated Use TripSocketService for trip-related notifications
   */
  subscribeToNotifications(callback: (data: any) => void): () => void {
    if (!this.socket) {
      return () => {};
    }

    const eventName = `notification`;
    this.socket.on(eventName, callback);

    return () => {
      this.socket?.off(eventName, callback);
    };
  }

  // getSocket is already defined above

  updateAuth(token: string): void {
    if (this.socket) {
      this.socket.auth = { token };
      this.socket.disconnect().connect();
    }
  }
}
