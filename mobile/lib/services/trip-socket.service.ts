import { SocketService } from "./socket.service";
import { NotificationService } from "./notification.service";
import { TripStreamUpdateType } from "../types";
import {
  successHapticFeedback,
  errorHapticFeedback,
  lightHapticFeedback,
  warningHapticFeedback,
} from "../utils/haptics";

export enum ScreenType {
  TRIP_VIEW = "trip-view",
  PROCESSING = "processing",
  NONE = "none",
}

export interface ErrorContext {
  isInsufficientBalance: boolean;
  notificationType?: string;
  elapsedTime?: number;
  // Full payload from the socket message
  payload?: {
    type: string;
    content: string;
    progress?: number;
    elapsedTime?: number;
    notificationType?: string;
    // Additional fields that might be in the payload
    available?: number;
    needed?: number;
    [key: string]: any;
  };
}

export interface TripSocketHandlers {
  onInProgress?: (content: string, progress?: number) => void;
  onError?: (content: string, context?: ErrorContext) => void;
  onSuccess?: (content: string) => void;
}

export class TripSocketService {
  private static instance: TripSocketService;
  private socketService: SocketService;
  private notificationService: NotificationService;
  private currentScreen: ScreenType = ScreenType.NONE;
  private currentTripId: string | null = null;
  private currentUserId: string | null = null;
  private handlers: TripSocketHandlers = {};
  private notificationUnsubscribe: (() => void) | null = null;

  private constructor() {
    this.socketService = SocketService.getInstance();
    this.notificationService = NotificationService.getInstance();

    // Subscribe to direct notifications from the backend
    this.subscribeToDirectNotifications();
  }

  /**
   * Subscribe to direct notifications from the backend
   * These are sent to the user's room and include things like insufficient balance
   */
  private subscribeToDirectNotifications() {
    const socket = this.socketService.getSocket();
    if (!socket) {
      return;
    }

    // Unsubscribe from previous subscription if exists
    if (this.notificationUnsubscribe) {
      this.notificationUnsubscribe();
      this.notificationUnsubscribe = null;
    }

    // Handle direct notifications from the server
    const handleNotification = (notification: any) => {
      console.log("Received direct notification:", notification);

      if (!notification || typeof notification !== "object") {
        return;
      }

      const { title, body, type } = notification;

      // Show the notification
      this.notificationService.showNotification(title, body);

      // If this is an insufficient balance notification, also call the error handler
      if (type === "insufficient_balance" && this.handlers.onError) {
        // Create error context object for direct notifications with full payload
        const errorContext: ErrorContext = {
          isInsufficientBalance: true,
          notificationType: "insufficient_balance",
          payload: notification, // Include the full notification object
        };

        console.log(
          `Calling onError handler from direct notification: ${body}`,
          "Notification payload:",
          notification,
          "Error context:",
          errorContext,
        );
        this.handlers.onError(body, errorContext);
      }
    };

    // Subscribe to notifications
    // Note: Using the deprecated method but with a separate handler function
    // to make it easier to update when a new method is available
    this.notificationUnsubscribe =
      this.socketService.subscribeToNotifications(handleNotification);
  }

  static getInstance(): TripSocketService {
    if (!TripSocketService.instance) {
      TripSocketService.instance = new TripSocketService();
    }
    return TripSocketService.instance;
  }

  /**
   * Set the current active screen and trip ID
   * @param screen The current screen type
   * @param tripId The current trip ID
   * @param userId The current user ID
   */
  setCurrentScreen(
    screen: ScreenType,
    tripId: string | null = null,
    userId: string | null = null,
  ): void {
    this.currentScreen = screen;

    if (tripId) {
      this.currentTripId = tripId;
    }

    // Store userId for user-specific notifications
    if (userId) {
      this.currentUserId = userId;
    }
  }

  /**
   * Register handlers for trip socket events
   * @param handlers Object containing callback functions for different event types
   */
  registerHandlers(handlers: TripSocketHandlers): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  /**
   * Clear all registered handlers
   */
  clearHandlers(): void {
    this.handlers = {};
  }

  // Keep track of active subscriptions to prevent duplicates
  private activeSubscriptions: Map<string, boolean> = new Map();

  /**
   * Subscribe to trip stream updates for a specific trip
   * @param tripId The trip ID to subscribe to
   * @returns A function to unsubscribe
   */
  subscribeToTripUpdates(tripId: string): () => void {
    let roomId = tripId;
    if (!roomId) {
      roomId = "new-trip";
    }

    // Check if already subscribed to this trip
    if (this.activeSubscriptions.get(roomId)) {
      return () => {
        // Only perform unsubscribe actions if this is the last unsubscribe call
        this.activeSubscriptions.delete(roomId);

        // Get the socket instance to check connection status
        const socket = this.socketService.getSocket();
        if (socket?.connected && this.currentTripId === roomId) {
          this.socketService.leaveRoom(roomId);
          this.currentTripId = null;
        }
      };
    }

    // Mark as subscribed
    this.activeSubscriptions.set(roomId, true);
    // Get the socket instance to check connection status
    const socket = this.socketService.getSocket();
    if (!socket) {
      return () => {
        this.activeSubscriptions.delete(roomId);
      };
    }

    // Store the current trip ID
    this.currentTripId = roomId;

    // Only join the room if the socket is connected and we haven't already joined
    if (socket.connected) {
      if (!this.socketService.hasJoined(roomId)) {
        this.socketService.joinRoom(roomId);
      }
    } else {
      // Set up a one-time connect listener to join the room when connected
      socket.once("connect", () => {
        if (this.currentTripId === roomId) {
          this.socketService.joinRoom(roomId);
        }
      });
    }

    // Subscribe to stream chunks
    const unsubscribe = this.socketService.subscribeToStreamChunks(
      roomId,
      (data) => {
        this.handleStreamUpdate(data);
      },
    );

    return () => {
      unsubscribe();
      this.activeSubscriptions.delete(roomId);
      // Only leave the room if this is still the current trip
      if (socket.connected && this.currentTripId === roomId) {
        this.socketService.leaveRoom(roomId);
        this.currentTripId = null;
      }
    };
  }

  /**
   * Subscribe to user-specific updates
   * @param userId The user ID to subscribe to
   * @returns A function to unsubscribe
   */
  subscribeToUserUpdates(userId: string): () => void {
    const roomId = `user-${userId}`;

    // Check if already subscribed to this user room
    if (this.activeSubscriptions.get(roomId)) {
      return () => {
        // Only perform unsubscribe actions if this is the last unsubscribe call
        this.activeSubscriptions.delete(roomId);

        // Get the socket instance to check connection status
        const socket = this.socketService.getSocket();
        if (socket?.connected) {
          this.socketService.leaveRoom(roomId);
          if (this.currentUserId === userId) {
            this.currentUserId = null;
          }
        }
      };
    }

    // Mark as subscribed
    this.activeSubscriptions.set(roomId, true);

    // Get the socket instance to check connection status
    const socket = this.socketService.getSocket();
    if (!socket) {
      return () => {
        this.activeSubscriptions.delete(roomId);
      };
    }

    // Store the current user ID
    this.currentUserId = userId;

    // Only join the room if the socket is connected and we haven't already joined
    if (socket.connected) {
      if (!this.socketService.hasJoined(roomId)) {
        this.socketService.joinRoom(roomId);
      }
    } else {
      // Set up a one-time connect listener to join the room when connected
      socket.once("connect", () => {
        if (this.currentUserId === userId) {
          this.socketService.joinRoom(roomId);
        }
      });
    }

    // Subscribe to stream chunks for this user
    const unsubscribe = this.socketService.subscribeToStreamChunks(
      roomId,
      (data) => {
        this.handleStreamUpdate(data);
      },
    );

    return () => {
      unsubscribe();
      this.activeSubscriptions.delete(roomId);
      // Only leave the room if this is still the current user
      if (socket.connected) {
        this.socketService.leaveRoom(roomId);
        if (this.currentUserId === userId) {
          this.currentUserId = null;
        }
      }
    };
  }

  /**
   * Handle stream updates from the socket
   * @param data The update data from the socket
   */
  private handleStreamUpdate(data: any): void {
    // Validate data structure
    if (!data || typeof data !== "object") {
      console.warn("Invalid stream update data:", data);
      return;
    }

    const status = data.type as TripStreamUpdateType;
    if (!status) {
      console.warn("Missing status in stream update:", data);
      return;
    }

    const content = data.content || "";
    const progress = data.progress;
    const notificationType = data.notificationType; // Extract notification type from backend
    const elapsedTime = data.elapsedTime; // Extract elapsed time if available

    // Extract balance information if available
    const available = data.available;
    const needed = data.needed;

    console.log(
      `Received stream update: ${status}, notificationType: ${notificationType}`,
      {
        content,
        progress,
        elapsedTime,
        available,
        needed,
        fullPayload: data,
      },
    );

    // Show notification based on current screen and update type
    this.showNotificationIfNeeded(status, content);

    // Check if this is an insufficient balance error based on notification type
    const isInsufficientBalance =
      notificationType === "insufficient_balance" ||
      status === TripStreamUpdateType.INSUFFICIENT_BALANCE;

    // Handle based on update type
    switch (status) {
      case TripStreamUpdateType.IN_PROGRESS:
        // Trigger light haptic feedback for progress updates
        lightHapticFeedback();

        if (this.handlers.onInProgress) {
          this.handlers.onInProgress(content, progress);
        } else {
          console.warn("No onInProgress handler registered");
        }
        break;

      case TripStreamUpdateType.ERROR:
        // If this is an insufficient balance error, use warning feedback instead
        if (isInsufficientBalance) {
          warningHapticFeedback();
        } else {
          // Trigger error haptic feedback for regular errors
          errorHapticFeedback();
        }

        if (this.handlers.onError) {
          // Create error context object with full payload
          const errorContext: ErrorContext = {
            isInsufficientBalance,
            notificationType,
            elapsedTime,
            payload: data, // Include the full payload
          };

          // Log the error for debugging
          console.log(
            `Calling onError handler with: ${content}`,
            "Stream data:",
            data,
            "Error context:",
            errorContext,
          );

          // Call the error handler with the error message and context
          this.handlers.onError(content, errorContext);
        } else {
          console.warn("No onError handler registered");
        }
        break;

      case TripStreamUpdateType.READY:
        // Trigger success haptic feedback when ready
        successHapticFeedback();

        if (this.handlers.onSuccess) {
          this.handlers.onSuccess(content);
        } else {
          console.warn("No onSuccess handler registered");
        }
        break;

      case TripStreamUpdateType.INSUFFICIENT_BALANCE:
        // Trigger warning haptic feedback for insufficient balance
        warningHapticFeedback();

        // Call the error handler with the insufficient balance message
        if (this.handlers.onError) {
          // Create error context object with insufficient balance flag and full payload
          const errorContext: ErrorContext = {
            isInsufficientBalance: true,
            notificationType: "insufficient_balance",
            elapsedTime,
            payload: data, // Include the full payload
          };

          console.log(
            `Calling onError handler for insufficient balance: ${content}`,
            "Stream data:",
            data,
            "Error context:",
            errorContext,
          );
          this.handlers.onError(content, errorContext);
        } else {
          console.warn(
            "No onError handler registered for insufficient balance",
          );
        }
        break;

      default:
        console.warn("Unknown stream update type:", status);
        break;
    }
  }

  /**
   * Show a notification based on the update type and current screen
   * @param status The update status
   * @param content The update content
   */
  private showNotificationIfNeeded(
    status: TripStreamUpdateType,
    content: string,
  ): void {
    // For IN_PROGRESS state, only show notification if on a different screen than trip-view or processing
    if (
      status === TripStreamUpdateType.IN_PROGRESS &&
      this.currentScreen !== ScreenType.TRIP_VIEW &&
      this.currentScreen !== ScreenType.PROCESSING
    ) {
      this.notificationService.showNotification("Trip Update", content);
    }
  }
}
