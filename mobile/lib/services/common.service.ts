import { getPersisted } from "../hooks/use-storage";

export class CommonService {
  protected headers: {
    [key: string]: string;
  } = {
    "Content-Type": "application/json",
  };

  async setTokenInHeaders() {
    const token = await getPersisted("token");
    if (token) {
      this.headers["Authorization"] = `Bearer ${token}`;
    }
    return this.headers;
  }

  protected setHeaders(key: string, value: string) {
    this.headers[key] = value;
  }
}
