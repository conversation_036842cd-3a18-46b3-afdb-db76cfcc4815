import { API_URL } from "../constants";
import { CommonService } from "./common.service";

export interface NotificationPreferences {
  trip_updates: boolean;
  new_features: boolean;
  credit_balance: boolean;
  special_offers: boolean;
  travel_tips: boolean;
}

export class NotificationSettingsService extends CommonService {
  private static instance: NotificationSettingsService;

  private constructor() {
    super();
  }

  static getInstance(): NotificationSettingsService {
    if (!NotificationSettingsService.instance) {
      NotificationSettingsService.instance = new NotificationSettingsService();
    }
    return NotificationSettingsService.instance;
  }

  /**
   * Get user notification preferences
   * @returns User notification preferences
   */
  async getNotificationPreferences(): Promise<NotificationPreferences> {
    await this.setTokenInHeaders();

    try {
      const response = await fetch(`${API_URL}/user-preferences`, {
        method: "GET",
        headers: this.headers,
      });

      if (!response.ok) {
        throw new Error("Failed to get notification preferences");
      }

      const data = await response.json();
      return data.notifications;
    } catch (error) {
      console.error("Error getting notification preferences:", error);
      // Return default preferences if there's an error
      return {
        trip_updates: true,
        new_features: true,
        credit_balance: true,
        special_offers: false,
        travel_tips: true,
      };
    }
  }

  /**
   * Update notification preferences
   * @param preferences Notification preferences to update
   * @returns Updated notification preferences
   */
  async updateNotificationPreferences(
    preferences: Partial<NotificationPreferences>,
  ): Promise<NotificationPreferences> {
    await this.setTokenInHeaders();

    try {
      const response = await fetch(
        `${API_URL}/user-preferences/notifications`,
        {
          method: "PATCH",
          headers: this.headers,
          body: JSON.stringify(preferences),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to update notification preferences");
      }

      const data = await response.json();
      return data.notifications;
    } catch (error) {
      console.error("Error updating notification preferences:", error);
      throw error;
    }
  }
}
