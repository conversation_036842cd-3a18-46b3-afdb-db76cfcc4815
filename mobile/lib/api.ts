export interface GenerateActivitiesRequest {
  country: string;
  city: string;
  travelType:
  | "Budget"
  | "Luxury"
  | "Roadtrip"
  | "Family"
  | "Cultural"
  | "Adventure"
  | "Romantic"
  | "Business"
  | "Solo";
  intensity: number;
  additionalRequirements?: string;
}

export interface GenerateRestaurantsRequest {
  country: string;
  city: string;
  priceRange: "Budget" | "Moderate" | "Luxury";
  cuisinePreferences: string[];
  additionalRequirements?: string;
}

export interface GenerateHotelsRequest {
  country: string;
  city: string;
  priceRange: "Budget" | "Moderate" | "Luxury";
  starRating: number;
  amenities: string[];
  additionalRequirements?: string;
}