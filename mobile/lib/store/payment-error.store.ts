import { create } from 'zustand';

export interface PaymentError {
  id: string;
  message: string;
  type: 'error' | 'warning' | 'info' | 'success';
  timestamp: number;
}

interface PaymentErrorStore {
  errors: PaymentError[];
  showError: (message: string, type?: PaymentError['type']) => void;
  hideError: (id: string) => void;
  clearAllErrors: () => void;
}

/**
 * Payment error store for managing banner notifications
 * Replaces Alert dialogs with banner notifications for better UX
 */
export const usePaymentErrorStore = create<PaymentErrorStore>((set, get) => ({
  errors: [],

  showError: (message: string, type: PaymentError['type'] = 'error') => {
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const error: PaymentError = {
      id,
      message,
      type,
      timestamp: Date.now(),
    };

    set((state) => ({
      errors: [...state.errors, error],
    }));

    // Auto-hide success and info messages after 5 seconds
    if (type === 'success' || type === 'info') {
      setTimeout(() => {
        get().hideError(id);
      }, 5000);
    }
  },

  hideError: (id: string) => {
    set((state) => ({
      errors: state.errors.filter((error) => error.id !== id),
    }));
  },

  clearAllErrors: () => {
    set({ errors: [] });
  },
}));
