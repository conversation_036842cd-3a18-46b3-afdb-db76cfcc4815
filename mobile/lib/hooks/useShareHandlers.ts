import { useState } from "react";
import { Alert, Share } from "react-native";
import { DbTrip as Trip } from "../types";
import { getShareMessage } from "../utils/share-utils";

interface UseShareHandlersProps {
  trip: Trip | null;
  shareUrl: string;
}

type SocialPlatform =
  | "twitter"
  | "facebook"
  | "instagram"
  | "whatsapp"
  | "telegram";

/**
 * Hook for handling various sharing methods
 * @param trip Trip object
 * @param shareUrl Share URL
 * @returns Share handlers and state
 */
export const useShareHandlers = ({ trip, shareUrl }: UseShareHandlersProps) => {
  const [email, setEmail] = useState("");

  // Generic share handler
  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: getShareMessage(trip, shareUrl),
        url: shareUrl,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log(`Shared with ${result.activityType}`);
        } else {
          console.log("Shared successfully");
        }
      } else if (result.action === Share.dismissedAction) {
        console.log("Share dismissed");
      }
    } catch (error: any) {
      Alert.alert("Error Sharing", error.message);
    }
  };

  // Platform-specific share handlers
  const handleSocialShare = (platform: SocialPlatform) => {
    try {
      let url = "";
      const message = encodeURIComponent(getShareMessage(trip, shareUrl));

      switch (platform) {
        case "twitter":
          url = `https://twitter.com/intent/tweet?text=${message}`;
          break;
        case "facebook":
          url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${message}`;
          break;
        case "whatsapp":
          url = `whatsapp://send?text=${message}`;
          break;
        case "telegram":
          url = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${message}`;
          break;
        case "instagram":
          // Instagram doesn't support direct sharing via URL scheme
          // We'll use the native share dialog instead
          handleShare();
          return;
      }

      // On mobile, we can try to open the app directly
      // This is a simplified approach - in a real app, you'd use Linking.openURL
      // and handle the case where the app isn't installed
      if (url) {
        // In a real implementation, you would use Linking.openURL(url)
        // For now, we'll just show a success message
        Alert.alert("Success", `Sharing to ${platform}...`);
      }
    } catch (error: any) {
      Alert.alert("Error", `Could not share to ${platform}: ${error.message}`);
    }
  };

  // Share link only
  const handleShareLink = async () => {
    try {
      await Share.share({
        message: `Check out my trip: ${shareUrl}`,
        url: shareUrl,
      });
    } catch (error: any) {
      Alert.alert("Error", error.message);
    }
  };

  // Email sharing
  const handleEmailShare = () => {
    if (!email) {
      Alert.alert("Please enter an email address");
      return;
    }

    // In a real app, this would call an API to send the email
    // For now, we'll just show a success message
    Alert.alert("Success", "Itinerary has been sent to " + email);
    setEmail("");
  };

  return {
    email,
    setEmail,
    handleShare,
    handleSocialShare,
    handleShareLink,
    handleEmailShare,
  };
};
