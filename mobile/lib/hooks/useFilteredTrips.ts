import { useCallback, useEffect, useState } from "react";
import { TripService } from "../services/trip.service";
import { DbTrip } from "../types";

const tripService = TripService.getInstance();

export interface TripFilters {
  searchQuery?: string;
  favorite?: boolean;
  generating?: boolean;
  ready?: boolean;
  includeArchived?: boolean;
}

interface UseFilteredTripsProps {
  filters: TripFilters;
  enabled?: boolean;
}

/**
 * Hook for fetching filtered trips from the backend
 * @param filters Filter criteria
 * @param enabled Whether to enable the hook
 * @returns Filtered trips, loading state, error, and refresh function
 */
export function useFilteredTrips({
  filters,
  enabled = true,
}: UseFilteredTripsProps) {
  const [trips, setTrips] = useState<DbTrip[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadFilteredTrips = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      // Only include filters that are active
      const activeFilters: TripFilters = {};

      if (filters.searchQuery && filters.searchQuery.trim() !== "") {
        activeFilters.searchQuery = filters.searchQuery;
      }

      if (filters.favorite) {
        activeFilters.favorite = true;
      }

      if (filters.generating) {
        activeFilters.generating = true;
      }

      if (filters.ready) {
        activeFilters.ready = true;
      }

      if (filters.includeArchived) {
        activeFilters.includeArchived = true;
      }

      // If no filters are active, use the regular fetchTrips method
      let fetchedTrips: DbTrip[];

      if (Object.keys(activeFilters).length === 0) {
        fetchedTrips = await tripService.fetchTrips();
      } else {
        fetchedTrips = await tripService.fetchFilteredTrips(activeFilters);
      }

      setTrips(fetchedTrips);
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [
    enabled,
    filters.searchQuery,
    filters.favorite,
    filters.generating,
    filters.ready,
    filters.includeArchived,
  ]);

  // Load trips when filters change
  useEffect(() => {
    loadFilteredTrips();
  }, [loadFilteredTrips]);

  return {
    trips,
    loading,
    error,
    loadFilteredTrips,
  };
}
