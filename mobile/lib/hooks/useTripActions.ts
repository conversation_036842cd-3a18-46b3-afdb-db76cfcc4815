import { Alert } from "react-native";
import { router } from "expo-router";
import { useCallback } from "react";
import { impactAsync, ImpactFeedbackStyle } from "expo-haptics";
import { TripService } from "../services/trip.service";

/**
 * Custom hook for trip actions
 * Handles trip selection, deletion, and favorite toggling
 */
export const useTripActions = (loadTrips: () => Promise<void>) => {
  const tripService = TripService.getInstance();

  /**
   * Handle trip selection
   * @param viewMode View mode to use when navigating to trip view
   * @returns Callback function for trip selection
   */
  const handleTripSelect = useCallback(
    (viewMode: string) => (tripId: string) => {
      router.dismissTo({
        pathname: "/trip-view",
        params: { tripId, viewMode },
      });
      impactAsync(ImpactFeedbackStyle.Light);
    },
    [],
  );

  /**
   * Handle trip deletion
   * @param tripId ID of the trip to delete
   */
  const handleDeleteTrip = useCallback(
    async (tripId: string) => {
      Alert.alert("Delete Trip", "Are you sure you want to delete this trip?", [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const result = await tripService.archiveTrip(tripId);

              if (result.success) {
                // Show success alert
                Alert.alert("Success", "Trip deleted successfully", [
                  { text: "OK" },
                ]);
                await loadTrips();
                impactAsync(ImpactFeedbackStyle.Light);
              } else {
                // Show error alert
                Alert.alert(
                  "Error Deleting Trip",
                  result.message || "Failed to delete trip",
                  [{ text: "OK" }],
                );
              }
            } catch (error) {
              // Handle unexpected errors
              const errorMessage =
                error instanceof Error
                  ? error.message
                  : "An unexpected error occurred";
              Alert.alert("Error Deleting Trip", errorMessage, [
                { text: "OK" },
              ]);
            }
          },
        },
      ]);
    },
    [loadTrips],
  );

  /**
   * Handle toggling trip favorite status
   * @param tripId ID of the trip to toggle favorite status
   */
  const handleToggleFavorite = useCallback(
    async (tripId: string) => {
      try {
        await tripService.toggleFavorite(tripId);
        await loadTrips();
        impactAsync(ImpactFeedbackStyle.Light);
      } catch (error) {
        console.error("Error toggling favorite:", error);
      }
    },
    [loadTrips],
  );

  /**
   * Navigate to trip creation
   */
  const navigateToTripCreation = useCallback(() => {
    router.push("/trip-details");
  }, []);

  return {
    handleTripSelect,
    handleDeleteTrip,
    handleToggleFavorite,
    navigateToTripCreation,
  };
};
