import { useEffect } from "react";
import {
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
} from "react-native-reanimated";
import { ANIMATION_CONFIG } from "../constants/processing-constants";

/**
 * Hook for confetti animation
 * @param visible Whether the confetti is visible
 * @returns Animation values
 */
export const useConfettiAnimation = (visible: boolean) => {
  const confettiY = Array(10)
    .fill(0)
    .map(() => useSharedValue(-20));
  const confettiX = Array(10)
    .fill(0)
    .map(() => useSharedValue(0));
  const confettiRotation = Array(10)
    .fill(0)
    .map(() => useSharedValue(0));
  const confettiScale = Array(10)
    .fill(0)
    .map(() => useSharedValue(0));

  useEffect(() => {
    if (visible) {
      // Animate confetti
      confettiY.forEach((y, i) => {
        // Random starting position
        const startX = Math.random() * 300 - 150;
        confettiX[i].value = startX;

        // Random scale
        confettiScale[i].value = 0.5 + Math.random() * 1.5;

        // Animate falling with some delay based on index
        y.value = withDelay(
          i * 100,
          withTiming(500, {
            duration:
              ANIMATION_CONFIG.CONFETTI_BASE_DURATION + Math.random() * 1000,
          }),
        );

        // Animate rotation
        confettiRotation[i].value = withRepeat(
          withTiming(360, {
            duration:
              ANIMATION_CONFIG.CONFETTI_BASE_DURATION / 2 +
              Math.random() * 1000,
          }),
          -1,
        );
      });
    } else {
      // Reset confetti
      confettiY.forEach((y, i) => {
        y.value = -20;
        confettiScale[i].value = 0;
      });
    }
  }, [visible]);

  return { confettiY, confettiX, confettiRotation, confettiScale };
};
