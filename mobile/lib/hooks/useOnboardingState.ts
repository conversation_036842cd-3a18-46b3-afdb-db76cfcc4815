import { useState, useCallback, useEffect } from "react";
import { TripDetails } from "../types";
import { useTripStore } from "../store";
import { useRouter } from "expo-router";
import { Alert } from "react-native";
import { calculateDays } from "../utils/date";
import { TripService } from "../services/trip.service";
import { validateTripDuration, calculateTripDuration } from "../utils/date-constraints";

// Default values for the onboarding form
const DEFAULT_FORM_DATA: TripDetails = {
  destination: "Vietnam",
  arrivalCity: "Hanoi",
  departureCity: "Ho Chi Minh City",
  arrivalMode: "Air",
  departureMode: "Air",
  startDate: new Date().toISOString(),
  endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
  arrivalTime: "10:00",
  departureTime: "16:00",
  wakeUpTime: "07:00",
  sleepTime: "23:00",
  travelType: "Adventure",
  budget: "normal",
  intensity: 8,
  people: {
    adults: 1,
    children: 0,
  },
  cuisinePreferences: ["No Preference"],
  interests: [],
  mustVisitCities: [],
  additionalRequirements: "",
  totalDays: 14,
};

export const useOnboardingState = () => {
  const router = useRouter();
  const { setTripDetails } = useTripStore();
  const [isLoading, setLoading] = useState(false);
  const tripService = TripService.getInstance();

  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<TripDetails>(DEFAULT_FORM_DATA);

  // Clear any existing trip details from store when starting onboarding
  useEffect(() => {
    setTripDetails(DEFAULT_FORM_DATA);
  }, [setTripDetails]);

  const STEP_TITLES = [
    "Location",
    "Dates",
    "Travel Style",
    "Cuisine",
    "Interests",
  ];

  const updateFormData = useCallback((updates: Partial<TripDetails>) => {
    setFormData((prev) => ({ ...prev, ...updates }));
  }, []);

  const nextStep = useCallback(() => {
    if (currentStep < STEP_TITLES.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep, STEP_TITLES.length]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  const validateStep = useCallback(
    (step: number): boolean => {
      switch (step) {
        case 0: // Location
          return (
            !!formData.destination &&
            !!formData.arrivalCity &&
            !!formData.departureCity &&
            !!formData.arrivalMode &&
            !!formData.departureMode
          );
        case 1: // Dates
          const tripValidation = validateTripDuration(formData.startDate, formData.endDate, null, 'onboarding');
          return (
            !!formData.startDate &&
            !!formData.endDate &&
            !!formData.arrivalTime &&
            !!formData.departureTime &&
            !!formData.wakeUpTime &&
            !!formData.sleepTime &&
            calculateDays(formData.startDate, formData.endDate) > 0 &&
            tripValidation.isValid
          );
        case 2: // Travel Style
          return (
            !!formData.travelType &&
            !!formData.budget &&
            formData.intensity >= 0 &&
            formData.intensity <= 10 &&
            formData.people.adults > 0
          );
        case 3: // Cuisine
          return (
            Array.isArray(formData.cuisinePreferences) &&
            formData.cuisinePreferences.length > 0
          );
        case 4: // Interests
          return (
            Array.isArray(formData.interests) && formData.interests.length > 0
          );
        default:
          return true;
      }
    },
    [formData],
  );

  const handleSubmit = useCallback(async () => {
    try {
      setLoading(true);

      // Calculate trip duration (inclusive of both start and end dates)
      const totalDays = calculateTripDuration(formData.startDate, formData.endDate);

      // Update trip details in the store
      setTripDetails({
        ...formData,
        totalDays,
      });
      router.push(`/trip-confirmation`);
    } catch (error) {
      console.error("Error creating trip:", error);
      Alert.alert("Error", "Failed to create trip. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [formData, tripService, router, setLoading, setTripDetails]);

  return {
    currentStep,
    formData,
    stepTitles: STEP_TITLES,
    isLastStep: currentStep === STEP_TITLES.length - 1,
    isLoading,
    updateFormData,
    nextStep,
    prevStep,
    validateStep,
    handleSubmit,
  };
};

export default useOnboardingState;
