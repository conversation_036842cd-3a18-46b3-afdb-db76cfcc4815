import { useEffect, useState } from "react";
import { MapRegionService } from "../services/map-region.service";

// Import Region type from react-native-maps for type definitions
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};
const mapRegionService = MapRegionService.getInstance();

/**
 * Custom hook for managing map region
 * Handles map region state and country selection
 */
export const useMapRegion = (selectedCountry: string | null) => {
  // Default to world view
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: 30,
    longitude: 0,
    latitudeDelta: 180,
    longitudeDelta: 180,
  });

  // Fetch map region for a country when selected
  useEffect(() => {
    const fetchCountryMapRegion = async () => {
      if (selectedCountry && selectedCountry !== "Unknown") {
        try {
          const region =
            await mapRegionService.getCountryMapRegion(selectedCountry);
          if (region) {
            setMapRegion(region);
          }
        } catch (error) {
          console.error("Error fetching country map region:", error);
        }
      }
    };

    fetchCountryMapRegion();
  }, [selectedCountry]);

  return {
    mapRegion,
    setMapRegion,
  };
};
