import { useCallback, useEffect, useState } from "react";
import { Alert } from "react-native";
import { useItineraryGeneration } from "./useItineraryGeneration";
import { useTripStore } from "../store";
import { useTripSocket } from "./use-trip-socket";
import { ScreenType } from "../services/trip-socket.service";
import { useGlobalInsufficientDays } from "./useGlobalInsufficientDays";

/**
 * Hook for trip generation
 * @returns Trip generation state and functions
 */
export const useTripGeneration = () => {
  const { tripDetails, reset } = useTripStore();
  const { handleInsufficientBalanceError: handleGlobalInsufficientBalance } = useGlobalInsufficientDays();

  const {
    generateItinerary,
    status: itineraryStatus,
    isLoading,
    tripId,
    done: initialDone,
    error: initialError,
    showDaysQuotaModal: initialShowDaysQuotaModal,
    setShowDaysQuotaModal: setInitialShowDaysQuotaModal,
    requiredDays: initialRequiredDays,
    balance,
    fetchBalance,
  } = useItineraryGeneration();

  // Add local state to ensure we can update from socket events
  const [error, setError] = useState<string | null>(initialError);
  const [done, setDone] = useState<boolean>(initialDone);
  const [status, setItineraryStatus] = useState<string | null>(itineraryStatus);



  // Use the global insufficient balance handler
  const handleInsufficientBalanceError = useCallback(
    (errorMessage: string, context?: any) => {
      console.info(
        "Delegating insufficient balance error to global handler from processing screen:",
        { errorMessage, context },
      );

      // Use the global handler (async)
      handleGlobalInsufficientBalance(errorMessage, context).catch((error) => {
        console.error("Error in global insufficient balance handler:", error);
      });

      // Set error message for display in processing screen
      setError(errorMessage.split('.')[0]);
    },
    [handleGlobalInsufficientBalance],
  );

  // Connect to socket for additional updates
  // Always subscribe to user room and new-trip room when there's no tripId
  useTripSocket({
    tripId: tripId || "new-trip", // Use "new-trip" as room when no tripId
    screenType: ScreenType.PROCESSING,
    subscribeToUserRoom: true, // Always subscribe to user room for balance notifications
    handlers: {
      onInProgress: useCallback((content: string) => {
        if (content) {
          // Update the status text
          itineraryStatus !== content && setItineraryStatus(content);
        }
      }, [itineraryStatus]),
      onError: useCallback((content: string, context?: any) => {
        if (content) {
          // Only handle insufficient balance errors if context confirms it
          if (context?.isInsufficientBalance) {
            console.info(
              "Detected insufficient balance error from socket with context in processing screen",
            );
            // Pass both content and context to the handler
            handleInsufficientBalanceError(content, context);
          } else {
            console.info(
              "Received general error in processing screen (not insufficient balance):",
              { content, context }
            );
          }

          // Update the error text
          error !== content && setError(content);
        }
      }, [error, handleInsufficientBalanceError]),
      onSuccess: useCallback(() => {
        // Mark as done
        !done && setDone(true);
      }, [done]),
    },
  });

  // Sync local state with values from useItineraryGeneration
  useEffect(() => {
    if (initialError !== error) {
      setError(initialError);
    }

    if (initialDone !== done) {
      setDone(initialDone);
    }

    if (itineraryStatus !== status) {
      setItineraryStatus(itineraryStatus);
    }
  }, [initialError, initialDone, itineraryStatus, balance]);

  useEffect(() => {
    if (tripId) {
      // Clear all interests from the store
      reset();
    }
  }, [tripId]);

  // Prepare trip generation parameters
  const getTripGenerationParams = () => ({
    destination: tripDetails.destination,
    arrivalCity: tripDetails.arrivalCity || tripDetails.destination,
    departureCity: tripDetails.departureCity || tripDetails.destination,
    startDate: tripDetails.startDate,
    endDate: tripDetails.endDate,
    arrivalTime: tripDetails.arrivalTime,
    departureTime: tripDetails.departureTime,
    arrivalMode: tripDetails.arrivalMode,
    departureMode: tripDetails.departureMode,
    tripType: tripDetails.travelType,
    budget: tripDetails.budget,
    intensity: tripDetails.intensity,
    interests: tripDetails.interests,
    cuisinePreferences: tripDetails.cuisinePreferences || [
      "Local",
      "Traditional",
      "International",
    ],
    wakeUpTime: tripDetails.wakeUpTime,
    sleepTime: tripDetails.sleepTime,
    adults: tripDetails.people.adults,
    children: tripDetails.people.children,
    mustVisitCities: tripDetails.mustVisitCities,
    additionalRequirements: tripDetails.additionalRequirements,
  });

  console.log("Trip generation params:", getTripGenerationParams());

  const processItinerary = async () => {
    if (!tripDetails.interests || tripDetails.interests.length === 0) {
      Alert.alert(
        "No interests selected. Please go back and select your interests.",
      );
      return;
    }

    // Clear error state before starting new generation attempt
    setError(null);
    setDone(false);
    setItineraryStatus(null);

    try {
      // Socket errors will be handled by the socket handlers
      // which are prioritized over the catch block
      await generateItinerary(getTripGenerationParams());
    } catch (err) {
      // This is a fallback for errors that weren't caught by the socket
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.log("Error generating itinerary (catch block):", errorMessage);
      // Insufficient credits errors are now handled in useItineraryGeneration
    }
  };

  return {
    status,
    isLoading,
    tripId,
    error,
    done,
    tripDetails,
    showDaysQuotaModal: initialShowDaysQuotaModal,
    setShowDaysQuotaModal: setInitialShowDaysQuotaModal,
    requiredDays: initialRequiredDays,
    balance,
    fetchBalance,
    processItinerary,
  };
};
