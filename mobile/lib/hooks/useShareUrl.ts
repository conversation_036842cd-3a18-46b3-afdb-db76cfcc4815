import { useEffect, useState } from "react";
import { DbTrip as Trip } from "../types";

interface UseShareUrlProps {
  trip: Trip | null;
  tripId: string | undefined;
}

/**
 * Hook for generating and managing share URL
 * @param trip Trip object
 * @param tripId Trip ID
 * @returns Share URL
 */
export const useShareUrl = ({ trip, tripId }: UseShareUrlProps) => {
  const [shareUrl, setShareUrl] = useState("");

  // Generate share URL when trip data is loaded
  useEffect(() => {
    if (trip && tripId) {
      // In a real app, this would be a deep link to the trip
      setShareUrl(`https://itrip.app/itinerary/${tripId}`);
    }
  }, [trip, tripId]);

  return shareUrl;
};
