import { useCallback, useEffect, useState } from "react";
import {
  DaysBalance,
  DaysBalanceService,
  DaysTransaction,
  PaginatedResponse,
} from "../services/days-balance.service";
import { useAuth } from "./use-auth";
import { useRefreshOnFocus } from "./use-refresh-on-focus";

const daysBalanceService = DaysBalanceService.getInstance();

export function useDaysBalance() {
  const { auth } = useAuth();
  const [balance, setBalance] = useState<DaysBalance | null>(null);
  const [transactions, setTransactions] = useState<DaysTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<
    PaginatedResponse<DaysTransaction>["pagination"]
  >({
    totalItems: 0,
    totalPages: 0,
    currentPage: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  const fetchBalance = useCallback(async () => {
    if (auth.status !== "authenticated") {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await daysBalanceService.getBalance();
      setBalance(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch balance");
      console.error("Error fetching days balance:", err);
    } finally {
      setIsLoading(false);
    }
  }, [auth.status]);

  const fetchTransactions = useCallback(
    async (limit: number = 20, page: number = 1, reset: boolean = false) => {
      if (auth.status !== "authenticated") {
        return {
          data: [],
          pagination: {
            totalItems: 0,
            totalPages: 0,
            currentPage: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await daysBalanceService.getTransactionHistory(
          limit,
          page,
        );

        // If reset is true or it's the first page, replace transactions
        // Otherwise append the new transactions to the existing ones
        setTransactions((prevTransactions) =>
          reset || page === 1
            ? response.data
            : [...prevTransactions, ...response.data],
        );

        // Update pagination state
        setPagination(response.pagination);

        return response;
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch transactions",
        );
        console.error("Error fetching transactions:", err);
        return {
          data: [],
          pagination: {
            totalItems: 0,
            totalPages: 0,
            currentPage: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      } finally {
        setIsLoading(false);
      }
    },
    [auth.status],
  );

  const addDays = useCallback(
    async (amount: number, description?: string) => {
      if (auth.status !== "authenticated") {
        return null;
      }

      setIsLoading(true);
      setError(null);

      try {
        const updatedBalance = await daysBalanceService.addDays(
          amount,
          description,
        );
        setBalance(updatedBalance);
        return updatedBalance;
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to add days");
        console.error("Error adding days:", err);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [auth.status],
  );

  // Get total available days
  const getTotalAvailableDays = useCallback(() => {
    if (!balance) return 0;
    return daysBalanceService.getTotalAvailableDays(balance);
  }, [balance]);

  // Fetch balance on initial load
  useEffect(() => {
    if (auth.status === "authenticated") {
      fetchBalance();
    }
  }, [auth.status, fetchBalance]);

  // Refresh data when the screen comes into focus
  useRefreshOnFocus(fetchBalance);

  return {
    balance,
    transactions,
    pagination,
    isLoading,
    error,
    fetchBalance,
    fetchTransactions,
    addDays,
    getTotalAvailableDays,
  };
}
