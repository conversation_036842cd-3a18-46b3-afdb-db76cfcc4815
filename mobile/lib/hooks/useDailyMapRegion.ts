import { useCallback, useEffect, useState } from "react";
import { MapRegionService } from "../services/map-region.service";

// Import Region type from react-native-maps for type definitions
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};

interface UseDailyMapRegionProps {
  tripId: string | null;
  day: string | number | null;
  destination?: string;
}

/**
 * Hook for managing daily itinerary map region
 * @param tripId Trip ID
 * @param day Day number
 * @param destination Trip destination
 * @returns Map region and related functions
 */
export const useDailyMapRegion = ({
  tripId,
  day,
  destination,
}: UseDailyMapRegionProps) => {
  const [mapRegion, setMapRegion] = useState<Region | null>(null);
  const mapRegionService = MapRegionService.getInstance();

  // Initialize map region from backend
  useEffect(() => {
    const fetchMapRegion = async () => {
      if (!tripId || !day) return;

      const dayNumber = typeof day === "string" ? parseInt(day, 10) : day;
      if (isNaN(dayNumber)) return;

      try {
        // First try to get day-specific map region
        const region = await mapRegionService.getDailyMapRegion(
          tripId,
          dayNumber,
        );
        if (region) {
          setMapRegion(region);
          return;
        }

        // If day region fails, try to get trip-level map region
        const tripRegion = await mapRegionService.getTripMapRegion(tripId);
        if (tripRegion) {
          setMapRegion(tripRegion);
          return;
        }

        // If trip region fails, try to get country-level map region
        if (destination) {
          const countryRegion =
            await mapRegionService.getCountryMapRegion(destination);
          if (countryRegion) {
            setMapRegion(countryRegion);
            return;
          }
        }

        // Fallback to default region if all attempts fail
        setMapRegion({
          latitude: 30,
          longitude: 0,
          latitudeDelta: 180,
          longitudeDelta: 180,
        });
      } catch (error) {
        console.error("Error fetching map region:", error);
        // Fallback to default region on error
        setMapRegion({
          latitude: 30,
          longitude: 0,
          latitudeDelta: 180,
          longitudeDelta: 180,
        });
      }
    };

    fetchMapRegion();
  }, [tripId, day, destination]);

  // Handle map region change with optimization
  const handleRegionChangeComplete = useCallback((region: Region) => {
    setMapRegion((prev) => {
      if (
        !prev ||
        prev.latitude !== region.latitude ||
        prev.longitude !== region.longitude ||
        prev.latitudeDelta !== region.latitudeDelta ||
        prev.longitudeDelta !== region.longitudeDelta
      ) {
        return region;
      }
      return prev;
    });
  }, []);

  return {
    mapRegion,
    setMapRegion,
    handleRegionChangeComplete,
  };
};
