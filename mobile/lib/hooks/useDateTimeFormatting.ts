/**
 * Custom hook for date and time formatting
 */
export const useDateTimeFormatting = () => {
  /**
   * Format time to HH:MM format
   * @param date Date object
   * @returns Time string in HH:MM format
   */
  const formatTimeToHHMM = (date: Date): string => {
    return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  };

  /**
   * Format date to YYYY-MM-DD format
   * @param date Date object
   * @returns Date string in YYYY-MM-DD format
   */
  const formatDateToYYYYMMDD = (date: Date): string => {
    return date.toISOString().split("T")[0];
  };

  /**
   * Format date to human-readable format
   * @param date Date object or ISO string
   * @returns Date string in human-readable format
   */
  const formatDateToHuman = (date: Date | string): string => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  /**
   * Convert time string to Date object
   * @param timeString Time string in HH:MM format
   * @returns Date object with the specified time
   */
  const getTimeAsDate = (timeString: string): Date => {
    const date = new Date();
    const [hours, minutes] = timeString.split(":").map(Number);
    if (!isNaN(hours) && !isNaN(minutes)) {
      date.setHours(hours, minutes, 0, 0);
    }
    return date;
  };

  /**
   * Calculate the number of days between two dates (inclusive)
   * @param startDate Start date
   * @param endDate End date
   * @returns Number of days (inclusive of both start and end dates)
   */
  const calculateDays = (startDate: Date, endDate: Date): number => {
    // Set both dates to midnight UTC to avoid timezone issues
    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setUTCHours(0, 0, 0, 0);
    end.setUTCHours(0, 0, 0, 0);

    // Calculate difference in days and add 1 to include both start and end dates
    const diffInMs = end.getTime() - start.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    // Add 1 to include both first and last day, ensure minimum of 1 day
    return Math.max(1, diffInDays + 1);
  };

  return {
    formatTimeToHHMM,
    formatDateToYYYYMMDD,
    formatDateToHuman,
    getTimeAsDate,
    calculateDays,
  };
};
