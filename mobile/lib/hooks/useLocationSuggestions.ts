import { useCallback, useRef, useState } from "react";
import {
  getCitySuggestions,
  getCountrySuggestions,
  validateLocation,
} from "../utils/locationSuggestions";

export interface LocationSuggestion {
  name: string;
  country?: string;
  type: "country" | "city";
  coordinates?: {
    lat: number;
    lng: number;
  };
}

interface UseLocationSuggestionsProps {
  onError?: (error: string) => void;
  debounceMs?: number;
}

interface UseLocationSuggestionsResult {
  isLoading: boolean;
  error: string | null;
  suggestions: LocationSuggestion[];
  searchCountries: (query: string) => Promise<void>;
  searchCities: (query: string, country: string) => Promise<void>;
  validateLocation: (
    name: string,
    type: "country" | "city",
  ) => Promise<LocationSuggestion | null>;
  clearSuggestions: () => void;
  setError: (error: string | null) => void;
}

export function useLocationSuggestions({
  onError,
  debounceMs = 300,
}: UseLocationSuggestionsProps = {}): UseLocationSuggestionsResult {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);

  // Refs for debouncing and cancellation
  const debounceTimeout = useRef<NodeJS.Timeout | undefined>(undefined);
  const abortController = useRef<AbortController>(new AbortController());

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    onError?.(errorMessage);
  };

  const cancelPreviousRequest = () => {
    // Always abort the current controller and create a new one
    abortController.current.abort();
    abortController.current = new AbortController();

    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
  };

  const debouncedSearch = useCallback(
    (searchFn: () => Promise<void>) => {
      cancelPreviousRequest();

      return new Promise<void>((resolve) => {
        debounceTimeout.current = setTimeout(() => {
          searchFn().finally(resolve);
        }, debounceMs);
      });
    },
    [debounceMs],
  );

  const searchCountries = async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    await debouncedSearch(async () => {
      setIsLoading(true);
      setError(null);

      try {
        const results = await getCountrySuggestions(
          query,
          abortController.current.signal,
        );
        setSuggestions(results);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          // Ignore abort errors
          return;
        }
        const errorMessage =
          error instanceof Error ? error.message : "Error searching countries";
        handleError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  };

  const searchCities = async (query: string, country: string) => {
    if (!country || country.length < 2) {
      setSuggestions([]);
      return;
    }

    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    await debouncedSearch(async () => {
      setIsLoading(true);
      setError(null);

      try {
        const results = await getCitySuggestions(
          query,
          country,
          abortController.current.signal,
        );
        setSuggestions(results);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          // Ignore abort errors
          return;
        }
        const errorMessage =
          error instanceof Error ? error.message : "Error searching cities";
        handleError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  };

  const validateLocationName = async (
    name: string,
    type: "country" | "city",
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // Cancel any previous request and get a fresh AbortController
      cancelPreviousRequest();
      const result = await validateLocation(
        name,
        type,
        abortController.current.signal,
      );
      return result;
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        return null;
      }
      const errorMessage =
        error instanceof Error ? error.message : "Error validating location";
      handleError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const clearSuggestions = () => {
    cancelPreviousRequest();
    setSuggestions([]);
  };

  return {
    isLoading,
    error,
    suggestions,
    searchCountries,
    searchCities,
    validateLocation: validateLocationName,
    clearSuggestions,
    setError,
  };
}
