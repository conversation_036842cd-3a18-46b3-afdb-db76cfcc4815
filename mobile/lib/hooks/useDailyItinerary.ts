import { useMemo } from "react";
import { Activity } from "../types";

interface UseDailyItineraryProps {
  day: string | number | null;
  itinerary?: { day: number; activities: Activity[] }[];
}

/**
 * Hook for managing daily itinerary data
 * @param day Day number
 * @param itinerary Full trip itinerary
 * @returns Activities for the selected day
 */
export const useDailyItinerary = ({
  day,
  itinerary,
}: UseDailyItineraryProps) => {
  // Get the current day's activities
  const activities = useMemo(() => {
    if (!day) {
      return [];
    }

    // Get the day number from the parameter
    const dayNumber = typeof day === "string" ? Number(day) : day;
    const dayItinerary = itinerary?.find(
      (itinerary) => itinerary.day === dayNumber,
    );

    if (!dayItinerary) {
      return [];
    }

    // Return activities
    return dayItinerary.activities;
  }, [day, itinerary]);

  return {
    activities,
    dayNumber: typeof day === "string" ? Number(day) : day,
  };
};
