import { useEffect, useState, useCallback } from "react";
import { Socket } from "socket.io-client";
import { SocketConfig, SocketService } from "../services/socket.service";
const socketService = SocketService.getInstance();

export function useSocket(config: SocketConfig = {}) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Reconnect function
  const reconnect = useCallback(() => {
    if (socket) {
      socket.connect();
    }
  }, [socket]);

  useEffect(() => {
    const socketInstance = socketService.connect(config);

    if (!socketInstance) {
      return;
    }

    socketInstance.on("connect", () => {
      setIsConnected(true);
      setError(null);
    });

    socketInstance.on("disconnect", (reason) => {
      setIsConnected(false);
    });

    socketInstance.on("connect_error", (err) => {
      setError(err);
      setIsConnected(false);
    });

    // Additional event listeners for debugging
    socketInstance.on("error", (err) => {
      console.error("Socket error:", err);
    });

    socketInstance.on("reconnect", (attemptNumber) => {
      console.log(`Socket reconnected after ${attemptNumber} attempts`);
    });

    socketInstance.on("reconnect_attempt", (attemptNumber) => {
      console.log(`Socket reconnect attempt #${attemptNumber}`);
    });

    socketInstance.on("reconnect_error", (err) => {
      console.error("Socket reconnection error:", err);
    });

    socketInstance.on("reconnect_failed", () => {
      console.error("Socket reconnection failed");
    });

    // Check initial connection status
    if (socketInstance.connected) {
      setIsConnected(true);
    }

    setSocket(socketInstance);

    return () => {
      socketInstance.off("connect");
      socketInstance.off("disconnect");
      socketInstance.off("connect_error");
      socketInstance.off("error");
      socketInstance.off("reconnect");
      socketInstance.off("reconnect_attempt");
      socketInstance.off("reconnect_error");
      socketInstance.off("reconnect_failed");
    };
  }, [config]);

  return { socket, isConnected, error, reconnect };
}
