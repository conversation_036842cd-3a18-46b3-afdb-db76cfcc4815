import { useEffect, useState } from "react";
import {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import {
  ANIMATION_CONFIG,
  TRANSPORT_ICONS,
} from "../constants/processing-constants";

/**
 * Hook for transport animation
 * @returns Animation values and styles
 */
export const useTransportAnimation = () => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const [currentIcon, setCurrentIcon] = useState(0);

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, { duration: ANIMATION_CONFIG.ROTATION_DURATION }),
      -1,
    );

    const interval = setInterval(() => {
      scale.value = withSequence(
        withTiming(0.8, { duration: 200 }),
        withSpring(1),
      );
      setCurrentIcon((prev) => (prev + 1) % TRANSPORT_ICONS.length);
    }, ANIMATION_CONFIG.ICON_CHANGE_INTERVAL);

    return () => clearInterval(interval);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotateZ: rotation.value + "deg" }, { scale: scale.value }],
  }));

  return { animatedStyle, currentIcon };
};
