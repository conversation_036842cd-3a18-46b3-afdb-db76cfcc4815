import { useState, useCallback } from "react";
import { useDaysBalance } from "./use-days-balance";
import { useGlobalInsufficientDays } from "./useGlobalInsufficientDays";
import {
  extractRequiredDays,
  isInsufficientBalanceError,
} from "../utils/days-utils";

/**
 * Hook for handling insufficient days in trip view screen
 * @returns Insufficient days state and functions
 */
export const useTripViewInsufficientDays = () => {
  const [showDaysQuotaModal, setShowDaysQuotaModal] = useState<boolean>(false);
  const [requiredDays, setRequiredDays] = useState<number>(0);

  const { balance, fetchBalance, getTotalAvailableDays } = useDaysBalance();
  const { handleInsufficientBalanceError: handleGlobalInsufficientBalance } = useGlobalInsufficientDays();

  // Use the global insufficient balance handler
  const handleInsufficientBalanceError = useCallback(
    (errorMessage: string, context?: any) => {
      console.info(
        "Delegating insufficient balance error to global handler from trip view:",
        { errorMessage, context },
      );

      // Use the global handler (async)
      handleGlobalInsufficientBalance(errorMessage, context).catch((error) => {
        console.error("Error in global insufficient balance handler:", error);
      });

      // Also update local state for backward compatibility
      if (context?.isInsufficientBalance) {
        let needed = 0;
        let available = getTotalAvailableDays();

        // Extract needed days from context if available
        if (context?.payload?.needed !== undefined) {
          needed = context.payload.needed;
        } else if (context?.payload?.requiredDays !== undefined) {
          needed = context.payload.requiredDays;
        } else {
          needed = extractRequiredDays(errorMessage, available);
        }

        setRequiredDays(needed);
        setShowDaysQuotaModal(true);
      }
    },
    [handleGlobalInsufficientBalance, getTotalAvailableDays],
  );

  return {
    showDaysQuotaModal,
    setShowDaysQuotaModal,
    requiredDays,
    balance,
    fetchBalance,
    getTotalAvailableDays,
    handleInsufficientBalanceError,
  };
};
