import { ResponseType } from "expo-auth-session/build/AuthRequest.types";
import * as <PERSON> from "expo-auth-session/providers/google";
import * as AppleAuthentication from "expo-apple-authentication";
import { router } from "expo-router";
import * as <PERSON>Browser from "expo-web-browser";
import { useEffect } from "react";
import { Platform } from "react-native";
import { googleConfig, API_URL } from "../constants";
import { AuthService } from "../services/auth.service";
import { setAuth, useAuthStore } from "../store";
import { getPersisted, removePersisted } from "./use-storage";

// Ensure web browser can complete auth session
WebBrowser.maybeCompleteAuthSession();

const authService = AuthService.getInstance();

export function useAuth() {
  const { auth, setAuth } = useAuthStore();

  // Configure Google OAuth
  const [request, response, promptAsync] = Google.useAuthRequest({
    androidClientId: googleConfig.androidClientId,
    iosClientId: googleConfig.iosClientId,
    webClientId: googleConfig.webClientId,
    scopes: ["profile", "email"],
    responseType: ResponseType.Code,
  });

  // Handle Google auth response
  useEffect(() => {
    if (response?.type === "success") {
      const { id_token } = response.params;
      handleGoogleAuth(id_token);
    }
  }, [response]);

  const handleGoogleAuth = async (idToken: string) => {
    try {
      setAuth({ ...auth, status: "loading", loadingType: "google" });
      const response = await authService.loginWithGoogle({
        token: idToken,
      });
      setAuth({
        status: "authenticated",
        token: response.access_token,
        user: response.user,
        loadingType: undefined,
      });
    } catch (error) {
      console.error("Google auth error:", error);
      setAuth({
        status: "unauthenticated",
        error: "Failed to authenticate with Google",
        loadingType: undefined,
      });
    }
  };

  const requestVerificationCode = async (email: string) => {
    try {
      const response = await authService.requestVerificationCode(email);
      return response;
    } catch (error) {
      setAuth({
        ...auth,
        status: "unauthenticated",
        error: "Failed to request verification code",
      });
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    if (!request) {
      throw new Error("Google Auth request was not initialized");
    }
    await promptAsync();
  };

  const loginWithApple = async () => {
    if (Platform.OS !== "ios") {
      throw new Error("Apple Sign In is only available on iOS");
    }

    setAuth({ ...auth, status: "loading", loadingType: "apple" });

    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      if (credential.identityToken) {
        const result = await authService.loginWithApple({
          identityToken: credential.identityToken,
          user: credential.fullName ? {
            name: {
              firstName: credential.fullName.givenName || undefined,
              lastName: credential.fullName.familyName || undefined,
            }
          } : undefined,
        });

        setAuth({
          status: "authenticated",
          token: result.access_token,
          user: result.user,
          loadingType: undefined,
        });

        router.dismissAll();
        router.replace("/home");
      } else {
        throw new Error("No identity token received from Apple");
      }
    } catch (error: any) {
      console.error("Apple auth error:", error);
      if (error.code === "ERR_REQUEST_CANCELED") {
        // User canceled the sign-in flow
        setAuth({
          status: "unauthenticated",
          error: undefined,
          loadingType: undefined,
        });
      } else {
        setAuth({
          status: "unauthenticated",
          error: "Failed to authenticate with Apple",
          loadingType: undefined,
        });
      }
    }
  };

  const logout = async () => {
    setAuth({ ...auth, status: "loading", loadingType: "logout" });
    try {
      const success = await authService.logout();
      if (success) {
        setAuth({ status: "unauthenticated", token: undefined, loadingType: undefined });
      } else {
        setAuth({ status: "unauthenticated", error: "Failed to logout", loadingType: undefined });
      }
      router.dismissAll();
      router.replace("/login");
      return success;
    } catch (error) {
      setAuth({ status: "unauthenticated", error: "Failed to logout", loadingType: undefined });
      throw error;
    } finally {
      await removePersisted("token");
    }
  };

  const sendVerificationCode = async (email: string, code: string) => {
    try {
      const response = await authService.sendVerificationCode(email, code);
      setAuth({
        ...auth,
        status: "authenticated",
        token: response.access_token,
        user: response.user,
        loadingType: undefined,
      });
    } catch (error) {
      setAuth({
        ...auth,
        status: "unauthenticated",
        error: "Failed to send verification code",
        loadingType: undefined,
      });
      throw error;
    }
  };

  return {
    requestVerificationCode,
    loginWithGoogle,
    loginWithApple,
    logout,
    auth,
    sendVerificationCode,
  };
}

export const checkToken = async () => {
  const token = await getPersisted("token");
  if (token) {
    try {
      const response = await authService.checkToken(token);

      if (response) {
        setAuth({
          status: "authenticated",
          token: response.access_token,
          user: response.user,
          loadingType: undefined,
        });
      } else {
        setAuth({
          status: "unauthenticated",
          error: "Failed to check token",
          token: undefined,
          loadingType: undefined,
        });
        await removePersisted("token");
      }
    } catch (error) {
      console.log("Token validation failed, clearing stored token:", error);
      setAuth({
        status: "unauthenticated",
        error: "Token expired or invalid",
        token: undefined,
        loadingType: undefined,
      });
      await removePersisted("token");
    }
  } else {
    setAuth({
      status: "unauthenticated",
      error: "No token found",
      token: undefined,
      loadingType: undefined,
    });
  }
};

export const resetStorage = async () => {
  await removePersisted("token");
  await removePersisted("user");
  await removePersisted("isFirstLaunch");
  // Reset auth state to unauthenticated
  setAuth({
    status: "unauthenticated",
    token: undefined,
    user: undefined,
    error: undefined,
    loadingType: undefined,
  });
};
