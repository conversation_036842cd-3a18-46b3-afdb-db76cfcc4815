import { useLocalSearchParams } from "expo-router";
import { useCallback, useMemo, useState } from "react";
import { useSharedValue } from "react-native-reanimated";
import { DbTrip } from "../types";
import { TripFilters } from "./useFilteredTrips";

/**
 * Custom hook for managing home screen state
 * Handles view mode, search, and filters
 */
export const useHomeState = () => {
  // Get the viewMode parameter from navigation if available
  const { viewMode: initialViewMode } = useLocalSearchParams<{
    viewMode?: "map" | "list";
  }>();

  const [viewMode, setViewMode] = useState<"map" | "list">(
    (initialViewMode as "map" | "list") || "map",
  );

  const toggleAnimation = useSharedValue(viewMode === "list" ? 1 : 0);
  const [isZoomedOut, setIsZoomedOut] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<TripFilters>({
    favorite: false,
    generating: false,
    ready: false,
  });

  /**
   * Toggle view mode between map and list
   */
  const toggleViewMode = useCallback(() => {
    const newMode = viewMode === "map" ? "list" : "map";
    setViewMode(newMode);
    toggleAnimation.value = newMode === "map" ? 0 : 1;
  }, [viewMode, toggleAnimation]);

  /**
   * Toggle a filter
   * @param filterName Name of the filter to toggle
   */
  const toggleFilter = useCallback((filterName: keyof TripFilters) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [filterName]: !prevFilters[filterName],
    }));
  }, []);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    setFilters({ favorite: false, generating: false, ready: false });
  }, []);



  /**
   * Group trips by country
   * @param trips Array of trips
   * @returns Object with country as key and array of trips as value
   */
  const groupTripsByCountry = useCallback((trips: DbTrip[]) => {
    if (!trips || !trips.length) return {};

    return trips.reduce(
      (acc, trip) => {
        const country =
          trip.tripDetails?.destination?.split(",").pop()?.trim() || "Unknown";
        if (!acc[country]) {
          acc[country] = [];
        }
        acc[country].push(trip);
        return acc;
      },
      {} as { [key: string]: DbTrip[] },
    );
  }, []);

  return {
    viewMode,
    toggleAnimation,
    isZoomedOut,
    searchQuery,
    filters,
    setViewMode,
    setIsZoomedOut,
    setSearchQuery,
    toggleViewMode,
    toggleFilter,
    clearFilters,
    groupTripsByCountry,
  };
};
