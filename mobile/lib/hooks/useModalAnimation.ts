import { useEffect } from "react";
import {
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from "react-native-reanimated";

/**
 * Hook for modal animation
 * @param visible Whether the modal is visible
 * @returns Animation values and styles
 */
export const useModalAnimation = (visible: boolean) => {
  const scale = useSharedValue(0);
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // Animate modal
      scale.value = withSpring(1, {
        damping: 12,
        stiffness: 100,
      });
      rotation.value = withSequence(
        withTiming(-0.1, { duration: 100 }),
        withSpring(0, { damping: 3 }),
      );
    } else {
      scale.value = withTiming(0);
    }
  }, [visible]);

  const containerStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value * 30}deg` },
    ],
  }));

  return { containerStyle };
};
