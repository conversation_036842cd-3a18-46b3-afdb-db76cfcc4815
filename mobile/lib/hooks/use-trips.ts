import { useCallback, useEffect, useState } from "react";
import { TripService } from "../services/trip.service";
import { DbTrip } from "../types";
const tripService = TripService.getInstance();

interface UseTripsProps {
  enabled?: boolean;
}

export function useTrips({ enabled = true }: UseTripsProps) {
  const [trips, setTrips] = useState<DbTrip[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadTrips = useCallback(async () => {
    if (!enabled) return;
    setLoading(true);
    setError(null);
    try {
      const trips = await tripService.fetchTrips();
      setTrips(trips);
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [enabled]);

  useEffect(() => {
    loadTrips();
  }, [loadTrips]);

  return {
    trips,
    loading,
    error,
    loadTrips,
  };
}
