import { useState, useEffect, useCallback } from "react";
import { DbTrip } from "../types";
import { TripService } from "../services/trip.service";
const tripService = TripService.getInstance();

interface UseTripProps {
  tripId: string;
  enabled?: boolean;
}

export function useTrip({ tripId, enabled = true }: UseTripProps) {
  const [trip, setTrip] = useState<DbTrip | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const loadTrip = useCallback(async () => {
    if (!tripId || !enabled) return;
    setLoading(true);
    setError(null);
    try {
      const trip = await tripService.fetchTripById(tripId);
      setTrip(trip);
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [tripId, enabled]);

  useEffect(() => {
    loadTrip();
  }, [loadTrip]);

  return { trip, loading, error, loadTrip };
}
