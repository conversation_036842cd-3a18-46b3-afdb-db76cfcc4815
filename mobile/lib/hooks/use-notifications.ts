import { useEffect, useState } from "react";
import { NotificationService } from "../services/notification.service";
import { SocketService } from "../services/socket.service";
import {
  NotificationPreferences,
  NotificationSettingsService,
} from "../services/notification-settings.service";
import { useAuth } from "./use-auth";
import { useSocket } from "./use-socket";

const socketService = SocketService.getInstance();
const notificationService = NotificationService.getInstance();
const notificationSettingsService = NotificationSettingsService.getInstance();

export function useNotifications() {
  const { auth } = useAuth();
  const [preferences, setPreferences] =
    useState<NotificationPreferences | null>(null);

  const { isConnected, error, socket } = useSocket({
    auth: {
      token: auth.token,
    },
    autoConnect: true,
  });

  // Fetch notification preferences when authenticated
  useEffect(() => {
    if (auth.status === "authenticated" && auth.user?._id) {
      fetchNotificationPreferences();
    }
  }, [auth.status, auth.user?._id]);

  const fetchNotificationPreferences = async () => {
    try {
      const prefs =
        await notificationSettingsService.getNotificationPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error("Error fetching notification preferences:", error);
      // Use default preferences if there's an error
      setPreferences({
        trip_updates: true,
        new_features: true,
        credit_balance: true,
        special_offers: false,
        travel_tips: true,
      });
    }
  };

  // Check if a notification type is enabled
  const isNotificationEnabled = (
    type: keyof NotificationPreferences,
  ): boolean => {
    if (!preferences) return true; // Default to true if preferences not loaded
    return preferences[type] ?? true; // Default to true if preference not found
  };

  useEffect(() => {
    if (!socket || !isConnected || !auth.user?._id || error) {
      return;
    }

    // Set up user-specific notifications using the base socket service
    // This is for general user notifications not related to trips
    if (!socketService.hasJoined(auth.user?._id)) {
      socketService.joinRoom(auth.user?._id);

      // Set up notification handler
      socket.on("notification", (notification) => {
        // Map notification types to preference keys
        let preferenceKey: keyof NotificationPreferences = "trip_updates";

        if (notification.type === "insufficient_balance") {
          preferenceKey = "credit_balance";
        } else if (
          notification.type === "info" &&
          notification.title.includes("feature")
        ) {
          preferenceKey = "new_features";
        } else if (
          notification.type === "info" &&
          notification.title.includes("tip")
        ) {
          preferenceKey = "travel_tips";
        } else if (
          notification.type === "info" &&
          notification.title.includes("offer")
        ) {
          preferenceKey = "special_offers";
        }

        // Only show notification if this type is enabled
        if (isNotificationEnabled(preferenceKey)) {
          notificationService.showNotification(
            notification.title,
            notification.body,
          );
        } else {
          console.log(
            `Notification of type ${preferenceKey} suppressed due to user preferences`,
          );
        }
      });
    }

    return () => {
      socket.off("notification");
      // We don't leave the room here as the user might still need notifications
    };
  }, [isConnected, auth.user?._id, error, socket, preferences]);
}
