import { Platform } from "react-native";

// Conditional MMKV import to avoid web platform issues
let MMKV: any = null;
let storage: any = null;

if (Platform.OS !== 'web') {
  const MMKVModule = require("react-native-mmkv");
  MMKV = MMKVModule.MMKV;
  storage = new MMKV();
}

// Web fallback storage using localStorage
const webStorage = {
  getString: (key: string) => {
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage.getItem(key);
    }
    return null;
  },
  set: (key: string, value: string) => {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.setItem(key, value);
    }
  },
  delete: (key: string) => {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.removeItem(key);
    }
  },
  clearAll: () => {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.clear();
    }
  },
  getBoolean: (key: string) => {
    if (typeof window !== 'undefined' && window.localStorage) {
      const value = window.localStorage.getItem(key);
      return value === 'true';
    }
    return false;
  }
};

const getStorage = () => {
  return Platform.OS === 'web' ? webStorage : storage;
};

export function useMMKV() {
  const currentStorage = getStorage();

  const getItem = async (key: string) => {
    return await currentStorage?.getString(key);
  };

  const set = async (key: string, value: string | boolean | number) => {
    return await currentStorage?.set(key, value.toString());
  };

  const removeItem = async (key: string) => {
    return await currentStorage?.delete(key);
  };

  const clear = async () => {
    return await currentStorage?.clearAll();
  };

  const getBoolean = async (key: string) => {
    return await currentStorage?.getBoolean(key);
  };

  const setBoolean = async (key: string, value: boolean) => {
    return await currentStorage?.set(key, value.toString());
  };

  return { getItem, set, removeItem, clear, getBoolean, setBoolean };
}

export async function persist(key: string, value: string) {
  const currentStorage = getStorage();
  await currentStorage?.set(key, value);
}

export async function getPersisted(key: string) {
  const currentStorage = getStorage();
  const value = await currentStorage?.getString(key);
  return value;
}

export async function removePersisted(key: string) {
  const currentStorage = getStorage();
  await currentStorage?.delete(key);
}
