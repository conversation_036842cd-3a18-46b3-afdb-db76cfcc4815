import { useState } from "react";
import { Activity } from "../types";

/**
 * Hook for managing activity selection
 * @returns Activity selection state and functions
 */
export const useActivitySelection = () => {
  const [selectedActivity, setSelectedActivity] = useState<number | null>(0);
  const [showActivities, setShowActivities] = useState(false);

  /**
   * Handle activity selection
   * @param index Index of the selected activity
   * @param activities Array of activities
   */
  const handleActivitySelect = (index: number, activities: Activity[]) => {
    if (!activities[index]) {
      return;
    }

    setSelectedActivity(index);
  };

  /**
   * Toggle activities modal
   * @param show Whether to show or hide the modal
   */
  const toggleActivitiesModal = (show: boolean) => {
    setShowActivities(show);
  };

  return {
    selectedActivity,
    showActivities,
    handleActivitySelect,
    toggleActivitiesModal,
  };
};
