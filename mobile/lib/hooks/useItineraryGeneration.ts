import { useCallback, useState } from "react";
import { TripService } from "../services/trip.service";
import { useTripSocket } from "./use-trip-socket";
import { ScreenType } from "../services/trip-socket.service";
import { useDaysBalance } from "./use-days-balance";
import { useGlobalInsufficientDays } from "./useGlobalInsufficientDays";

const tripService = TripService.getInstance();

interface UseItineraryGenerationProps {
  onProgress?: (chunk: string) => void;
  onError?: (error: string) => void;
}

export function useItineraryGeneration({
  onProgress,
  onError,
}: UseItineraryGenerationProps = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tripId, setTripId] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [status, setStatus] = useState<string | null>(null);
  const [elapsedTime] = useState<number>(0);
  const [done, setDone] = useState<boolean>(false);
  const [showDaysQuotaModal, setShowDaysQuotaModal] = useState<boolean>(false);
  const [requiredDays] = useState<number>(0);

  const { balance, fetchBalance, getTotalAvailableDays } = useDaysBalance();
  const { handleInsufficientBalanceError: handleGlobalInsufficientBalance } = useGlobalInsufficientDays();

  // Use the global insufficient balance handler
  const handleInsufficientBalanceError = useCallback(
    (errorMessage: string, context?: any) => {
      console.info(
        "Delegating insufficient balance error to global handler from itinerary generation:",
        { errorMessage, context },
      );

      // Use the global handler (async)
      handleGlobalInsufficientBalance(errorMessage, context).catch((error) => {
        console.error("Error in global insufficient balance handler:", error);
      });

      // Set error message for display
      setError(errorMessage.split('.')[0]);
    },
    [handleGlobalInsufficientBalance],
  );

  // Use our new socket hook for handling trip updates
  useTripSocket({
    tripId: tripId,
    screenType: ScreenType.PROCESSING,
    handlers: {
      onInProgress: useCallback(
        (content: string, progress?: number) => {
          setStatus(content);
          if (progress !== undefined) {
            setProgress(progress);
          }

          if (onProgress) {
            onProgress(content);
          }
        },
        [onProgress],
      ),

      onError: useCallback(
        (content: string, context?: any) => {
          setStatus(null);

          // Only handle insufficient balance errors if context confirms it
          if (context?.isInsufficientBalance) {
            console.info(
              "Detected insufficient balance error from socket with context",
            );
            // Pass both content and context to the handler
            handleInsufficientBalanceError(content, context);
          } else {
            console.info(
              "Received general error (not insufficient balance):",
              { content, context }
            );
          }

          setError(content);

          if (onError) {
            onError(content);
          }
        },
        [onError, handleInsufficientBalanceError],
      ),

      onSuccess: useCallback((_content: string) => {
        setStatus(null);
        setDone(true);
      }, []),
    },
  });

  const generateItinerary = async (params: {
    destination: string;
    arrivalCity: string;
    departureCity: string;
    startDate: string;
    endDate: string;
    arrivalTime: string;
    departureTime: string;
    arrivalMode: "Air" | "Land" | "Sea";
    departureMode: "Air" | "Land" | "Sea";
    tripType: string;
    budget: string;
    intensity: number;
    interests: string[];
    cuisinePreferences?: string[];
    wakeUpTime: string;
    sleepTime: string;
    adults: number;
    children: number;
    mustVisitCities?: string[];
    additionalRequirements?: string;
  }) => {
    setIsLoading(true);
    setError(null);
    setDone(false);

    try {
      // Send initial POST request to create trip
      const trip = await tripService.generateTrip(params);

      if (!trip) {
        throw new Error("Failed to generate itinerary");
      }

      setTripId(trip._id);

      return trip._id;
    } catch (error) {
      console.error("Error generating itinerary:", error);

      // Check if this is an insufficient balance error based on HTTP status code
      // Backend returns 402 Payment Required for InsufficientDaysException
      const isInsufficientBalanceError =
        (error as any)?.response?.status === 402 ||
        (error as any)?.status === 402;

      if (isInsufficientBalanceError) {
        console.info("HTTP 402 error detected - insufficient balance");

        // Create a context object similar to socket errors
        const errorContext = {
          isInsufficientBalance: true,
          notificationType: "insufficient_balance",
          payload: {
            type: "insufficient_balance",
            content: "Not enough days to generate trip",
            // Try to extract balance info from error message if available
            available: 0, // Will be updated by fetchBalance call
            needed: 1, // Default assumption
          }
        };

        const errorMessage = error instanceof Error ? error.message : String(error);
        handleInsufficientBalanceError(errorMessage, errorContext);
      } else {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.info("HTTP error during trip generation (not insufficient balance):", errorMessage);
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    generateItinerary,
    isLoading,
    progress,
    error,
    status,
    elapsedTime,
    tripId,
    done,
    showDaysQuotaModal,
    setShowDaysQuotaModal,
    requiredDays,
    balance,
    fetchBalance,
    getTotalAvailableDays,
  };
}
