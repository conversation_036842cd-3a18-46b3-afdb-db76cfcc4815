import { useEffect, useState } from "react";
import {
  TripSocketService,
  ScreenType,
  TripSocketHandlers,
} from "../services/trip-socket.service";
import { SocketService } from "../services/socket.service";
import { useAuth } from "./use-auth";

interface UseTripSocketProps {
  tripId: string | null | undefined;
  screenType: ScreenType;
  handlers: TripSocketHandlers;
  subscribeToUserRoom?: boolean;
}

/**
 * Hook to handle trip socket updates
 * @param tripId The trip ID to subscribe to
 * @param screenType The current screen type
 * @param handlers Object containing callback functions for different event types
 * @param subscribeToUserRoom Whether to subscribe to user-specific room for notifications
 */
export function useTripSocket({
  tripId,
  screenType,
  handlers,
  subscribeToUserRoom = true,
}: UseTripSocketProps) {
  const tripSocketService = TripSocketService.getInstance();
  const socketService = SocketService.getInstance();
  const [isConnected, setIsConnected] = useState(false);
  const {
    auth: { user },
  } = useAuth();

  // Monitor socket connection status
  useEffect(() => {
    const socket = socketService.getSocket();
    if (!socket) {
      return;
    }

    const handleConnect = () => {
      setIsConnected(true);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
    };

    // Check initial connection status
    setIsConnected(socket.connected);

    // Set up listeners
    socket.on("connect", handleConnect);
    socket.on("disconnect", handleDisconnect);

    return () => {
      socket.off("connect", handleConnect);
      socket.off("disconnect", handleDisconnect);
    };
  }, []);

  // Handle user-specific notifications (even without a tripId)
  useEffect(() => {
    // Only set up if connected and we have a user
    if (!isConnected || !user?._id || !subscribeToUserRoom) {
      return;
    }

    // Set current screen and register handlers
    tripSocketService.setCurrentScreen(screenType, null, user._id);
    tripSocketService.registerHandlers(handlers);

    // Subscribe to user-specific updates
    const unsubscribeUser = tripSocketService.subscribeToUserUpdates(user._id);

    return () => {
      unsubscribeUser();
      // Don't clear handlers here as they might be needed for trip updates
    };
  }, [isConnected, user?._id, screenType, subscribeToUserRoom, handlers]);

  // Handle trip updates
  useEffect(() => {
    if (!tripId) {
      return;
    }

    // Only set up if connected
    if (!isConnected) {
      return;
    }

    // Set current screen and register handlers
    tripSocketService.setCurrentScreen(screenType, tripId, user?._id);
    tripSocketService.registerHandlers(handlers);

    // Subscribe to trip updates - only once per tripId
    const unsubscribe = tripSocketService.subscribeToTripUpdates(tripId);

    return () => {
      unsubscribe();
      // Only clear handlers if we're not also subscribed to user updates
      if (!subscribeToUserRoom || !user?._id) {
        tripSocketService.clearHandlers();
      }
    };
  }, [
    tripId,
    screenType,
    isConnected,
    user?._id,
    subscribeToUserRoom,
    handlers,
  ]);
}
