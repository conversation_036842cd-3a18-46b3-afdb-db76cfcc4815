import { useState } from "react";
import { useTripStore } from "../store";
import {
  LocationSuggestion,
  useLocationSuggestions,
} from "./useLocationSuggestions";

/**
 * Custom hook for managing location selection
 */
export const useLocationSelection = () => {
  const { tripDetails, setTripDetails } = useTripStore();

  // Location suggestions hook
  const {
    isLoading: isLoadingLocations,
    suggestions: locationSuggestions,
    searchCountries,
    searchCities,
    clearSuggestions,
  } = useLocationSuggestions({
    onError: (error) => console.error("Location search error:", error),
  });

  // Modal visibility states
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showCityModal, setShowCityModal] = useState(false);
  const [showMustVisitSheet, setShowMustVisitSheet] = useState(false);

  // Other state
  const [activeField, setActiveField] = useState<
    "arrival" | "departure" | null
  >(null);
  const [searchQuery, setSearchQuery] = useState("");

  /**
   * Handle country search
   */
  const handleCountrySearch = async (text: string) => {
    setSearchQuery(text);
    await searchCountries(text);
  };

  /**
   * Select a country
   */
  const selectCountry = (suggestion: LocationSuggestion) => {
    setTripDetails({
      ...tripDetails,
      destination: suggestion.name,
      arrivalCity: "",
      departureCity: "",
      mustVisitCities: [],
      additionalRequirements: "",
    });
    setShowCountryModal(false);
    setSearchQuery("");
    clearSuggestions();
  };

  /**
   * Handle city search
   */
  const handleCitySearch = async (text: string) => {
    setSearchQuery(text);
    if (tripDetails.destination) {
      await searchCities(text, tripDetails.destination);
    }
  };

  /**
   * Select a city
   */
  const selectCity = (suggestion: LocationSuggestion) => {
    if (activeField && suggestion.name) {
      setTripDetails({
        ...tripDetails,
        [activeField === "arrival" ? "arrivalCity" : "departureCity"]:
          suggestion.name,
      });
    }
    setShowCityModal(false);
    setSearchQuery("");
    clearSuggestions();
    setActiveField(null);
  };

  /**
   * Open the country selection modal
   */
  const openCountryModal = () => {
    setShowCountryModal(true);
    setSearchQuery("");
  };

  /**
   * Open the city selection modal
   */
  const openCityModal = (field: "arrival" | "departure") => {
    setShowCityModal(true);
    setActiveField(field);
    setSearchQuery("");
  };

  /**
   * Open the must visit cities modal
   */
  const openMustVisitModal = () => {
    setShowMustVisitSheet(true);
    setSearchQuery("");
  };

  /**
   * Add a must visit city
   */
  const addMustVisitCity = (suggestion: LocationSuggestion) => {
    if (
      suggestion.name &&
      tripDetails.mustVisitCities &&
      !tripDetails.mustVisitCities.includes(suggestion.name)
    ) {
      setTripDetails({
        ...tripDetails,
        mustVisitCities: [...tripDetails.mustVisitCities, suggestion.name],
      });
    }
    setSearchQuery("");
    clearSuggestions();
  };

  /**
   * Remove a must visit city
   */
  const removeMustVisitCity = (city: string) => {
    if (tripDetails.mustVisitCities) {
      setTripDetails({
        ...tripDetails,
        mustVisitCities: tripDetails.mustVisitCities.filter((c) => c !== city),
      });
    }
  };

  return {
    isLoadingLocations,
    locationSuggestions,
    showCountryModal,
    showCityModal,
    showMustVisitSheet,
    activeField,
    searchQuery,
    setShowCountryModal,
    setShowCityModal,
    setShowMustVisitSheet,
    handleCountrySearch,
    handleCitySearch,
    selectCountry,
    selectCity,
    openCountryModal,
    openCityModal,
    openMustVisitModal,
    addMustVisitCity,
    removeMustVisitCity,
  };
};
