import { useCallback } from "react";
import { useGlobalModalStore } from "../store";
import { DaysBalanceService } from "../services/days-balance.service";
import {
  extractRequiredDays,
  isInsufficientBalanceError,
} from "../utils/days-utils";

const daysBalanceService = DaysBalanceService.getInstance();

/**
 * Global hook for handling insufficient days errors across the entire app
 * This hook can be used from any screen to show the insufficient balance modal
 */
export const useGlobalInsufficientDays = () => {
  const { showInsufficientDaysModal } = useGlobalModalStore();

  /**
   * Get total available days without using the hook to avoid conflicts
   */
  const getTotalAvailableDays = useCallback(async (): Promise<number> => {
    try {
      const balance = await daysBalanceService.getBalance();
      return daysBalanceService.getTotalAvailableDays(balance);
    } catch (error) {
      console.error("Error getting total available days:", error);
      return 0;
    }
  }, []);

  /**
   * Handle insufficient balance errors globally
   * This function can be called from anywhere in the app
   */
  const handleInsufficientBalanceError = useCallback(
    async (errorMessage: string, context?: any) => {
      console.info(
        "Handling insufficient balance error globally:",
        { errorMessage, context },
      );

      // Only handle if this is actually an insufficient balance error
      if (!context?.isInsufficientBalance) {
        console.warn(
          "handleInsufficientBalanceError called but context indicates this is not an insufficient balance error. Ignoring.",
          { context }
        );
        return;
      }

      let needed = 0;
      let available = 0;

      // Try to extract from structured data first
      if (context?.payload?.needed && context?.payload?.available !== undefined) {
        needed = context.payload.needed;
        available = context.payload.available;
        console.info("Using structured data from context:", { needed, available });
      } else {
        // Fallback to parsing the error message
        console.info("No structured data found, parsing error message:", errorMessage);

        // Extract required days from error message
        const extractedDays = extractRequiredDays(errorMessage);
        needed = extractedDays;

        // Get current available days
        available = await getTotalAvailableDays();

        console.info("Extracted from message parsing:", { needed, available });
      }

      // Ensure we have valid numbers
      if (needed <= 0) {
        console.warn("Invalid needed days, defaulting to 1:", needed);
        needed = 1;
      }

      if (available < 0) {
        console.warn("Invalid available days, defaulting to 0:", available);
        available = 0;
      }

      console.info(
        "Final extracted values for global modal:",
        { needed, available, source: needed > 0 ? "structured_data" : "message_parsing" },
      );

      // Show the global modal
      showInsufficientDaysModal(needed, available);
    },
    [getTotalAvailableDays, showInsufficientDaysModal],
  );

  /**
   * Check if an error is an insufficient balance error and handle it
   * Returns true if the error was handled, false otherwise
   */
  const checkAndHandleInsufficientBalance = useCallback(
    async (error: any): Promise<boolean> => {
      if (!error) return false;

      const errorMessage = typeof error === 'string' ? error : error.message || '';

      if (isInsufficientBalanceError(errorMessage)) {
        // Create context similar to socket errors
        const errorContext = {
          isInsufficientBalance: true,
          notificationType: "insufficient_balance",
          payload: {
            type: "insufficient_balance",
            content: errorMessage,
            available: await getTotalAvailableDays(),
            needed: extractRequiredDays(errorMessage) || 1,
          }
        };

        await handleInsufficientBalanceError(errorMessage, errorContext);
        return true;
      }

      return false;
    },
    [handleInsufficientBalanceError, getTotalAvailableDays],
  );

  return {
    handleInsufficientBalanceError,
    checkAndHandleInsufficientBalance,
  };
};
