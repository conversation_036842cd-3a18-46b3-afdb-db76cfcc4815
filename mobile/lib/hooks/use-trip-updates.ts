import { useEffect, useRef, useState } from "react";
import { useTripSocket } from "./use-trip-socket";
import { ScreenType } from "../services/trip-socket.service";

interface UseTripUpdatesProps {
  onUpdate: (data: any) => void;
  tripId: string;
  runOnMount?: boolean;
}

/**
 * @deprecated Use useTripSocket instead
 */
export function useTripUpdates({
  onUpdate,
  runOnMount = true,
  tripId,
}: UseTripUpdatesProps) {
  const [error, setError] = useState<Error | null>(null);
  const alreadyRan = useRef(false);

  // Use the new socket hook with a wrapper for backward compatibility
  useTripSocket({
    tripId,
    screenType: ScreenType.NONE,
    handlers: {
      onInProgress: (content) => {
        try {
          onUpdate({ type: "in_progress", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
      onError: (content) => {
        try {
          onUpdate({ type: "error", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
      onSuccess: (content) => {
        try {
          onUpdate({ type: "ready", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
    },
  });

  // Run onUpdate once if runOnMount is true
  useEffect(() => {
    if (tripId && runOnMount && !alreadyRan.current) {
      onUpdate(tripId);
      alreadyRan.current = true;
    }
  }, [tripId, runOnMount, onUpdate]);

  return { error };
}

/**
 * @deprecated Use useTripSocket instead
 */
export function useTripStreamUpdates({
  tripId,
  onUpdate,
  runOnMount = true,
}: UseTripUpdatesProps) {
  // This is now just a wrapper around useTripSocket for backward compatibility
  const [error, setError] = useState<Error | null>(null);
  const alreadyRan = useRef(false);

  // Use the new socket hook with a wrapper for backward compatibility
  useTripSocket({
    tripId,
    screenType: ScreenType.NONE,
    handlers: {
      onInProgress: (content) => {
        try {
          onUpdate({ type: "in_progress", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
      onError: (content) => {
        try {
          onUpdate({ type: "error", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
      onSuccess: (content) => {
        try {
          onUpdate({ type: "ready", content });
        } catch (err) {
          setError(
            err instanceof Error ? err : new Error("Failed to process update"),
          );
        }
      },
    },
  });

  // Run onUpdate once if runOnMount is true
  useEffect(() => {
    if (tripId && runOnMount && !alreadyRan.current) {
      onUpdate(tripId);
      alreadyRan.current = true;
    }
  }, [tripId, runOnMount, onUpdate]);

  return { error };
}
