import { useMemo } from "react";
import { useRouter } from "expo-router";
import { useTripStore } from "../store";
import { successHapticFeedback } from "../utils/haptics";
import { TripDetails } from "../types";
import { useDaysBalance } from "./use-days-balance";
import {
  calculateMaxDepartureDate,
  calculateMinDepartureDate,
  calculateTripDuration,
  validateTripDuration,
} from "../utils/date-constraints";

/**
 * Custom hook for managing trip details state and validation
 */
export const useTripDetailsState = () => {
  const router = useRouter();
  const { tripDetails, setTripDetails } = useTripStore();
  const { balance } = useDaysBalance();

  /**
   * Convert string dates from store to Date objects for UI components
   */
  const arrivalDate = useMemo(
    () =>
      tripDetails.startDate ? new Date(tripDetails.startDate) : new Date(),
    [tripDetails.startDate],
  );

  const departureDate = useMemo(
    () =>
      tripDetails.endDate
        ? new Date(tripDetails.endDate)
        : new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    [tripDetails.endDate],
  );

  /**
   * Convert time strings to Date objects for time pickers
   */
  const getTimeAsDate = (timeString: string) => {
    const date = new Date();
    const [hours, minutes] = timeString.split(":").map(Number);
    if (!isNaN(hours) && !isNaN(minutes)) {
      date.setHours(hours, minutes, 0, 0);
    }
    return date;
  };



  const wakeUpTime = useMemo(
    () => getTimeAsDate(tripDetails.wakeUpTime),
    [tripDetails.wakeUpTime],
  );

  const sleepTime = useMemo(
    () => getTimeAsDate(tripDetails.sleepTime),
    [tripDetails.sleepTime],
  );

  /**
   * Format time to HH:MM format
   */
  const formatTimeToHHMM = (date: Date) => {
    return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  };

  /**
   * Update trip details
   */
  const updateTripDetails = (updates: Partial<TripDetails>) => {
    setTripDetails({
      ...tripDetails,
      ...updates,
    });
  };

  /**
   * Calculate date constraints based on user balance
   */
  const dateConstraints = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today

    const arrivalDateObj = new Date(tripDetails.startDate);
    const minDepartureDate = calculateMinDepartureDate(arrivalDateObj);
    const maxDepartureDate = calculateMaxDepartureDate(arrivalDateObj, balance);

    return {
      minArrivalDate: today,
      maxArrivalDate: undefined, // No max limit for arrival date
      minDepartureDate,
      maxDepartureDate,
    };
  }, [tripDetails.startDate, balance]);

  /**
   * Validate trip duration
   */
  const tripDurationValidation = useMemo(() => {
    return validateTripDuration(tripDetails.startDate, tripDetails.endDate, balance, 'trip-details');
  }, [tripDetails.startDate, tripDetails.endDate, balance]);

  /**
   * Check if form is valid
   */
  const isFormValid = useMemo(() => {
    const basicValidation = (
      tripDetails.destination !== "" &&
      tripDetails.travelType !== "Select Type" &&
      tripDetails.budget !== undefined
    );

    return basicValidation && tripDurationValidation.isValid;
  }, [tripDetails, tripDurationValidation.isValid]);

  /**
   * Handle next button press
   */
  const handleNext = () => {
    if (isFormValid) {
      // Trigger haptic feedback
      successHapticFeedback();

      // Update the trip details with the formatted values
      setTripDetails({
        ...tripDetails,
        totalDays: calculateTripDuration(
          tripDetails.startDate,
          tripDetails.endDate,
        ),
        wakeUpTime: formatTimeToHHMM(wakeUpTime),
        sleepTime: formatTimeToHHMM(sleepTime),
      });

      router.push("/interests");
    }
  };

  return {
    tripDetails,
    updateTripDetails,
    arrivalDate,
    departureDate,
    wakeUpTime,
    sleepTime,
    isFormValid,
    handleNext,
    formatTimeToHHMM,
    dateConstraints,
    tripDurationValidation,
    balance,
  };
};
