import { useEffect, useState, useCallback } from 'react';
import * as SplashScreen from 'expo-splash-screen';
import { checkToken } from './use-auth';

/**
 * Custom hook for managing splash screen behavior
 * Provides control over when to hide the splash screen with minimum display time
 */
export const useSplashScreen = () => {
  const [appIsReady, setAppIsReady] = useState(false);
  const [hasLayouted, setHasLayouted] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        await checkToken();
        console.log("Token checked")
      } catch (e) {
        console.warn("Token check failed")
        console.warn(e);
      } finally {
        console.log("Setting app ready")
        setAppIsReady(true);
      }
    }
    prepare();
  }, []);

  useEffect(() => {
    if (appIsReady && hasLayouted) {
      console.log("✅ Hiding splash screen safely...");
      SplashScreen.hideAsync();
    } else {
      console.log("❌ Attempted to hide splash before ready/layout");
    }
  }, [appIsReady, hasLayouted]);

  const onLayoutRootView = useCallback(() => {
    setHasLayouted(true);
  }, []);


  return {
    isAppReady: appIsReady,
    onLayoutRootView,
  };
};
