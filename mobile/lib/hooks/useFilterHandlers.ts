import { useCallback, useEffect, useState } from "react";
import { getAvailableDays } from "../../ui/trips/TripViewUtils";
import { TripService } from "../services/trip.service";
import { DbTrip as Trip } from "../types";

const tripService = TripService.getInstance();

interface UseFilterHandlersProps {
  trip: Trip | null;
  handleFilterChangeForRegion: (day: number, city: string) => void;
}

/**
 * Hook for managing filter handlers in trip view
 * @param trip Trip data
 * @param handleFilterChangeForRegion Function to handle filter change for map region
 * @returns Filter state and handlers
 */
export const useFilterHandlers = ({
  trip,
  handleFilterChangeForRegion,
}: UseFilterHandlersProps) => {
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [availableDays, setAvailableDays] = useState<number[]>([]);

  const updateAvailableFilters = useCallback(async () => {
    if (!trip?.itinerary) return;

    const cities = await tripService.fetchTripCities(trip._id);
    setAvailableCities(cities);

    const days = getAvailableDays(trip.itinerary);
    setAvailableDays(days);
  }, [trip]);

  useEffect(() => {
    updateAvailableFilters();
  }, [trip, updateAvailableFilters]);

  // Filter state
  const [selectedDay, setSelectedDay] = useState<number>(0);
  const [selectedCity, setSelectedCity] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<
    "all" | "completed" | "pending"
  >("all");

  // Picker visibility state
  const [showDayPicker, setShowDayPicker] = useState(false);
  const [showCityPicker, setShowCityPicker] = useState(false);
  const [showStatusPicker, setShowStatusPicker] = useState(false);

  // Handle city selection
  const onCitySelected = useCallback(
    (city: string) => {
      setSelectedCity(city);
      setShowCityPicker(false);

      if (!trip?.itinerary) return;

      // If the current selected day isn't available for this city, reset to 'all'
      if (selectedDay !== 0 && !availableDays.includes(selectedDay)) {
        setSelectedDay(0);
      }

      // Fetch updated map region from backend when filter changes
      handleFilterChangeForRegion(selectedDay, city);
    },
    [trip, selectedDay, handleFilterChangeForRegion, availableDays],
  );

  // Handle day selection
  const onDaySelected = useCallback(
    (day: number) => {
      setSelectedDay(day);
      setShowDayPicker(false);

      if (!trip?.itinerary) return;

      // If the current selected city isn't available for this day, reset to 'all'
      if (selectedCity !== "all" && !availableCities.includes(selectedCity)) {
        setSelectedCity("all");
      }

      // Fetch updated map region from backend when filter changes
      handleFilterChangeForRegion(day, selectedCity);
    },
    [trip, selectedCity, handleFilterChangeForRegion, availableCities],
  );

  // Handle status selection
  const onStatusSelected = useCallback(
    (status: "all" | "completed" | "pending") => {
      setSelectedStatus(status);
      setShowStatusPicker(false);

      if (!trip?.itinerary) return;

      // Fetch updated map region from backend when filter changes
      handleFilterChangeForRegion(selectedDay, selectedCity);
    },
    [selectedDay, selectedCity, handleFilterChangeForRegion],
  );

  // Close all pickers
  const closeAllPickers = useCallback(() => {
    setShowDayPicker(false);
    setShowCityPicker(false);
    setShowStatusPicker(false);
  }, []);

  return {
    selectedDay,
    selectedCity,
    selectedStatus,
    showDayPicker,
    showCityPicker,
    showStatusPicker,
    setShowDayPicker,
    setShowCityPicker,
    setShowStatusPicker,
    onCitySelected,
    onDaySelected,
    onStatusSelected,
    closeAllPickers,
  };
};
