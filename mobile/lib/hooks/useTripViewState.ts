import { useState, useCallback } from "react";
import { router } from "expo-router";
import { DbTrip as Trip } from "../types";
import { calculateTripDuration } from "../utils/date-constraints";

interface UseTripViewStateProps {
  initialViewMode?: "map" | "list";
  trip: Trip | null;
}

/**
 * Hook for managing trip view state
 * @param initialViewMode Initial view mode (map or list)
 * @param trip Trip data
 * @returns Trip view state and functions
 */
export const useTripViewState = ({
  initialViewMode,
  trip,
}: UseTripViewStateProps) => {
  // View mode state
  const [viewMode, setViewMode] = useState<"map" | "list">(
    initialViewMode || "map",
  );
  const [itineraryViewMode, setItineraryViewMode] = useState<"day" | "city">(
    "day",
  );

  // Expansion state
  const [expandedDays, setExpandedDays] = useState<{ [key: string]: boolean }>({
    "1": true,
  });
  const [expandedCities, setExpandedCities] = useState<{
    [key: string]: boolean;
  }>({});
  const [showMoreOptions, setShowMoreOptions] = useState(false);

  // Generation state
  const [isGenerating, setIsGenerating] = useState(() => {
    return trip?.daysProgress.some((day) => day.is_generating);
  });

  // Note: isGenerating state is initialized lazily above and managed manually
  // No useEffect needed since we handle state updates in the components

  // Handle navigation back to home screen with the same view mode
  const handleBackToHome = useCallback(() => {
    router.replace({
      pathname: "/home",
      params: { viewMode },
    });
  }, [viewMode]);

  // Toggle day expansion
  const toggleDay = useCallback((dayKey: string) => {
    setExpandedDays((prev) => ({
      ...prev,
      [dayKey]: !prev[dayKey],
    }));
  }, []);

  // Toggle city expansion
  const toggleCity = useCallback((cityKey: string) => {
    setExpandedCities((prev) => ({
      ...prev,
      [cityKey]: !prev[cityKey],
    }));
  }, []);

  // Expand or collapse all days
  const handleExpandAll = useCallback(() => {
    if (!trip) return;

    const allDaysExpanded = Object.values(expandedDays).every(
      (expanded) => expanded,
    );
    const newExpandedState = !allDaysExpanded;

    // Recalculate total days to ensure accuracy
    const days = trip?.tripDetails?.startDate && trip?.tripDetails?.endDate
      ? calculateTripDuration(trip.tripDetails.startDate, trip.tripDetails.endDate)
      : trip.tripDetails.totalDays || 0;

    const newExpandedDays: { [key: string]: boolean } = {};

    for (let i = 1; i <= days; i++) {
      newExpandedDays[i] = newExpandedState;
    }

    setExpandedDays(newExpandedDays);
  }, [trip, expandedDays]);

  return {
    viewMode,
    setViewMode,
    itineraryViewMode,
    setItineraryViewMode,
    expandedDays,
    expandedCities,
    showMoreOptions,
    setShowMoreOptions,
    isGenerating,
    setIsGenerating,
    handleBackToHome,
    toggleDay,
    toggleCity,
    handleExpandAll,
  };
};
