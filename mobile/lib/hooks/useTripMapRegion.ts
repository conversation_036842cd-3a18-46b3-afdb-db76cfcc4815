import { useState, useEffect, useCallback } from "react";
import { DbTrip as Trip } from "../types";
import { MapRegionService } from "../services/map-region.service";

// Import Region type from react-native-maps for type definitions
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};

interface UseTripMapRegionProps {
  tripId: string | null;
  trip: Trip | null;
}

/**
 * Hook for managing map region in trip view
 * @param tripId Trip ID
 * @param trip Trip data
 * @returns Map region state and functions
 */
export const useTripMapRegion = ({ tripId, trip }: UseTripMapRegionProps) => {
  // Initialize with null to indicate we need to set the region
  const [mapRegion, setMapRegion] = useState<Region | null>(null);
  const mapRegionService = MapRegionService.getInstance();

  // Initialize map region from backend
  useEffect(() => {
    const fetchMapRegion = async () => {
      if (!tripId || !trip) return;

      if (!mapRegion || mapRegion === null) {
        try {
          // First try to get trip-specific map region
          const region = await mapRegionService.getTripMapRegion(tripId);
          if (region) {
            setMapRegion(region);
            return;
          }

          // If trip region fails, try to get country-level map region
          if (trip.tripDetails && trip.tripDetails.destination) {
            const countryRegion = await mapRegionService.getCountryMapRegion(
              trip.tripDetails.destination,
            );
            if (countryRegion) {
              setMapRegion(countryRegion);
              return;
            }
          }

          // Fallback to default region if all attempts fail
          setMapRegion({
            latitude: 30,
            longitude: 0,
            latitudeDelta: 180,
            longitudeDelta: 180,
          });
        } catch (error) {
          console.error("Error fetching map region:", error);
          // Fallback to default region on error
          setMapRegion({
            latitude: 30,
            longitude: 0,
            latitudeDelta: 180,
            longitudeDelta: 180,
          });
        }
      }
    };

    fetchMapRegion();
  }, [trip, tripId, mapRegion]);

  // Handle region change from map
  const handleRegionChangeComplete = useCallback((region: Region) => {
    setMapRegion(region);
  }, []);

  // Handle filter change for region
  const handleFilterChangeForRegion = useCallback(
    (day: number, city: string) => {
      if (!tripId) return;

      let $promise;
      if (day > 0) {
        if (city !== "all") {
          $promise = mapRegionService.getCityMapRegion(tripId, city, day);
        } else {
          $promise = mapRegionService.getDailyMapRegion(tripId, day);
        }
      } else {
        if (city !== "all") {
          $promise = mapRegionService.getCityMapRegion(tripId, city);
        } else {
          $promise = mapRegionService.getTripMapRegion(tripId);
        }
      }

      $promise
        .then((region) => {
          if (region) {
            setMapRegion(region);
          }
        })
        .catch((error) => console.error("Error fetching map region:", error));
    },
    [tripId],
  );

  return {
    mapRegion,
    handleRegionChangeComplete,
    handleFilterChangeForRegion,
  };
};
