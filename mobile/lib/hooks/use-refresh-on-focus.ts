import { useCallback, useEffect } from "react";
import { useNavigation } from "@react-navigation/native";

export function useRefreshOnFocus(callback: () => void) {
  const navigation = useNavigation();

  const onFocus = useCallback(() => {
    callback();
  }, [callback]);

  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", onFocus);
    return unsubscribe;
  }, [navigation, onFocus]);
}
