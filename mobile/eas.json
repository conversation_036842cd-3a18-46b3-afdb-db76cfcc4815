{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "env": {"APP_VARIANT": "development"}, "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "buildType": "app-bundle"}}, "preview": {"distribution": "internal", "channel": "preview", "autoIncrement": true, "env": {"APP_VARIANT": "preview"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk"}}, "production": {"channel": "production", "autoIncrement": true, "env": {"APP_VARIANT": "production"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "app-bundle"}}}, "submit": {"production": {"ios": {"appleId": "$APPLE_ID", "ascAppId": "$ASC_APP_ID", "appleTeamId": "$APPLE_TEAM_ID", "sku": "itrip-ios"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production", "releaseStatus": "draft"}}, "preview": {"ios": {"appleId": "$APPLE_ID", "ascAppId": "$ASC_APP_ID", "appleTeamId": "$APPLE_TEAM_ID"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}