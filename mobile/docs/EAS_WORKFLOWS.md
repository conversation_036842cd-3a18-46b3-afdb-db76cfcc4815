# EAS Workflows Documentation

This document explains the EAS (Expo Application Services) workflows for builds and updates.

## Overview

We have three main EAS workflows located in `mobile/.eas/workflows/`:

1. **Preview Build** (`preview.yml`) - Creates preview builds for testing
2. **Production Build** (`production.yml`) - Creates production builds for app store submission
3. **EAS Update** (`update.yml`) - Publishes over-the-air updates

## Prerequisites

### EAS Account Setup

1. **Expo Account** - You need an Expo account with EAS access
   - Sign up at https://expo.dev
   - EAS Workflows run on Expo's infrastructure

### App Store Credentials

For automatic app store submission, you'll need:

1. **Apple Developer Account** - For iOS submissions
   - Apple ID, App Store Connect app ID, Apple Team ID
   - Configured in your EAS project settings

2. **Google Play Console** - For Android submissions
   - Google Service Account JSON file
   - Configured in your EAS project settings

## Workflow Details

### 1. Preview Build (`mobile/.eas/workflows/preview.yml`)

**Triggers:**
- Push to `develop` branch (when mobile files change)
- Pull requests (opened or synchronized)

**What it does:**
- Builds preview versions for both iOS and Android in parallel
- Uses the `preview` profile from `eas.json`
- Creates internal distribution builds for testing

**Build Profile:**
- iOS: Release configuration, internal distribution
- Android: APK format for easy testing

### 2. Production Build (`mobile/.eas/workflows/production.yml`)

**Triggers:**
- Git tags starting with `v` (e.g., `v1.0.0`, `v1.2.3`)

**What it does:**
- Builds production versions for both iOS and Android
- Uses the `production` profile from `eas.json`
- Automatically submits to app stores after successful builds
- Runs builds and submissions in sequence

**Build Profile:**
- iOS: Release configuration, app store distribution
- Android: AAB format for Play Store

### 3. EAS Update (`mobile/.eas/workflows/update.yml`)

**Triggers:**
- Push to `main` branch (publishes to production channel)
- Push to `develop` branch (publishes to development channel)

**What it does:**
- Publishes over-the-air updates without requiring new builds
- Updates are delivered to existing app installations
- Faster than full builds for JavaScript/React Native changes

## Usage Examples

### Creating a Preview Build

```bash
# Push to develop branch
git checkout develop
git add .
git commit -m "Add new feature"
git push origin develop
```

### Creating a Production Release

```bash
# Create and push a version tag
git checkout main
git tag v1.0.1
git push origin v1.0.1
```

### Publishing an Update

```bash
# For development channel
git checkout develop
git add .
git commit -m "Fix bug"
git push origin develop

# For production channel
git checkout main
git add .
git commit -m "Fix critical bug"
git push origin main
```

## Build Profiles

The workflows use the build profiles defined in `mobile/eas.json`:

- **development**: For local development with dev client
- **preview**: For internal testing and QA
- **production**: For app store releases

## Monitoring Builds

- Check build status at: https://expo.dev/accounts/[account]/projects/itrp-mobile/builds
- EAS Dashboard shows workflow progress and logs
- Workflows run on Expo's infrastructure, not GitHub Actions

## Key Differences from GitHub Actions

EAS Workflows are different from GitHub Actions:

1. **Infrastructure**: Runs on Expo's servers, not GitHub's
2. **Configuration**: Uses EAS-specific syntax and pre-packaged jobs
3. **Integration**: Deeply integrated with EAS Build, Submit, and Update
4. **Billing**: Uses EAS build minutes, not GitHub Actions minutes

## Troubleshooting

### Common Issues

1. **Workflow not triggering**: Ensure the workflow file is in the correct location (`mobile/.eas/workflows/`)
2. **Build failures**: Check the EAS build logs for detailed error messages
3. **App store submission failures**: Verify Apple/Google credentials in EAS project settings

### Build Status

- ✅ **Success**: Build completed successfully
- ❌ **Failed**: Check logs for errors
- 🟡 **In Progress**: Build is currently running
- ⏸️ **Canceled**: Build was manually canceled

## Best Practices

1. **Use preview builds** for testing before production releases
2. **Tag releases** with semantic versioning (v1.0.0, v1.1.0, etc.)
3. **Test updates** on development channel before pushing to production
4. **Monitor build times** and optimize if builds become too slow
5. **Configure credentials** properly in EAS project settings

## Related Documentation

- [EAS Workflows Documentation](https://docs.expo.dev/eas/workflows/get-started/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [EAS Submit Documentation](https://docs.expo.dev/submit/introduction/)
- [EAS Update Documentation](https://docs.expo.dev/eas-update/introduction/)
