# Global Insufficient Balance Modal System

## Overview

The global insufficient balance modal system ensures that the insufficient balance modal (`DaysQuotaModal`) is displayed consistently across the entire app, regardless of which screen the user is currently on. This provides a better user experience by showing the modal immediately when an insufficient balance error occurs, rather than only on specific screens.

## Architecture

### 1. Global State Management (`mobile/lib/store.ts`)

The global modal state is managed using Zustand:

```typescript
interface GlobalModalState {
  showDaysQuotaModal: boolean;
  requiredDays: number;
  currentBalance: number;
}

export const useGlobalModalStore = create<GlobalModalStore>((set) => ({
  showDaysQuotaModal: false,
  requiredDays: 0,
  currentBalance: 0,
  
  setShowDaysQuotaModal: (show: boolean) => set({ showDaysQuotaModal: show }),
  showInsufficientDaysModal: (requiredDays: number, currentBalance: number) => set({ 
    showDaysQuotaModal: true, 
    requiredDays, 
    currentBalance 
  }),
  hideDaysQuotaModal: () => set({ showDaysQuotaModal: false }),
}));
```

### 2. Global Modal Provider (`mobile/ui/common/GlobalModalProvider.tsx`)

This component is added to the root layout and renders the global modal:

```typescript
export const GlobalModalProvider: React.FC = () => {
  const { showDaysQuotaModal, requiredDays, currentBalance, hideDaysQuotaModal } = useGlobalModalStore();
  const { fetchBalance } = useDaysBalance();

  return (
    <DaysQuotaModal
      visible={showDaysQuotaModal}
      onClose={hideDaysQuotaModal}
      currentBalance={currentBalance}
      requiredAmount={requiredDays}
      onRefreshBalance={fetchBalance}
    />
  );
};
```

### 3. Global Hook (`mobile/lib/hooks/useGlobalInsufficientDays.ts`)

This hook provides functions to handle insufficient balance errors globally:

```typescript
export const useGlobalInsufficientDays = () => {
  const { showInsufficientDaysModal } = useGlobalModalStore();
  const { getTotalAvailableDays } = useDaysBalance();

  const handleInsufficientBalanceError = useCallback((errorMessage: string, context?: any) => {
    // Extract balance information and show global modal
    showInsufficientDaysModal(needed, available);
  }, []);

  const checkAndHandleInsufficientBalance = useCallback((error: any): boolean => {
    // Check if error is insufficient balance and handle it
    // Returns true if handled, false otherwise
  }, []);

  return { handleInsufficientBalanceError, checkAndHandleInsufficientBalance };
};
```

## Usage

### For Socket Errors

Existing hooks like `useTripGeneration` and `useItineraryGeneration` have been updated to use the global handler:

```typescript
const { handleInsufficientBalanceError: handleGlobalInsufficientBalance } = useGlobalInsufficientDays();

const handleInsufficientBalanceError = useCallback(
  (errorMessage: string, context?: any) => {
    handleGlobalInsufficientBalance(errorMessage, context);
    // Any additional local handling...
  },
  [handleGlobalInsufficientBalance],
);
```

### For HTTP 402 Errors

For API calls that might return 402 errors:

```typescript
const { checkAndHandleInsufficientBalance } = useGlobalInsufficientDays();

try {
  await someApiCall();
} catch (error) {
  const handled = checkAndHandleInsufficientBalance(error);
  if (!handled) {
    // Handle other types of errors
  }
}
```

### For Manual Triggering

To manually show the insufficient balance modal:

```typescript
const { showInsufficientDaysModal } = useGlobalModalStore();

// Show modal with specific values
showInsufficientDaysModal(requiredDays, currentBalance);
```

## Benefits

1. **Consistent UX**: The modal appears immediately when insufficient balance is detected, regardless of the current screen
2. **Centralized Logic**: All insufficient balance handling is centralized in one place
3. **Backward Compatibility**: Existing local modals can be gradually removed without breaking functionality
4. **Global Accessibility**: The modal can be triggered from anywhere in the app

## Migration

The following screens have been updated to use the global modal:

- ✅ Processing screen (`mobile/app/processing.tsx`)
- ✅ Trip view screen (`mobile/app/trip-view.tsx`)
- ✅ Itinerary generation hooks
- ✅ Trip generation hooks
- ✅ Trip view insufficient days hook

Local `DaysQuotaModal` instances have been removed from these screens since they now use the global modal.

## Testing

Use the `TestGlobalModal` component to test the global modal system:

```typescript
import { TestGlobalModal } from "../ui/common/TestGlobalModal";

// Add to any screen for testing
<TestGlobalModal />
```

This component provides buttons to trigger the global modal with different scenarios.
