diff --git a/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
index 3cbedf8..520ca81 100644
--- a/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
+++ b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift
@@ -6,7 +6,7 @@ import Foundation
 // The core module that describes the `global.expo` object.
 internal final class CoreModule: Module {
   internal func definition() -> ModuleDefinition {
-    Constant("expoModulesCoreVersion") {
+    Constant("expoModulesCoreVersion") { () -> [String: Any] in
       let version = CoreModuleHelper.getVersion()
       let components = version.split(separator: "-")[0].split(separator: ".").compactMap { Int($0) }
 
diff --git a/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift.bak b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift.bak
new file mode 100644
index 0000000..520ca81
--- /dev/null
+++ b/node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift.bak
@@ -0,0 +1,83 @@
+// Copyright 2015-present 650 Industries. All rights reserved.
+
+import React
+import Foundation
+
+// The core module that describes the `global.expo` object.
+internal final class CoreModule: Module {
+  internal func definition() -> ModuleDefinition {
+    Constant("expoModulesCoreVersion") { () -> [String: Any] in
+      let version = CoreModuleHelper.getVersion()
+      let components = version.split(separator: "-")[0].split(separator: ".").compactMap { Int($0) }
+
+      return [
+        "version": version,
+        "major": components[0],
+        "minor": components[1],
+        "patch": components[2]
+      ]
+    }
+
+    Constant("cacheDir") {
+      FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""
+    }
+
+    Constant("documentsDir") {
+      FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
+    }
+
+    // Expose some common classes and maybe even the `modules` host object in the future.
+    Function("uuidv4") { () -> String in
+      return UUID().uuidString.lowercased()
+    }
+
+    Function("uuidv5") { (name: String, namespace: String) -> String in
+      guard let namespaceUuid = UUID(uuidString: namespace) else {
+        throw InvalidNamespaceException(namespace)
+      }
+
+      return uuidv5(name: name, namespace: namespaceUuid).uuidString.lowercased()
+    }
+
+    // swiftlint:disable:next unused_closure_parameter
+    Function("getViewConfig") { (moduleName: String, viewName: String?) -> [String: Any]? in
+      var validAttributes: [String: Any] = [:]
+      var directEventTypes: [String: Any] = [:]
+      let moduleHolder = appContext?.moduleRegistry.get(moduleHolderForName: getHolderName(moduleName))
+
+      guard let viewDefinition = moduleHolder?.definition.views[viewName ?? DEFAULT_MODULE_VIEW] else {
+        return nil
+      }
+      for propName in viewDefinition.getSupportedPropNames() {
+        validAttributes[propName] = true
+      }
+      for eventName in viewDefinition.getSupportedEventNames() {
+        guard let normalizedEventName = RCTNormalizeInputEventName(eventName) else {
+          continue
+        }
+        directEventTypes[normalizedEventName] = [
+          "registrationName": eventName
+        ]
+      }
+
+      return [
+        "validAttributes": validAttributes,
+        "directEventTypes": directEventTypes
+      ]
+    }
+
+    AsyncFunction("reloadAppAsync") { (reason: String) in
+      DispatchQueue.main.async {
+        RCTTriggerReloadCommandListeners(reason)
+      }
+    }
+  }
+
+  private func getHolderName(_ viewName: String) -> String {
+    if let appIdentifier = appContext?.appIdentifier, viewName.hasSuffix("_\(appIdentifier)") {
+      return String(viewName.dropLast("_\(appIdentifier)".count))
+    }
+
+    return viewName
+  }
+}
diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
index 7236b01..e24dc0f 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AnyChild.swift
@@ -5,14 +5,16 @@ import SwiftUI
 /**
  A type-erased protocol representing a child view for a SwiftUI view.
  */
-extension ExpoSwiftUI {
-  public protocol AnyChild: SwiftUI.View {
-    // swiftlint:disable:next nesting - Keep AnyChild protocol inside ExpoSwiftUI namespace
-    associatedtype ChildViewType: SwiftUI.View
-    var childView: ChildViewType { get }
+// Define the protocol outside the extension
+public protocol ExpoSwiftUIAnyChild: SwiftUI.View {
+  associatedtype ChildViewType: SwiftUI.View
+  var childView: ChildViewType { get }
+  var id: ObjectIdentifier { get }
+}
 
-    var id: ObjectIdentifier { get }
-  }
+// Then extend it in the ExpoSwiftUI namespace
+extension ExpoSwiftUI {
+  public typealias AnyChild = ExpoSwiftUIAnyChild
 }
 
 public extension ExpoSwiftUI.AnyChild where Self == ChildViewType {
diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
index 3c855b7..5725312 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/AutoSizingStack.swift
@@ -32,12 +32,16 @@ extension ExpoSwiftUI {
           content.overlay {
             content.fixedSize(horizontal: axis.contains(.horizontal), vertical: axis.contains(.vertical))
               .hidden()
-              .onGeometryChange(for: CGSize.self, of: { proxy in proxy.size }, action: { size in
-                var size = size
-                size.width = axis.contains(.horizontal) ? size.width : ShadowNodeProxy.UNDEFINED_SIZE
-                size.height = axis.contains(.vertical) ? size.height : ShadowNodeProxy.UNDEFINED_SIZE
-                proxy.setViewSize?(size)
-              })
+              .background(
+                GeometryReader { proxy in
+                  Color.clear.onAppear {
+                    var size = proxy.size
+                    size.width = axis.contains(.horizontal) ? size.width : ShadowNodeProxy.UNDEFINED_SIZE
+                    size.height = axis.contains(.vertical) ? size.height : ShadowNodeProxy.UNDEFINED_SIZE
+                    self.proxy.setViewSize?(size)
+                  }
+                }
+              )
           }
         } else {
           content
diff --git a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
index 0b879b0..6fad0b4 100644
--- a/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
+++ b/node_modules/expo-modules-core/ios/Core/Views/SwiftUI/SwiftUIHostingView.swift
@@ -11,6 +11,12 @@ internal protocol AnyExpoSwiftUIHostingView {
   func getProps() -> ExpoSwiftUI.ViewProps
 }
 
+/**
+ For a SwiftUI view to self-contain a HostingView, it can conform to the WithHostingView protocol.
+ */
+public protocol ExpoSwiftUIWithHostingView {
+}
+
 extension ExpoSwiftUI {
   /**
    Checks if the child view is wrapped by a `UIViewHost` and matches the specified SwiftUI view type.
@@ -25,12 +31,7 @@ extension ExpoSwiftUI {
 
 extension ExpoSwiftUI {
   internal typealias AnyHostingView = AnyExpoSwiftUIHostingView
-
-  /**
-   For a SwiftUI view to self-contain a HostingView, it can conform to the WithHostingView protocol.
-   */
-  public protocol WithHostingView {
-  }
+  public typealias WithHostingView = ExpoSwiftUIWithHostingView
 
   /**
    A hosting view that renders a SwiftUI view inside the UIKit view hierarchy.
