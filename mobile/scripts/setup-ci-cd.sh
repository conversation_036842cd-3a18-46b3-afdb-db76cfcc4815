#!/bin/bash

# Setup script for GitHub Actions CI/CD
# This script helps configure the initial setup for EAS and GitHub Actions

set -e

echo "🚀 Setting up GitHub Actions CI/CD for Expo app..."

# Check if we're in the mobile directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the mobile directory"
    exit 1
fi

# Check if EAS CLI is installed
if ! command -v eas &> /dev/null; then
    echo "📦 Installing EAS CLI..."
    npm install --global eas-cli
fi

# Check if user is logged in to Expo
echo "🔐 Checking Expo authentication..."
if ! eas whoami &> /dev/null; then
    echo "Please log in to your Expo account:"
    eas login
fi

# Initialize EAS project if not already done
if [ ! -f "eas.json" ]; then
    echo "⚙️ Initializing EAS project..."
    eas build:configure
else
    echo "✅ EAS project already configured"
fi

# Create a test build to verify setup
echo "🧪 Creating test builds to verify setup..."
echo "Building development profile for iOS..."
eas build --platform ios --profile development --non-interactive

echo "Building development profile for Android..."
eas build --platform android --profile development --non-interactive

# Display next steps
echo ""
echo "✅ Setup complete! Next steps:"
echo ""
echo "1. Set up GitHub secrets in your repository:"
echo "   - EXPO_TOKEN: Get from https://expo.dev/accounts/[account]/settings/access-tokens"
echo "   - APPLE_ID: Your Apple ID email"
echo "   - ASC_APP_ID: App Store Connect app ID"
echo "   - APPLE_TEAM_ID: Apple Developer Team ID"
echo "   - GOOGLE_SERVICE_ACCOUNT_JSON: Base64 encoded service account JSON"
echo ""
echo "2. Create your app in App Store Connect and Google Play Console"
echo ""
echo "3. Test the workflows:"
echo "   - Create a pull request to test CI"
echo "   - Create a tag (v1.0.0) to test CD"
echo ""
echo "4. Read the full setup guide: ./GITHUB_ACTIONS_SETUP.md"
echo ""
echo "🎉 Happy deploying!"
