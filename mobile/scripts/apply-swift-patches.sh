#!/bin/bash

# Apply Swift compatibility patches for Expo modules
# This script should be run after npm install

echo "Applying Swift compatibility patches..."

# Check if the CoreModule.swift file exists
CORE_MODULE_FILE="node_modules/expo-modules-core/ios/Core/Modules/CoreModule.swift"

if [ ! -f "$CORE_MODULE_FILE" ]; then
    echo "Warning: $CORE_MODULE_FILE not found. Skipping Swift patches."
    exit 0
fi

# Check if the file already has the fix applied
if grep -q "{ () -> \[String: Any\] in () -> \[String: Any\] in" "$CORE_MODULE_FILE"; then
    echo "Applying fix for duplicate closure declaration in CoreModule.swift..."
    
    # Apply the fix using sed
    sed -i '' 's/{ () -> \[String: Any\] in () -> \[String: Any\] in/{ () -> [String: Any] in/g' "$CORE_MODULE_FILE"
    
    echo "✅ Swift compatibility patch applied successfully!"
else
    echo "✅ Swift compatibility patch already applied or not needed."
fi

echo "Swift patches application completed."
