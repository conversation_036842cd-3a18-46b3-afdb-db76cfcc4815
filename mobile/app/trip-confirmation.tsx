import { MaterialCommunityIcons, Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useAuthStore, useTripStore } from "../lib/store";
import ScreenHeader from "../ui/common/ScreenHeader";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { INTERESTS_ICONS } from "../lib/constants";
import {
  mediumHapticFeedback,
  successHapticFeedback,
} from "../lib/utils/haptics";
import { calculateTripDuration } from "../lib/utils/date-constraints";

export default function TripConfirmationScreen() {
  const { tripDetails } = useTripStore();
  const [showAllInterests, setShowAllInterests] = useState(false);
  const { auth } = useAuthStore();
  const { bottom } = useSafeAreaInsets();

  // Debug: Let's see exactly what dates we're working with
  console.log('=== TRIP CONFIRMATION DEBUG ===');
  console.log('Raw startDate:', tripDetails.startDate);
  console.log('Raw endDate:', tripDetails.endDate);
  console.log('Parsed start:', new Date(tripDetails.startDate).toISOString());
  console.log('Parsed end:', new Date(tripDetails.endDate).toISOString());
  console.log('Start local date:', new Date(tripDetails.startDate).toLocaleDateString());
  console.log('End local date:', new Date(tripDetails.endDate).toLocaleDateString());

  const calculatedDays = calculateTripDuration(tripDetails.startDate, tripDetails.endDate);
  console.log('Calculated days:', calculatedDays);
  console.log('Stored totalDays:', tripDetails.totalDays);
  console.log('================================');


  const handleConfirm = () => {
    // Provide haptic feedback when confirming
    successHapticFeedback();

    if (auth.status === "authenticated") {
      router.replace("/processing");
    } else {
      router.replace({
        pathname: "/login",
        params: {
          backRoute: "/trip-confirmation",
        },
      });
    }
  };

  const visibleInterests = showAllInterests
    ? tripDetails.interests
    : tripDetails.interests.slice(0, 5);

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Trip Summary"
        subtitle="Review your trip details before we create your itinerary"
        showBackButton
        rightComponent={
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => {
              mediumHapticFeedback();
              router.dismissAll();
            }}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        }
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: bottom + 100 }}
        >
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons
                name="map-marker-path"
                size={24}
                color="#2196F3"
              />
              <Text style={styles.sectionTitle}>Trip Overview</Text>
            </View>

            <View style={styles.detailsContainer}>
              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="calendar-range"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  {new Date(tripDetails.startDate).toLocaleDateString()} -{" "}
                  {new Date(tripDetails.endDate).toLocaleDateString()} (
                  {calculateTripDuration(tripDetails.startDate, tripDetails.endDate)} days)
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="map-marker"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  {tripDetails.arrivalCity}{" "}
                  {tripDetails.arrivalCity !== tripDetails.departureCity
                    ? `→ ${tripDetails.departureCity}`
                    : ""}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="airplane"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  Arrival: {tripDetails.arrivalMode} ({tripDetails.arrivalTime})
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="airplane-takeoff"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  Departure: {tripDetails.departureMode} (
                  {tripDetails.departureTime})
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="account-group"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  {tripDetails.people.adults} Adult
                  {tripDetails.people.adults > 1 ? "s" : ""}
                  {tripDetails.people.children > 0
                    ? `, ${tripDetails.people.children} Child${tripDetails.people.children > 1 ? "ren" : ""}`
                    : ""}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons name="wallet" size={20} color="#666" />
                <Text style={styles.detailText}>
                  Budget: {tripDetails.budget}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons name="run" size={20} color="#666" />
                <Text style={styles.detailText}>
                  Intensity: {tripDetails.intensity}/10
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="clock-time-four"
                  size={20}
                  color="#666"
                />
                <Text style={styles.detailText}>
                  Wake up: {tripDetails.wakeUpTime} - Sleep:{" "}
                  {tripDetails.sleepTime}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <MaterialCommunityIcons name="food" size={20} color="#666" />
                <Text style={styles.detailText}>
                  Cuisine: {tripDetails?.cuisinePreferences?.join(", ") || "No Preference"}
                </Text>
              </View>

              {tripDetails.travelType && (
                <View style={styles.detailItem}>
                  <MaterialCommunityIcons
                    name="bag-suitcase"
                    size={20}
                    color="#666"
                  />
                  <Text style={styles.detailText}>
                    Travel Style: {tripDetails.travelType}
                  </Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="heart" size={24} color="#2196F3" />
              <Text style={styles.sectionTitle}>Selected Interests</Text>
            </View>

            <View style={styles.interestsContainer}>
              {visibleInterests.map((interest, index) => (
                <View key={index} style={styles.interestItem}>
                  <Ionicons
                    name={(INTERESTS_ICONS[interest] as any) || "add-outline"}
                    size={24}
                    color="#2196F3"
                  />
                  <Text style={styles.interestName}>{interest}</Text>
                </View>
              ))}
            </View>

            {!showAllInterests && tripDetails.interests.length > 5 && (
              <TouchableOpacity
                style={styles.seeMoreButton}
                onPress={() => setShowAllInterests(true)}
              >
                <Text style={styles.seeMoreText}>
                  See {tripDetails.interests.length - 5} more interests
                </Text>
                <Ionicons name="chevron-down" size={20} color="#2196F3" />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons
                name="information"
                size={24}
                color="#2196F3"
              />
              <Text style={styles.sectionTitle}>What&apos;s Next?</Text>
            </View>

            <Text style={styles.infoText}>
              We&apos;ll use AI to create a personalized itinerary based on your
              preferences. This includes:
            </Text>
            <View style={styles.bulletPoints}>
              <Text style={styles.bulletPoint}>• Optimized daily schedule</Text>
              <Text style={styles.bulletPoint}>
                • Curated attractions and activities
              </Text>
              <Text style={styles.bulletPoint}>
                • Restaurant recommendations
              </Text>
            </View>
          </View>
        </ScrollView>
        <View style={[styles.bottomButtonContainer]}>
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={handleConfirm}
          >
            <Text style={styles.confirmButtonText}>Generate my trip</Text>
            <MaterialCommunityIcons name="arrow-right" size={24} color="#FFF" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
    backgroundColor: "#F5F5F5",
    borderRadius: 12,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#2196F3",
  },
  detailsContainer: {
    gap: 12,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  detailText: {
    fontSize: 16,
    color: "#333",
  },
  interestsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  interestItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#E3F2FD",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  interestName: {
    fontSize: 14,
    color: "#2196F3",
    fontWeight: "500",
  },
  infoText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 12,
    lineHeight: 22,
  },
  bulletPoints: {
    gap: 8,
  },
  bulletPoint: {
    fontSize: 16,
    color: "#666",
    paddingLeft: 8,
  },
  bottomButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    paddingTop: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
  },
  confirmButton: {
    width: "100%",
    flexDirection: "row",
    backgroundColor: "#2196F3",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  confirmButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  seeMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    marginTop: 12,
    gap: 8,
  },
  seeMoreText: {
    fontSize: 14,
    color: "#2196F3",
    fontWeight: "600",
  },
});
