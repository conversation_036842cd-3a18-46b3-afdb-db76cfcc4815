import { useRouter } from "expo-router";
import React, { useState, useEffect } from "react";
import {
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { MMKV } from "react-native-mmkv";
import { mediumHapticFeedback } from "../lib/utils/haptics";
import ScreenHeader from "../ui/common/ScreenHeader";
import { LocationSection } from "../ui/trip-details/LocationSection";
import { DateTimeSection } from "../ui/trip-details/DateTimeSection";
import { TripPreferencesSection } from "../ui/trip-details/TripPreferencesSection";
import { CuisinePreferencesSection } from "../ui/trip-details/CuisinePreferencesSection";
import { AdditionalOptionsSection } from "../ui/trip-details/AdditionalOptionsSection";
import { TripDetailsFooter } from "../ui/trip-details/TripDetailsFooter";
import { GuidelinesModal } from "../ui/trip-details/GuidelinesModal";
import { useTripDetailsState } from "../lib/hooks/useTripDetailsState";

// Storage for guidelines modal state
const storage = new MMKV();
const GUIDELINES_SHOWN_KEY = "trip_details_guidelines_shown";

/**
 * Trip details screen
 * Allows users to enter details for their trip
 */
export default function TripDetailsScreen() {
  const router = useRouter();
  const { bottom } = useSafeAreaInsets();

  // Guidelines modal state
  const [showGuidelinesModal, setShowGuidelinesModal] = useState(false);

  // Use the custom hook to manage trip details state
  const {
    tripDetails,
    updateTripDetails,
    arrivalDate,
    departureDate,
    wakeUpTime,
    sleepTime,
    isFormValid,
    handleNext,
    formatTimeToHHMM,
    dateConstraints,
    tripDurationValidation,
  } = useTripDetailsState();

  // Check if guidelines modal should be shown on first visit
  useEffect(() => {
    const hasShownGuidelines = storage.getBoolean(GUIDELINES_SHOWN_KEY);
    if (!hasShownGuidelines) {
      setShowGuidelinesModal(true);
    }
  }, []);

  // Handle guidelines modal close
  const handleGuidelinesModalClose = () => {
    setShowGuidelinesModal(false);
    storage.set(GUIDELINES_SHOWN_KEY, true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Trip Details"
        subtitle="Enter your trip details"
        showBackButton
        rightComponent={
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => {
              mediumHapticFeedback();
              router.back();
            }}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: bottom + 100 }}
        >
          {/* Location Section */}
          <LocationSection
            tripDetails={tripDetails}
            updateTripDetails={updateTripDetails}
          />

          {/* Date & Time Section */}
          <DateTimeSection
            tripDetails={tripDetails}
            updateTripDetails={updateTripDetails}
            arrivalDate={arrivalDate}
            departureDate={departureDate}
            wakeUpTime={wakeUpTime}
            sleepTime={sleepTime}
            formatTimeToHHMM={formatTimeToHHMM}
            dateConstraints={dateConstraints}
            tripDurationValidation={tripDurationValidation}
          />

          {/* Trip Preferences Section */}
          <TripPreferencesSection
            tripDetails={tripDetails}
            updateTripDetails={updateTripDetails}
          />

          {/* Cuisine Preferences Section */}
          <CuisinePreferencesSection
            tripDetails={tripDetails}
            updateTripDetails={updateTripDetails}
          />

          {/* Additional Options Section */}
          <AdditionalOptionsSection
            tripDetails={tripDetails}
            updateTripDetails={updateTripDetails}
          />
        </ScrollView>

        {/* Footer with Next Button */}
        <TripDetailsFooter isFormValid={isFormValid} onNext={handleNext} />
      </KeyboardAvoidingView>

      {/* Guidelines Modal */}
      <GuidelinesModal
        visible={showGuidelinesModal}
        onClose={handleGuidelinesModalClose}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
  },
});
