import { Ionicons } from "@expo/vector-icons";
import { Redirect, router, useLocalSearchParams } from "expo-router";
import * as AppleAuthentication from "expo-apple-authentication";
import * as WebBrowser from "expo-web-browser";
import React, { useState, useEffect } from "react";
import {
  Alert,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Image,
  StatusBar,
  ActivityIndicator,
  Platform,
} from "react-native";
import { useAuth } from "../lib/hooks/use-auth";
import { KeyboardAvoidingWrapper } from "../ui/common/KeyboardAvoidingWrapper";
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

WebBrowser.maybeCompleteAuthSession();

export default function Login() {
  const [email, setEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isVerificationLoading, setIsVerificationLoading] = useState(false);
  const [isAppleSignInAvailable, setIsAppleSignInAvailable] = useState(false);
  const {
    loginWithGoogle,
    loginWithApple,
    requestVerificationCode,
    auth,
    sendVerificationCode,
  } = useAuth();
  const { backRoute } = useLocalSearchParams<{ backRoute: string }>();

  // Use global auth state for loading states since they're managed by the auth hook
  const isGoogleLoading = auth?.status === "loading" && auth?.loadingType === "google";
  const isAppleLoading = auth?.status === "loading" && auth?.loadingType === "apple";

  // Check if Apple Sign In is available
  useEffect(() => {
    const checkAppleSignInAvailability = async () => {
      if (Platform.OS === "ios") {
        const isAvailable = await AppleAuthentication.isAvailableAsync();
        setIsAppleSignInAvailable(isAvailable);
      }
    };
    checkAppleSignInAvailability();
  }, []);

  const handleEmailLogin = async () => {
    try {
      if (!email || !emailRegex.test(email)) {
        Alert.alert("Error", "Please fill in a valid email");
        return;
      }
      setIsEmailLoading(true);
      const isCodeSent = await requestVerificationCode(email);
      if (isCodeSent) {
        setIsCodeSent(true);
      }
    } catch {
      Alert.alert("Error", "Failed to login. Please check your credentials.");
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleSendVerificationCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      Alert.alert("Error", "Please fill in a valid verification code");
      return;
    }

    try {
      setIsVerificationLoading(true);
      await sendVerificationCode(email, verificationCode);
      router.replace(backRoute ?? "/home");
    } catch {
      Alert.alert("Error", "Failed to send verification code.");
    } finally {
      setIsVerificationLoading(false);
    }
  };

  if (auth?.status === "authenticated") {
    return <Redirect href={backRoute ?? "/home"} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingWrapper
        style={styles.keyboardWrapper}
        contentContainerStyle={styles.scrollContent}
        keyboardVerticalOffset={100}
      >
        {/* Logo and Welcome Section */}
        <View style={styles.welcomeSection}>
          <View style={styles.logoContainer}>
            <Image
              source={require("../assets/images/logo.png")}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
          <Text style={styles.welcomeTitle}>Welcome Back</Text>
          <Text style={styles.welcomeSubtitle}>Sign in to continue your journey</Text>
        </View>

        {/* Form Section */}
        <View style={styles.formSection}>
          <TouchableOpacity
            style={[styles.googleButton, isGoogleLoading && styles.disabledButton]}
            onPress={() => !isGoogleLoading && loginWithGoogle()}
            disabled={isGoogleLoading}
          >
            {isGoogleLoading ? (
              <ActivityIndicator size="small" color="#EA4335" />
            ) : (
              <Ionicons name="logo-google" size={24} color="#EA4335" />
            )}
            <Text style={styles.googleButtonText}>
              {isGoogleLoading ? "Signing in..." : "Continue with Google"}
            </Text>
          </TouchableOpacity>

          {isAppleSignInAvailable && (
            <TouchableOpacity
              style={[styles.appleButton, isAppleLoading && styles.disabledButton]}
              onPress={() => !isAppleLoading && loginWithApple()}
              disabled={isAppleLoading}
            >
              {isAppleLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Ionicons name="logo-apple" size={24} color="#FFFFFF" />
              )}
              <Text style={styles.appleButtonText}>
                {isAppleLoading ? "Signing in..." : "Continue with Apple"}
              </Text>
            </TouchableOpacity>
          )}

          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.divider} />
          </View>

          {!isCodeSent ? (
            <>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                placeholderTextColor="#999"
              />
              <Text style={styles.hintText}>
                You&apos;ll receive a verification code in your inbox
              </Text>
              <TouchableOpacity
                style={[styles.loginButton, isEmailLoading && styles.disabledButton]}
                onPress={() => !isEmailLoading && handleEmailLogin()}
                disabled={isEmailLoading}
              >
                {isEmailLoading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.loginButtonText}>Continue</Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <>
              <Text style={styles.label}>Verification Code</Text>
              <TextInput
                style={styles.input}
                value={verificationCode}
                onChangeText={setVerificationCode}
                placeholder="Enter 6-digit code"
                keyboardType="numeric"
                autoCapitalize="none"
                autoComplete="off"
                placeholderTextColor="#999"
              />
              <TouchableOpacity
                style={[styles.loginButton, isVerificationLoading && styles.disabledButton]}
                onPress={() => !isVerificationLoading && handleSendVerificationCode()}
                disabled={isVerificationLoading}
              >
                {isVerificationLoading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.loginButtonText}>Verify</Text>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.changeEmailButton}
                onPress={() => setIsCodeSent(false)}
              >
                <Text style={styles.changeEmailButtonText}>Change email</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </KeyboardAvoidingWrapper>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#2196F3",
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardWrapper: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "space-between",
    paddingHorizontal: 24,
  },
  welcomeSection: {
    alignItems: "center",
    paddingTop: 40,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 30,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: "800",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 18,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
  },
  formSection: {
    backgroundColor: "#FFFFFF",
    borderRadius: 24,
    padding: 24,
    marginBottom: 40,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  googleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  googleButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
    color: "#1a1a1a",
  },
  appleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#000000",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  appleButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
    color: "#FFFFFF",
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  dividerText: {
    marginHorizontal: 16,
    color: "#666",
    fontSize: 14,
    fontWeight: "500",
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  input: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: "#F8F9FA",
  },
  loginButton: {
    backgroundColor: "#2196F3",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 8,
    shadowColor: "#2196F3",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  loginButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "700",
  },
  disabledButton: {
    opacity: 0.6,
  },
  changeEmailButton: {
    padding: 12,
    alignItems: "center",
    marginTop: 16,
  },
  changeEmailButtonText: {
    color: "#2196F3",
    fontSize: 16,
    fontWeight: "600",
  },
  hintText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
    textAlign: "center",
    fontStyle: "italic",
  },
});
