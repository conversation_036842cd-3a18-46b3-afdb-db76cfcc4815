import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import ScreenHeader from "../ui/common/ScreenHeader";

export default function Privacy() {
  return (
    <View style={styles.container}>
      <ScreenHeader
        title="Privacy Policy"
        subtitle="How we handle your data"
        showBackButton
      />
      <ScrollView style={styles.content}>
        <Text style={styles.lastUpdated}>Last updated: March 2024</Text>

        <Text style={styles.section}>Data Collection</Text>
        <Text style={styles.paragraph}>
          We collect minimal personal information necessary to provide you with
          the best travel planning experience. This includes:
        </Text>
        <Text style={styles.bulletPoint}>
          • Account information (email, name)
        </Text>
        <Text style={styles.bulletPoint}>• Travel preferences and history</Text>
        <Text style={styles.bulletPoint}>
          • Device information and app usage data
        </Text>

        <Text style={styles.section}>AI Technology</Text>
        <Text style={styles.paragraph}>
          Our app uses artificial intelligence to enhance your travel planning
          experience. The AI processes your travel preferences and requirements
          to generate personalized recommendations.
        </Text>

        <Text style={styles.section}>Data Security</Text>
        <Text style={styles.paragraph}>
          We implement industry-standard security measures to protect your
          personal information. Your data is encrypted during transmission and
          storage.
        </Text>

        <Text style={styles.section}>Third-Party Services</Text>
        <Text style={styles.paragraph}>
          We may use third-party services for analytics, payment processing, and
          other features. These services have their own privacy policies and
          data handling practices.
        </Text>

        <Text style={styles.section}>Your Rights</Text>
        <Text style={styles.paragraph}>You have the right to:</Text>
        <Text style={styles.bulletPoint}>• Access your personal data</Text>
        <Text style={styles.bulletPoint}>• Request data deletion</Text>
        <Text style={styles.bulletPoint}>• Opt-out of data collection</Text>
        <Text style={styles.bulletPoint}>• Update your information</Text>

        <Text style={styles.section}>Contact Us</Text>
        <Text style={styles.paragraph}>
          If you have any questions about our privacy policy or data practices,
          please contact <NAME_EMAIL>
        </Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  lastUpdated: {
    fontSize: 14,
    color: "#666",
    marginBottom: 24,
    fontStyle: "italic",
  },
  section: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1a1a1a",
    marginTop: 24,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginBottom: 16,
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginLeft: 16,
    marginBottom: 8,
  },
});
