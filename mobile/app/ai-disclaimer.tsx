import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import ScreenHeader from "../ui/common/ScreenHeader";

export default function AIDisclaimer() {
  return (
    <View style={styles.container}>
      <ScreenHeader
        title="AI Disclaimer"
        subtitle="Understanding AI-powered features"
        showBackButton
      />
      <ScrollView style={styles.content}>
        <Text style={styles.lastUpdated}>Last updated: March 2024</Text>

        <Text style={styles.section}>AI Technology Usage</Text>
        <Text style={styles.paragraph}>
          AiPlanMyTrip uses artificial intelligence technology to enhance your travel
          planning experience. Our AI systems are designed to analyze travel
          preferences, destinations, and various data points to generate
          personalized recommendations and itineraries.
        </Text>

        <Text style={styles.section}>Nature of AI-Generated Content</Text>
        <Text style={styles.paragraph}>
          The travel recommendations, itineraries, and suggestions provided by
          our AI system are:
        </Text>
        <Text style={styles.bulletPoint}>
          • Generated automatically based on available data
        </Text>
        <Text style={styles.bulletPoint}>
          • Intended as suggestions rather than definitive guidance
        </Text>
        <Text style={styles.bulletPoint}>
          • Subject to limitations and potential inaccuracies
        </Text>
        <Text style={styles.bulletPoint}>
          • Updated regularly but may not reflect real-time changes
        </Text>

        <Text style={styles.section}>Limitations</Text>
        <Text style={styles.paragraph}>
          While we strive for accuracy and reliability, please be aware that:
        </Text>
        <Text style={styles.bulletPoint}>
          • AI recommendations may not account for all personal preferences or
          circumstances
        </Text>
        <Text style={styles.bulletPoint}>
          • Travel conditions and availability may change without notice
        </Text>
        <Text style={styles.bulletPoint}>
          • Local regulations and requirements should be verified independently
        </Text>

        <Text style={styles.section}>User Responsibility</Text>
        <Text style={styles.paragraph}>Users should:</Text>
        <Text style={styles.bulletPoint}>
          • Verify all important travel details independently
        </Text>
        <Text style={styles.bulletPoint}>
          • Use personal judgment when following AI recommendations
        </Text>
        <Text style={styles.bulletPoint}>
          • Be aware that actual experiences may differ from AI predictions
        </Text>

        <Text style={styles.section}>Continuous Improvement</Text>
        <Text style={styles.paragraph}>
          Our AI systems are continuously learning and improving. We regularly
          update our models to provide better recommendations, but this also
          means that results may vary over time.
        </Text>

        <Text style={styles.section}>Feedback</Text>
        <Text style={styles.paragraph}>
          We value your feedback on our AI-generated recommendations. Please
          report any inaccuracies or suggestions to help us improve our service
          at <EMAIL>
        </Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  lastUpdated: {
    fontSize: 14,
    color: "#666",
    marginBottom: 24,
    fontStyle: "italic",
  },
  section: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1a1a1a",
    marginTop: 24,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginBottom: 16,
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginLeft: 16,
    marginBottom: 8,
  },
});
