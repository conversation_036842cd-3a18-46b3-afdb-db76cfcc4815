import React from "react";
import { ScrollView, Text, StyleSheet, View } from "react-native";
import ScreenHeader from "../ui/common/ScreenHeader";

export default function Terms() {
  return (
    <View style={styles.container}>
      <ScreenHeader
        title="Terms of Use"
        subtitle="Agreement and conditions"
        showBackButton
      />
      <ScrollView style={styles.content}>
        <Text style={styles.lastUpdated}>Last updated: March 2024</Text>

        <Text style={styles.section}>1. Acceptance of Terms</Text>
        <Text style={styles.paragraph}>
          By accessing or using AiPlanMyTrip, you agree to be bound by these Terms of
          Use. If you do not agree to these terms, please do not use the app.
        </Text>

        <Text style={styles.section}>2. App Usage</Text>
        <Text style={styles.paragraph}>
          AiPlanMyTrip is a travel planning application that uses AI technology to
          assist in creating personalized travel itineraries. While we strive
          for accuracy, all recommendations are suggestions and should be
          verified independently.
        </Text>

        <Text style={styles.section}>3. User Accounts</Text>
        <Text style={styles.paragraph}>
          You are responsible for maintaining the confidentiality of your
          account credentials and for all activities under your account. You
          must provide accurate and complete information when creating an
          account.
        </Text>

        <Text style={styles.section}>4. AI-Generated Content</Text>
        <Text style={styles.paragraph}>
          Our app utilizes AI to generate travel recommendations and
          itineraries. While we strive for accuracy, we cannot guarantee the
          completeness or reliability of AI-generated content. Users should
          exercise judgment and verify important details independently.
        </Text>

        <Text style={styles.section}>5. Intellectual Property</Text>
        <Text style={styles.paragraph}>
          All content and functionality in the app, including but not limited to
          text, graphics, logos, and software, is the property of AiPlanMyTrip and is
          protected by intellectual property laws.
        </Text>

        <Text style={styles.section}>6. Limitation of Liability</Text>
        <Text style={styles.paragraph}>
          AiPlanMyTrip is not liable for any damages arising from the use or inability
          to use our services, including damages caused by reliance on
          AI-generated recommendations.
        </Text>

        <Text style={styles.section}>7. Modifications</Text>
        <Text style={styles.paragraph}>
          We reserve the right to modify these terms at any time. Continued use
          of the app after changes constitutes acceptance of the modified terms.
        </Text>

        <Text style={styles.section}>Contact</Text>
        <Text style={styles.paragraph}>
          For questions about these terms, please contact <NAME_EMAIL>
        </Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  lastUpdated: {
    fontSize: 14,
    color: "#666",
    marginBottom: 24,
    fontStyle: "italic",
  },
  section: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1a1a1a",
    marginTop: 24,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginBottom: 16,
  },
});
