import { Ionicons } from "@expo/vector-icons";
import Constants from "expo-constants";
import { router } from "expo-router";
import React from "react";
import {
  Alert,
  Linking,
  Platform,
  SafeAreaView,
  ScrollView,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useAuth } from "../lib/hooks/use-auth";
import { useDaysBalance } from "../lib/hooks/use-days-balance";
import ScreenHeader from "../ui/common/ScreenHeader";

const APP_VERSION = Constants.expoConfig?.version || "1.0.0";

interface SettingsSectionProps {
  title: string;
  children: React.ReactNode;
}

const SettingsSection = ({ title, children }: SettingsSectionProps) => (
  <View style={styles.section}>
    <Text style={styles.sectionTitle}>{title}</Text>
    <View style={styles.sectionContent}>{children}</View>
  </View>
);

interface SettingsItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  onPress: () => void;
  showChevron?: boolean;
  textColor?: string;
}

const SettingsItem = ({
  icon,
  title,
  onPress,
  showChevron = true,
  textColor,
}: SettingsItemProps) => (
  <TouchableOpacity style={styles.settingsItem} onPress={onPress}>
    <View style={styles.settingsItemLeft}>
      <Ionicons
        name={icon}
        size={22}
        color={textColor || "#666"}
        style={styles.settingsItemIcon}
      />
      <Text
        style={[styles.settingsItemText, textColor && { color: textColor }]}
      >
        {title}
      </Text>
    </View>
    {showChevron && <Ionicons name="chevron-forward" size={20} color="#999" />}
  </TouchableOpacity>
);

export default function Settings() {
  const { auth, logout } = useAuth();
  const { balance, getTotalAvailableDays } = useDaysBalance();
  const isLoggedIn = auth.status === "authenticated";

  const handleLogout = async () => {
    try {
      Alert.alert("Logout", "Are you sure you want to logout?", [
        { text: "Cancel", style: "cancel" },
        {
          text: "Logout",
          onPress: async () => {
            await logout();
            router.replace("/");
          },
        },
      ]);
    } catch {
      Alert.alert("Error", "Failed to logout. Please try again.");
    }
  };

  const handleRateApp = () => {
    // Replace with actual App Store/Play Store links based on platform
    const storeUrl = Platform.select({
      ios: "https://apps.apple.com/app/your-app-id",
      android: "https://play.google.com/store/apps/details?id=your.app.id",
    });
    if (storeUrl) Linking.openURL(storeUrl);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Settings"
        subtitle="Manage your preferences"
        showBackButton
      />
      <ScrollView style={styles.scrollView}>
        {isLoggedIn ? (
          <>
            <SettingsSection title="Account">
              <SettingsItem
                icon="person-circle"
                title="My Profile"
                onPress={() => router.push("/account")}
              />
              <SettingsItem
                icon="log-out"
                title="Sign Out"
                onPress={handleLogout}
              />
            </SettingsSection>

            <SettingsSection title="My Subscription">
              <SettingsItem
                icon="calendar"
                title="Days Dashboard"
                onPress={() => router.push("/credits-dashboard")}
              />
              <View style={styles.creditInfoContainer}>
                <View style={styles.creditBalanceRow}>
                  <Text style={styles.creditBalanceLabel}>
                    Available Days:
                  </Text>
                  <Text style={styles.creditBalanceValue}>
                    {balance ? getTotalAvailableDays().toLocaleString() : "Loading..."}
                  </Text>
                </View>
                {balance?.currentPlan && (
                  <View style={styles.creditBalanceRow}>
                    <Text style={styles.creditBalanceLabel}>
                      Current Plan:
                    </Text>
                    <Text style={styles.creditBalanceValue}>
                      {balance.currentPlan === 'free' ? 'Free' :
                        balance.currentPlan === 'pro' ? 'Pro' :
                          balance.currentPlan === 'premium' ? 'Premium' :
                            balance.currentPlan}
                    </Text>
                  </View>
                )}
                <TouchableOpacity
                  style={styles.getMoreButton}
                  onPress={() => router.push("/buy-credits")}
                >
                  <Text style={styles.getMoreButtonText}>Manage Plan</Text>
                </TouchableOpacity>
              </View>
            </SettingsSection>
          </>
        ) : (
          <SettingsSection title="Account">
            <SettingsItem
              icon="log-in"
              title="Sign In"
              onPress={() => router.push("/login")}
            />
          </SettingsSection>
        )}

        <SettingsSection title="Support">
          <SettingsItem
            icon="help-circle-outline"
            title="Help Center"
            onPress={() => Linking.openURL("https://aiplanmytrip.com/help")}
          />
          <SettingsItem
            icon="mail-outline"
            title="Contact Us"
            onPress={() => router.push("/contact-us")}
          />
          <SettingsItem
            icon="share-social"
            title="Share AiPlanMyTrip"
            onPress={async () => {
              try {
                await Share.share({
                  message:
                    "Check out AiPlanMyTrip - Your AI-powered travel companion!",
                  // Add store URL here when available
                });
              } catch (error) {
                console.error("Error sharing:", error);
              }
            }}
          />
          <SettingsItem icon="star" title="Rate Us" onPress={handleRateApp} />
        </SettingsSection>

        <SettingsSection title="Legal">
          <SettingsItem
            icon="document-text"
            title="Terms of Use"
            onPress={() => router.push("/terms")}
          />
          <SettingsItem
            icon="shield-checkmark"
            title="Privacy Policy"
            onPress={() => Linking.openURL("https://www.aiplanmytrip.com/privacy")}
          />
          <SettingsItem
            icon="document-outline"
            title="EULA"
            onPress={() => Linking.openURL("https://www.aiplanmytrip.com/eula")}
          />
          <SettingsItem
            icon="information-circle"
            title="AI Disclaimer"
            onPress={() => router.push("/ai-disclaimer")}
          />
        </SettingsSection>

        {isLoggedIn && (
          <SettingsSection title="Danger Zone">
            <SettingsItem
              icon="trash"
              title="Delete Account"
              onPress={() => router.push("/delete-account")}
              textColor="#FF3B30"
            />
          </SettingsSection>
        )}

        <SettingsSection title="About">
          <View style={styles.appInfo}>
            <Text style={styles.appName}>AiPlanMyTrip</Text>
            <Text style={styles.version}>Version {APP_VERSION}</Text>
          </View>
        </SettingsSection>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: "600",
    color: "#666",
    textTransform: "uppercase",
    marginBottom: 8,
  },
  sectionContent: {
    backgroundColor: "#fff",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  appInfo: {
    padding: 16,
    alignItems: "center",
  },
  appName: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  version: {
    fontSize: 15,
    color: "#666",
  },
  settingsItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  settingsItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingsItemIcon: {
    marginRight: 12,
  },
  settingsItemText: {
    fontSize: 16,
    color: "#1a1a1a",
  },
  creditInfoContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.05)",
  },
  creditBalanceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  creditBalanceLabel: {
    fontSize: 14,
    color: "#666",
  },
  creditBalanceValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2196F3",
  },
  getMoreButton: {
    backgroundColor: "#E3F2FD",
    borderRadius: 8,
    padding: 8,
    alignItems: "center",
  },
  getMoreButtonText: {
    color: "#2196F3",
    fontSize: 14,
    fontWeight: "600",
  },
});
