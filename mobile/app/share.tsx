import { useLocalSearchParams } from "expo-router";
import { SafeAreaView, StyleSheet, Text, View } from "react-native";
import { useTrip } from "../lib/hooks/use-trip";
import { useShareHandlers } from "../lib/hooks/useShareHandlers";
import { useShareUrl } from "../lib/hooks/useShareUrl";
import ScreenHeader from "../ui/common/ScreenHeader";
import { KeyboardAvoidingWrapper } from "../ui/common/KeyboardAvoidingWrapper";
import { EmailShareForm } from "../ui/share/EmailShareForm";
import { ErrorView } from "../ui/share/ErrorView";
import { LoadingView } from "../ui/share/LoadingView";
import { ShareLinkButton } from "../ui/share/ShareLinkButton";
import { TripPreviewCard } from "../ui/share/TripPreviewCard";

/**
 * Share screen component
 * Allows users to share their trip via various methods
 */
export const ShareScreen = () => {
  // Get trip ID from route params
  const { tripId } = useLocalSearchParams<{ tripId: string }>();

  // Fetch trip data
  const { trip, loading: isLoading, error, loadTrip } = useTrip({ tripId });

  // Generate share URL
  const shareUrl = useShareUrl({ trip, tripId });

  // Get share handlers
  const { email, setEmail, handleShareLink, handleEmailShare } =
    useShareHandlers({ trip, shareUrl });

  // Show loading state
  if (isLoading) {
    return <LoadingView />;
  }

  // Show error state
  if (error) {
    return <ErrorView error={error.message} onRetry={loadTrip} />;
  }

  // Main content
  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Share Your Trip"
        subtitle="Share your itinerary with friends or export it for your records"
      />

      <KeyboardAvoidingWrapper
        contentContainerStyle={styles.content}
        keyboardVerticalOffset={100}
      >
        <ShareLinkButton shareUrl={shareUrl} onShareLink={handleShareLink} />
        <View style={styles.divider} />

        <Text style={styles.title}>Trip Preview</Text>
        {trip && <TripPreviewCard trip={trip} />}

        <View style={styles.divider} />

        <EmailShareForm
          email={email}
          setEmail={setEmail}
          onEmailShare={handleEmailShare}
        />
      </KeyboardAvoidingWrapper>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  divider: {
    height: 1,
    backgroundColor: "#EEEEEE",
    marginVertical: 20,
  },
});

// Default export for backward compatibility
export default ShareScreen;
