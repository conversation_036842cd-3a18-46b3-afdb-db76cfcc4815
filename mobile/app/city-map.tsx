import React, { useCallback, useEffect, useState, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Activity } from "../lib/types";
import { TripService } from "../lib/services/trip.service";
import { MapRegionService } from "../lib/services/map-region.service";
import TripMapView from "../ui/map/TripMapView";
import ScreenHeader from "../ui/common/ScreenHeader";
import { useActivityCompletion } from "../ui/trips/ActivityManager";
import ErrorState from "../ui/common/ErrorState";

// Import Region type from react-native-maps for type definitions
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};

const tripService = TripService.getInstance();
const mapRegionService = MapRegionService.getInstance();


export default function CityMapScreen() {
  const { tripId, cityName } = useLocalSearchParams<{
    tripId: string;
    cityName: string;
  }>();

  const { top } = useSafeAreaInsets();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mapRegion, setMapRegion] = useState<Region | null>(null);

  const fetchCityActivities = useCallback(async () => {
    if (!tripId || !cityName) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch all activities for this city across all days
      const result = await tripService.fetchActivitiesByCity(tripId, cityName);

      // Extract activities from the city result
      const cityActivities = result[cityName.toLowerCase()] || [];

      // Filter out activities without coordinates as they can't be shown on map
      const mappableActivities = cityActivities.filter(
        (activity: Activity) => activity.coordinates
      );

      setActivities(mappableActivities);
    } catch (err) {
      console.error("Error fetching city activities:", err);
      setError("Failed to load activities for this city");
    } finally {
      setLoading(false);
    }
  }, [tripId, cityName]);

  // Lightweight refresh that only updates activity completion status without affecting map
  const refreshActivityCompletion = useCallback(async () => {
    if (!tripId || !cityName) return;

    try {
      // Fetch all activities for this city across all days
      const result = await tripService.fetchActivitiesByCity(tripId, cityName);

      // Extract activities from the city result
      const cityActivities = result[cityName.toLowerCase()] || [];

      // Filter out activities without coordinates as they can't be shown on map
      const mappableActivities = cityActivities.filter(
        (activity: Activity) => activity.coordinates
      );

      // Only update if the activities have actually changed (to prevent unnecessary re-renders)
      setActivities(prevActivities => {
        // Compare activities by their completion status and other properties
        const hasChanged = prevActivities.length !== mappableActivities.length ||
          prevActivities.some((prevActivity, index) => {
            const newActivity = mappableActivities[index];
            return !newActivity ||
              prevActivity.id !== newActivity.id ||
              prevActivity.completed !== newActivity.completed;
          });

        return hasChanged ? mappableActivities : prevActivities;
      });
    } catch (err) {
      console.error("Error refreshing activity completion:", err);
      // Don't show error for background refresh
    }
  }, [tripId, cityName]);

  // Handle activity completion
  const {
    completedActivities,
    activityInProgress,
    progressAnimation,
    completionAnimation,
    handleLongPress,
    handlePressOut,
    toggleActivityCompletion,
  } = useActivityCompletion({
    tripId: tripId as string,
    onTripUpdate: refreshActivityCompletion, // Lightweight refresh that preserves map state
  });

  // Fetch map region for the city
  const fetchCityMapRegion = useCallback(async () => {
    if (!tripId || !cityName) return;

    try {
      // Use backend to calculate the optimal region for this city
      const region = await mapRegionService.getCityMapRegion(tripId, cityName);
      if (region) {
        setMapRegion(region);
      } else {
        // Fallback to default region if backend fails
        setMapRegion({
          latitude: 0,
          longitude: 0,
          latitudeDelta: 50,
          longitudeDelta: 50,
        });
      }
    } catch (error) {
      console.error('Error fetching city map region:', error);
      // Fallback to default region on error
      setMapRegion({
        latitude: 0,
        longitude: 0,
        latitudeDelta: 50,
        longitudeDelta: 50,
      });
    }
  }, [tripId, cityName]);

  // Handle map region change
  const handleRegionChangeComplete = useCallback((region: Region) => {
    setMapRegion(region);
  }, []);

  useEffect(() => {
    fetchCityActivities();
    fetchCityMapRegion();
  }, [fetchCityActivities, fetchCityMapRegion]);

  // Memoize map props to prevent unnecessary re-renders
  const mapProps = useMemo(
    () => ({
      mapRegion: mapRegion!,
      activities,
      completedActivities,
      activityInProgress,
      progressAnimation,
      completionAnimation,
      tripId: tripId as string,
      selectedDay: 0, // Show all days for city view
      handleLongPress,
      handlePressOut,
      toggleActivityCompletion,
      onRegionChangeComplete: handleRegionChangeComplete,
    }),
    [
      mapRegion,
      activities,
      completedActivities,
      activityInProgress,
      progressAnimation,
      completionAnimation,
      tripId,
      handleLongPress,
      handlePressOut,
      toggleActivityCompletion,
      handleRegionChangeComplete,
    ],
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: top }]}>
        <ScreenHeader
          title="City Map"
          subtitle={cityName?.toUpperCase()}
          showBackButton
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading city activities...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: top }]}>
        <ScreenHeader
          title="City Map"
          subtitle={cityName?.toUpperCase()}
          showBackButton
        />
        <ErrorState
          error={error}
          onRetry={() => {
            setError(null);
            fetchCityActivities();
            fetchCityMapRegion();
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: top }]}>
      <ScreenHeader
        title="City Map"
        subtitle={cityName?.toUpperCase()}
        showBackButton
      />
      <View style={styles.mapContainer}>
        {mapRegion && <TripMapView {...mapProps} />}

        {/* Activities List Button */}
        <TouchableOpacity
          style={styles.activitiesButton}
          onPress={() => {
            router.push({
              pathname: "/activities-list",
              params: {
                context: "city",
                title: `${cityName?.toUpperCase()} Activities`,
                subtitle: `${activities.length} ${activities.length === 1 ? 'activity' : 'activities'}`,
                tripId: tripId as string,
                cityName: cityName as string,
              },
            });
          }}
        >
          <Ionicons name="list" size={24} color="#FFF" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  mapContainer: {
    flex: 1,
  },
  activitiesButton: {
    position: "absolute",
    bottom: 32,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
});
