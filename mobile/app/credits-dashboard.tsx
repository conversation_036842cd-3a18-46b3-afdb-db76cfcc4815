import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useEffect } from "react";
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useDaysBalance } from "../lib/hooks/use-days-balance";
import ScreenHeader from "../ui/common/ScreenHeader";
import { CurrentPlanCard } from "../ui/subscription/CurrentPlanCard";

import { mediumHapticFeedback } from "../lib/utils/haptics";

export default function DaysDashboardScreen() {
  const { balance, isLoading, error, getTotalAvailableDays } = useDaysBalance();

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Calculate percentage of days used
  const calculateUsagePercentage = () => {
    if (!balance) return 0;
    const totalAdded = balance.totalDaysAdded || 0;
    if (totalAdded === 0) return 0;
    const used = balance.totalDaysUsed || 0;
    return Math.min(Math.round((used / totalAdded) * 100), 100);
  };

  const usagePercentage = calculateUsagePercentage();
  const isLowBalance = balance && getTotalAvailableDays() < 5;

  const handleViewBillingHistory = () => {
    mediumHapticFeedback();
    router.push("/usage-history");
  };

  const handleRestorePurchases = async () => {
    mediumHapticFeedback();

    try {
      // This would integrate with the existing restore purchases functionality
      Alert.alert(
        "Restore Purchases",
        "This will restore your previous purchases from the App Store or Google Play.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Restore",
            onPress: async () => {
              // Add actual restore logic here
              Alert.alert("Info", "Restore purchases functionality will be implemented here.");
            }
          }
        ]
      );
    } catch {
      Alert.alert("Error", "Failed to restore purchases. Please try again.");
    }
  };

  useEffect(() => {
    if (error) {
      Alert.alert("Error", error);
    }
  }, [error]);

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="My Days Balance"
        subtitle="View and manage your subscription"
        showBackButton
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
      >
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2196F3" />
            <Text style={styles.loadingText}>Loading your balance...</Text>
          </View>
        ) : (
          <>
            {/* Current Plan Card */}
            {balance && (
              <CurrentPlanCard
                balance={balance}
              />
            )}

            {/* Quick Stats Card */}
            <View style={styles.statsCard}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {balance ? formatNumber(getTotalAvailableDays()) : "0"}
                </Text>
                <Text style={styles.statLabel}>Available Days</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {balance ? formatNumber(balance.totalDaysUsed || 0) : "0"}
                </Text>
                <Text style={styles.statLabel}>Days Used</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{usagePercentage}%</Text>
                <Text style={styles.statLabel}>Usage</Text>
              </View>
            </View>

            {isLowBalance && (
              <View style={styles.warningContainer}>
                <Ionicons name="warning" size={20} color="#FF9800" />
                <Text style={styles.warningText}>
                  Your days are running low! Upgrade your plan or buy day refills to continue.
                </Text>
              </View>
            )}

            {/* Main Actions */}

            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push("/buy-credits")}
              >
                <View style={[styles.actionIconContainer, { backgroundColor: '#E3F2FD' }]}>
                  <Ionicons name="trending-up" size={24} color="#2196F3" />
                </View>
                <View style={styles.actionTextContainer}>
                  <Text style={styles.actionTitle}>
                    {balance?.currentPlan === 'free' ? 'Upgrade Plan' : 'Manage Plan'}
                  </Text>
                  <Text style={styles.actionSubtitle}>
                    {balance?.currentPlan === 'free'
                      ? 'Get more days with Pro or Premium'
                      : 'Change plan or buy day refills'}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#999" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleViewBillingHistory}
              >
                <View style={[styles.actionIconContainer, { backgroundColor: '#FFF3E0' }]}>
                  <Ionicons name="receipt" size={24} color="#FF9800" />
                </View>
                <View style={styles.actionTextContainer}>
                  <Text style={styles.actionTitle}>Payment & Usage History</Text>
                  <Text style={styles.actionSubtitle}>View your billing and usage history</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#999" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleRestorePurchases}
              >
                <View style={[styles.actionIconContainer, { backgroundColor: '#E8F5E8' }]}>
                  <Ionicons name="refresh" size={24} color="#4CAF50" />
                </View>
                <View style={styles.actionTextContainer}>
                  <Text style={styles.actionTitle}>Restore Purchases</Text>
                  <Text style={styles.actionSubtitle}>Restore previous purchases</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#999" />
              </TouchableOpacity>


            </View>

            <View style={styles.infoCard}>
              <Text style={styles.infoTitle}>About Days & Subscriptions</Text>
              <Text style={styles.infoText}>
                Each trip generation uses 1 day from your balance. Subscription days reset monthly on your billing date.
                Day refill packs expire at the end of your current billing cycle, so use them before your subscription renews.
              </Text>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  balanceCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  balanceHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  balanceTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#666",
    marginLeft: 8,
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: "700",
    color: "#2196F3",
    marginBottom: 4,
  },
  balanceSubtitle: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
  },
  balanceBreakdown: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  breakdownItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  breakdownLabel: {
    fontSize: 14,
    color: "#666",
  },
  breakdownValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: "#E3F2FD",
    borderRadius: 4,
    overflow: "hidden",
    marginBottom: 8,
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#2196F3",
    borderRadius: 4,
  },
  progressBarWarning: {
    backgroundColor: "#FF9800",
  },
  progressText: {
    fontSize: 12,
    color: "#666",
  },
  warningContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF8E1",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    color: "#FF9800",
    marginLeft: 8,
    flex: 1,
  },
  refreshButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 8,
  },
  refreshText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  rotating: {
    transform: [{ rotate: "45deg" }],
  },
  actionsContainer: {
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  actionSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  infoCard: {
    backgroundColor: "#E3F2FD",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2196F3",
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: "#333",
    lineHeight: 20,
  },
  statsCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#2196F3',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 16,
  },
  subscriptionActionsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  subscriptionAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
});
