import { Ionicons } from "@expo/vector-icons";
import { useLocalSearchParams, router } from "expo-router";
import { useMemo } from "react";
import { SafeAreaView, StyleSheet, TouchableOpacity, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useTrip } from "../lib/hooks/use-trip";
import { useDailyItinerary } from "../lib/hooks/useDailyItinerary";
import { useDailyMapRegion } from "../lib/hooks/useDailyMapRegion";

import { useActivityCompletion } from "../ui/trips/ActivityManager";
import ErrorState from "../ui/common/ErrorState";
import ScreenHeader from "../ui/common/ScreenHeader";
import TripMapView from "../ui/map/TripMapView";

/**
 * Daily itinerary screen
 * Shows a map with activities for a specific day
 */
export default function DailyItineraryScreen() {
  // Get route params
  const { day, tripId } = useLocalSearchParams<{
    day: string;
    tripId: string;
  }>();

  // Get safe area insets
  const { top } = useSafeAreaInsets();

  // Fetch trip data
  const { trip, error, loadTrip } = useTrip({ tripId });

  // Get activities for the current day
  const { activities, dayNumber } = useDailyItinerary({
    day,
    itinerary: trip?.itinerary,
  });

  // Handle map region
  const { mapRegion, handleRegionChangeComplete } = useDailyMapRegion({
    tripId: tripId as string,
    day,
    destination: trip?.tripDetails?.destination,
  });



  // Handle activity completion
  const {
    completedActivities,
    activityInProgress,
    progressAnimation,
    completionAnimation,
    handleLongPress,
    handlePressOut,
    toggleActivityCompletion,
  } = useActivityCompletion({
    tripId: tripId as string,
    onTripUpdate: loadTrip,
  });



  // Memoize map props to prevent unnecessary re-renders
  const mapProps = useMemo(
    () => ({
      mapRegion: mapRegion!,
      activities,
      completedActivities,
      activityInProgress,
      progressAnimation,
      completionAnimation,
      tripId: tripId as string,
      selectedDay: dayNumber as number,
      handleLongPress,
      handlePressOut,
      toggleActivityCompletion,
      onRegionChangeComplete: handleRegionChangeComplete,
    }),
    [
      mapRegion,
      activities,
      completedActivities,
      activityInProgress,
      progressAnimation,
      completionAnimation,
      tripId,
      dayNumber,
      handleLongPress,
      handlePressOut,
      toggleActivityCompletion,
      handleRegionChangeComplete,
    ],
  );

  const cities = useMemo(() => {
    if (!activities) return "";
    return Array.from(new Set(activities.map((activity) => activity.city)))
      .filter(Boolean)
      .sort()
      .join(", ");
  }, [activities]);

  // Show error state if there's an error
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          title={`Day ${day} Itinerary`}
          subtitle={`${trip?.name}`}
          showBackButton
        />
        <ErrorState error={error.message} onRetry={loadTrip} />
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.mapContainer, { paddingTop: top }]}>
        <ScreenHeader
          title={`Day ${day} Itinerary`}
          subtitle={cities}
          showBackButton
        />

        {mapRegion && <TripMapView {...mapProps} />}

        {/* Activities List Button */}
        <TouchableOpacity
          style={styles.activitiesButton}
          onPress={() => {
            router.push({
              pathname: "/activities-list",
              params: {
                context: "timeline",
                title: `Day ${day} Activities`,
                subtitle: `${activities.length} ${activities.length === 1 ? 'activity' : 'activities'}`,
                tripId: tripId as string,
                day: day as string,
              },
            });
          }}
        >
          <Ionicons name="list" size={24} color="#FFF" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  mapContainer: {
    flex: 1,
  },

  activitiesButton: {
    position: "absolute",
    bottom: 32,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
