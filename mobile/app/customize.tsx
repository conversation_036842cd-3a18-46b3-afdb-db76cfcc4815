import { useLocalSearchParams } from "expo-router";
import React, { Fragment, useState } from "react";
import {
  ActivityIndicator,
  SafeAreaView,
  StyleSheet,
  View,
} from "react-native";
import { useTrip } from "../lib/hooks/use-trip";
import ErrorState from "../ui/common/ErrorState";
import ScreenHeader from "../ui/common/ScreenHeader";
import CustomizeItinerary from "../ui/itinerary/CustomizeItinerary";
import { useSafeAreaInsets } from "react-native-safe-area-context";
export default function CustomizeScreen() {
  const { tripId } = useLocalSearchParams<{ tripId: string }>();
  const { trip, loading, error, loadTrip } = useTrip({ tripId });
  const [selectedDay] = useState<number>(1);
  const { top } = useSafeAreaInsets();

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          title="Customize"
          subtitle="Customize your itinerary"
          showBackButton
        />
        <ActivityIndicator size="large" color="#000" />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          title="Customize"
          subtitle="Customize your itinerary"
          showBackButton
        />
        <ErrorState error={error.message} onRetry={loadTrip} />
      </SafeAreaView>
    );
  }

  const activities = trip?.itinerary[selectedDay - 1].activities;
  return (
    <Fragment>
      <View style={[styles.container, { paddingTop: top }]}>
        <ScreenHeader
          title="Customize"
          subtitle="Preview your itinerary, Press any activity to build your own itinerary"
          showBackButton
        />
      </View>
      <CustomizeItinerary
        activities={activities ?? []}
        selectedDay={selectedDay}
      />
    </Fragment>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: 0,
    right: 0,
    width: "100%",
    backgroundColor: "rgba(220, 255, 220, 0.65)",
    padding: 10,
    borderRadius: 10,
    zIndex: 1,
  },
});
