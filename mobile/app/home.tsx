import { useMemo, useRef, useState, useEffect } from "react";
import { SafeAreaView, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useRefreshOnFocus } from "../lib/hooks/use-refresh-on-focus";
import { useFilteredTrips } from "../lib/hooks/useFilteredTrips";
import { useHomeState } from "../lib/hooks/useHomeState";
import { useMapRegion } from "../lib/hooks/useMapRegion";
import { useTripActions } from "../lib/hooks/useTripActions";
import ErrorState from "../ui/common/ErrorState";
import FabButton from "../ui/common/FabButton";
import ScreenHeader from "../ui/common/ScreenHeader";
import SettingsLink from "../ui/common/SettingsLink";
import { TutorialProvider } from "../ui/common/TutorialManager";

import { FilterChips } from "../ui/home/<USER>";
import HomeTutorial from "../ui/home/<USER>";
import { MapViewComponent } from "../ui/home/<USER>";
import { RemainingDaysTracker } from "../ui/home/<USER>";
import { SearchBar } from "../ui/home/<USER>";
import { TripList } from "../ui/home/<USER>";
import { ViewToggle } from "../ui/home/<USER>";

/**
 * Home screen component
 * Shows a list of trips or a map view
 */
export default function HomeScreen() {
  const { top } = useSafeAreaInsets();

  // Refs for tutorial targets
  const fabButtonRef = useRef(null);
  const viewToggleRef = useRef(null);

  // State to track when the screen is ready for tutorials
  const [tutorialReady, setTutorialReady] = useState(false);

  // Custom hooks for state management
  const {
    viewMode,
    toggleAnimation,
    isZoomedOut,
    searchQuery,
    filters,
    setIsZoomedOut,
    setSearchQuery,
    toggleViewMode,
    toggleFilter,
    clearFilters,
    groupTripsByCountry,
  } = useHomeState();

  // Set tutorial ready after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setTutorialReady(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Use the filtered trips hook with the current filters
  const {
    trips,
    loading,
    error,
    loadFilteredTrips: loadTrips,
  } = useFilteredTrips({
    filters: {
      ...filters,
      searchQuery,
    },
    enabled: true,
  });

  const { mapRegion, setMapRegion } = useMapRegion(null);

  const {
    handleTripSelect,
    handleDeleteTrip,
    handleToggleFavorite,
    navigateToTripCreation,
  } = useTripActions(loadTrips);

  // Refresh trips when screen comes into focus
  useRefreshOnFocus(loadTrips);

  // Group trips by country - memoized to prevent unnecessary recalculations
  const tripsByCountry = useMemo(
    () => groupTripsByCountry(trips),
    [trips, groupTripsByCountry],
  );

  // Render content with tutorial provider
  const renderContent = () => {
    // Common content to render
    const content = (
      <>
        {/* Only render the tutorial component once */}
        <HomeTutorial
          fabButtonRef={fabButtonRef}
          viewToggleRef={viewToggleRef}
          isReady={tutorialReady}
        />
      </>
    );

    // Error state
    if (error) {
      return (
        <SafeAreaView style={styles.container}>
          <ScreenHeader
            title="My Trips"
            subtitle="Here are your saved trips"
            showBackButton={false}
            rightComponent={
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 16 }}
              >
                <RemainingDaysTracker />
                <ViewToggle
                  ref={viewToggleRef}
                  viewMode={viewMode}
                  toggleAnimation={toggleAnimation}
                  onPress={toggleViewMode}
                />
                <SettingsLink />
              </View>
            }
          />
          <ErrorState error={error.message} onRetry={loadTrips} />
          <FabButton ref={fabButtonRef} onPress={navigateToTripCreation} />
          {content}
        </SafeAreaView>
      );
    }

    // Map view
    if (viewMode === "map") {
      return (
        <View style={[styles.container, { paddingTop: top }]}>
          <ScreenHeader
            title="My Trips"
            subtitle="Here are your saved trips"
            showBackButton={false}
            rightComponent={
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 16 }}
              >
                <RemainingDaysTracker />
                <ViewToggle
                  ref={viewToggleRef}
                  viewMode={viewMode}
                  toggleAnimation={toggleAnimation}
                  onPress={toggleViewMode}
                />
                <SettingsLink />
              </View>
            }
          />
          <View style={styles.mapContainer}>
            <MapViewComponent
              mapRegion={mapRegion}
              setMapRegion={setMapRegion}
              isZoomedOut={isZoomedOut}
              setIsZoomedOut={setIsZoomedOut}
              tripsByCountry={tripsByCountry}
            />
          </View>

          <FabButton ref={fabButtonRef} onPress={navigateToTripCreation} />
          {content}
        </View>
      );
    }

    // List view
    return (
      <View style={[styles.container, { paddingTop: top }]}>
        <ScreenHeader
          title="My Trips"
          subtitle="Here are your saved trips"
          showBackButton={false}
          rightComponent={
            <View
              style={{ flexDirection: "row", alignItems: "center", gap: 16 }}
            >
              <RemainingDaysTracker />
              <ViewToggle
                ref={viewToggleRef}
                viewMode={viewMode}
                toggleAnimation={toggleAnimation}
                onPress={toggleViewMode}
              />
              <SettingsLink />
            </View>
          }
        />
        <View style={styles.content}>
          <SearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />
          <FilterChips
            filters={filters}
            toggleFilter={toggleFilter}
            clearFilters={clearFilters}
          />
          <TripList
            tripsByCountry={tripsByCountry}
            loading={loading}
            loadTrips={loadTrips}
            onTripPress={(trip) => handleTripSelect("list")(trip._id)}
            onTripDelete={handleDeleteTrip}
            onToggleFavorite={handleToggleFavorite}
            allTrips={trips}
          />

          <FabButton ref={fabButtonRef} onPress={navigateToTripCreation} />
          {content}
        </View>
      </View>
    );
  };

  // Wrap with tutorial provider
  return <TutorialProvider>{renderContent()}</TutorialProvider>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    flexDirection: "column",
  },
  mapContainer: {
    flex: 1,
    position: "relative",
  },
});
