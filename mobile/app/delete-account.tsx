import { router } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import ScreenHeader from "../ui/common/ScreenHeader";

export default function DeleteAccount() {
  const [confirmation, setConfirmation] = useState("");
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);

  const handleDeleteAccount = async () => {
    if (confirmation.toLowerCase() !== "delete") {
      Alert.alert("Error", 'Please type "delete" to confirm');
      return;
    }

    if (!showFinalConfirmation) {
      setShowFinalConfirmation(true);
      return;
    }

    try {
      // TODO: Implement account deletion logic
      Alert.alert(
        "Account Deleted",
        "Your account has been successfully deleted. We're sorry to see you go.",
        [{ text: "OK", onPress: () => router.replace("/") }],
      );
    } catch {
      Alert.alert("Error", "Failed to delete account. Please try again.");
    }
  };

  return (
    <View style={styles.container}>
      <ScreenHeader
        title="Delete Account"
        subtitle="We're sorry to see you go"
        showBackButton
      />

      <View style={styles.content}>
        <View style={styles.warningBox}>
          <Text style={styles.warningTitle}>⚠️ Warning</Text>
          <Text style={styles.warningText}>
            Deleting your account is permanent and cannot be undone. This will:
          </Text>
          <Text style={styles.bulletPoint}>
            • Delete all your saved trips and itineraries
          </Text>
          <Text style={styles.bulletPoint}>
            • Remove all your preferences and settings
          </Text>
          <Text style={styles.bulletPoint}>
            • Cancel any active subscriptions
          </Text>
          <Text style={styles.bulletPoint}>
            • Delete all your personal data
          </Text>
        </View>

        {!showFinalConfirmation ? (
          <>
            <Text style={styles.confirmationText}>
              To proceed, please type &lsquo;delete&rsquo; in the field below:
            </Text>
            <TextInput
              style={styles.input}
              value={confirmation}
              onChangeText={setConfirmation}
              placeholder="Type 'delete' to confirm"
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={[
                styles.deleteButton,
                { opacity: confirmation.toLowerCase() === "delete" ? 1 : 0.5 },
              ]}
              onPress={handleDeleteAccount}
              disabled={confirmation.toLowerCase() !== "delete"}
            >
              <Text style={styles.deleteButtonText}>Continue</Text>
            </TouchableOpacity>
          </>
        ) : (
          <>
            <Text style={styles.finalConfirmationText}>
              Are you absolutely sure you want to delete your account?
            </Text>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDeleteAccount}
            >
              <Text style={styles.deleteButtonText}>
                Yes, Delete My Account
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => router.back()}
            >
              <Text style={styles.cancelButtonText}>No, Keep My Account</Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    padding: 24,
  },
  warningBox: {
    backgroundColor: "rgba(255, 59, 48, 0.1)",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  warningTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#FF3B30",
    marginBottom: 8,
  },
  warningText: {
    fontSize: 16,
    color: "#1a1a1a",
    marginBottom: 12,
    lineHeight: 24,
  },
  bulletPoint: {
    fontSize: 16,
    color: "#1a1a1a",
    marginLeft: 8,
    marginBottom: 8,
    lineHeight: 24,
  },
  confirmationText: {
    fontSize: 16,
    color: "#1a1a1a",
    marginBottom: 16,
    lineHeight: 24,
  },
  input: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    marginBottom: 24,
    fontSize: 16,
  },
  deleteButton: {
    backgroundColor: "#FF3B30",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  finalConfirmationText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 24,
    textAlign: "center",
  },
  cancelButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 12,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "600",
  },
});
