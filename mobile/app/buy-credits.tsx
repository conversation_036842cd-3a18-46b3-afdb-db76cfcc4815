
import { Ionicons } from "@expo/vector-icons";
import {
  Alert,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View
} from "react-native";

import { useCallback, useMemo, useState } from "react";
import { useDaysBalance } from "../lib/hooks/use-days-balance";
import { usePayments } from "../lib/hooks/usePayments";
import ScreenHeader from "../ui/common/ScreenHeader";
import { InfoCard } from "../ui/credits/InfoCard";
import { LoadingView } from "../ui/credits/LoadingView";
import { PurchaseButton } from "../ui/credits/PurchaseButton";
import { RestorePurchasesButton } from "../ui/credits/RestorePurchasesButton";
import { CurrentPlanCard } from "../ui/subscription/CurrentPlanCard";
import { BackendDayRefillSelector } from "../ui/subscription/DayRefillCard";
import { SubscriptionPlanCard } from "../ui/subscription/SubscriptionPlanCard";
import { showBackToFreeConfirmation } from "../lib/utils/subscription-deeplinks";
// Helper function to determine plan order for upgrade/downgrade logic
const getPlanOrder = (planId: string): number => {
  const planOrder = { free: 0, pro: 1, premium: 2 };
  return planOrder[planId as keyof typeof planOrder] || 0;
};
/**
 * Subscription Screen
 * Allows users to purchase subscription plans and day refills
 */
export default function SubscriptionScreen() {
  // Initialize days balance
  const { balance, fetchBalance } = useDaysBalance();

  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  const {
    isLoading,
    productConfigs,
    purchaseProduct,
    purchaseSubscription,
    restorePurchases,
  } = usePayments(fetchBalance);

  const handleSelectPackage = useCallback((packageId: string, days?: number, price?: number) => {
    setSelectedPackage(packageId);
  }, []);

  const handlePurchase = useCallback(async () => {
    if (!selectedPackage) return;

    setIsPurchasing(true);

    try {
      const config = productConfigs.find(plan => plan.productId[Platform.OS as 'ios' | 'android'] === selectedPackage);

      if (!config) {
        console.error('Unknown product selected:', selectedPackage);
        return;
      }

      // Get the correct product ID for the current platform
      const platform = Platform.OS as 'ios' | 'android';
      const productId = config.productId[platform];

      if (!productId) {
        console.error(`No product ID found for platform ${platform}:`, config);
        return;
      }

      if (config?.type === 'subscription') {
        await purchaseSubscription(productId);
      } else {
        await purchaseProduct(productId);
      }

    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setIsPurchasing(false);
    }
  }, [selectedPackage, purchaseProduct, purchaseSubscription, productConfigs]);

  const handleBackToFree = useCallback(async () => {
    await showBackToFreeConfirmation();
  }, []);

  const handleRestorePurchases = useCallback(async () => {
    setIsRestoring(true);

    try {
      const result = await restorePurchases();

      if (result.success) {
        Alert.alert(
          'Restore Complete',
          result.message,
          [
            {
              text: 'OK',
              onPress: async () => {
                // Refresh the balance to show updated days
                await fetchBalance();
              }
            }
          ]
        );
      } else {
        Alert.alert('Restore Failed', result.message);
      }
    } catch (error) {
      console.error('Restore purchases error:', error);
      Alert.alert(
        'Restore Failed',
        'There was an error restoring your purchases. Please try again.'
      );
    } finally {
      setIsRestoring(false);
    }
  }, [restorePurchases, fetchBalance]);

  const isUpgrade = useMemo(() => {
    const selectedPlan = productConfigs.find(plan => plan.productId[Platform.OS as 'ios' | 'android'] === selectedPackage);
    return getPlanOrder(selectedPlan?.id!) > getPlanOrder(balance?.currentPlan!)
  }, [
    selectedPackage,
    balance?.currentPlan,
    productConfigs,
  ])

  const isDowngrade = useMemo(() => {
    const selectedPlan = productConfigs.find(plan => plan.productId[Platform.OS as 'ios' | 'android'] === selectedPackage);
    return getPlanOrder(selectedPlan?.id!) < getPlanOrder(balance?.currentPlan!)
  }, [
    selectedPackage,
    balance?.currentPlan,
    productConfigs,
  ])


  const isConsumable = useMemo(() => {
    const selectedPlan = productConfigs.find(plan => plan.productId[Platform.OS as 'ios' | 'android'] === selectedPackage);
    return selectedPlan?.type === 'consumable';
  }, [
    selectedPackage,
    productConfigs,
  ])

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Upgrade & Refills"
        subtitle="Change your plan or buy extra days"
        showBackButton
      />

      <ScrollView style={styles.scrollView}>
        {isLoading ? (
          <LoadingView />
        ) : (
          <View style={styles.content}>
            {/* Current Plan Information */}
            {balance && (
              <CurrentPlanCard
                balance={balance}
              />
            )}

            <Text style={styles.sectionTitle}>
              {balance?.currentPlan === 'free' ? 'Upgrade Your Plan' : 'Change Your Plan'}
            </Text>

            {/* Current Plan Indicator */}
            {balance && balance.currentPlan !== 'free' && (
              <View style={styles.currentPlanIndicator}>
                <Ionicons name="information-circle" size={16} color="#2196F3" />
                <Text style={styles.currentPlanText}>
                  You&apos;re currently on the {balance.currentPlan.charAt(0).toUpperCase() + balance.currentPlan.slice(1)} plan
                </Text>
              </View>
            )}

            {/* Subscription Plans */}
            {productConfigs.filter(plan => plan.type === 'subscription').map((plan) => {
              // Check if this is the current plan
              const isCurrentPlan = balance?.currentPlan === plan.id;
              const isUpgrade = balance?.currentPlan && getPlanOrder(plan.id) > getPlanOrder(balance.currentPlan);

              // For free plan: show back to free button if user is on paid plan
              const isFreePlan = plan.id === 'free';
              const isUserOnPaidPlan = balance?.currentPlan && balance.currentPlan !== 'free';
              const showBackToFreeButton = isFreePlan && isUserOnPaidPlan;

              // Free plan selection logic:
              // - Selected by default if user is on free plan
              // - Not selectable if user is on paid plan (shows back to free button instead)
              const isFreePlanSelected = isFreePlan && balance?.currentPlan === 'free';

              // Get the correct product ID for the current platform
              const platform = Platform.OS as 'ios' | 'android';
              const productId = plan.productId[platform];

              if (!productId) {
                console.warn(`No product ID found for platform ${platform} for plan:`, plan.id);
                return null;
              }

              return (
                <SubscriptionPlanCard
                  key={productId}
                  plan={{
                    daysPerMonth: plan.daysPerMonth!,
                    price: plan.price.usd,
                    popular: plan.popular,
                    type: 'subscription',
                    id: plan.id,
                    name: plan.name,
                    productId: productId,
                  }}
                  isSelected={isFreePlanSelected || selectedPackage === productId}
                  isCurrentPlan={isCurrentPlan}
                  isUpgrade={isUpgrade}
                  onSelect={handleSelectPackage}
                  showBackToFreeButton={showBackToFreeButton}
                  onBackToFree={handleBackToFree}
                />
              );
            })}

            <Text style={styles.sectionTitle}>Day Refills</Text>

            {/* Day Refill Selector */}
            <BackendDayRefillSelector
              selectedPackageId={selectedPackage}
              onSelect={handleSelectPackage}
              productConfigs={productConfigs}
            />

            {/* Purchase Button */}
            <PurchaseButton
              isSelected={!!selectedPackage}
              isLoading={isPurchasing}
              isUpgrade={isUpgrade}
              isDowngrade={isDowngrade}
              isConsumable={isConsumable}
              onPress={handlePurchase}
            />

            {/* Restore Purchases Button (iOS only) */}
            {Platform.OS === "ios" && (
              <RestorePurchasesButton
                isRestoring={isRestoring}
                onPress={handleRestorePurchases}
              />
            )}

            {/* Information Card */}
            <InfoCard />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },

  currentPlanIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  currentPlanText: {
    fontSize: 14,
    color: '#1976D2',
    marginLeft: 8,
    fontWeight: '500',
  },
});
