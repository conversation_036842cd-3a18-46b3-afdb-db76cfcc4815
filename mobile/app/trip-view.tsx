import { useLocalSearchParams, useFocusEffect } from "expo-router";
import { Fragment, useCallback } from "react";
import { SafeAreaView, StyleSheet } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTrip } from "../lib/hooks/use-trip";
import { useTripSocket } from "../lib/hooks/use-trip-socket";
import { useActivityCompletion } from "../ui/trips/ActivityManager";
import { ScreenType } from "../lib/services/trip-socket.service";
import { useTripViewState } from "../lib/hooks/useTripViewState";
import { useTripMapRegion } from "../lib/hooks/useTripMapRegion";
import { useFilterHandlers } from "../lib/hooks/useFilterHandlers";
import { useTripViewInsufficientDays } from "../lib/hooks/useTripViewInsufficientCredits";
import TripViewHeader from "../ui/trips/TripViewHeader";
import ErrorState from "../ui/common/ErrorState";
import { LoadingView } from "../ui/trip-view/LoadingView";
import { MapView } from "../ui/trip-view/MapView";
import { ListView } from "../ui/trip-view/ListView";

/**
 * Trip View Screen
 * Shows a trip in either map or list view
 */
export default function TripViewScreen() {
  // Get trip ID and initial view mode from route params
  const { tripId, viewMode: initialViewMode } = useLocalSearchParams<{
    tripId: string;
    viewMode?: "map" | "list";
  }>();

  // Get safe area insets
  const { top, bottom, left, right } = useSafeAreaInsets();

  // Fetch trip data
  const { trip, error, loadTrip } = useTrip({ tripId });

  // Initialize insufficient days handling
  const {
    handleInsufficientBalanceError,
  } = useTripViewInsufficientDays();

  // Initialize trip view state
  const {
    viewMode,
    setViewMode,
    itineraryViewMode,
    setItineraryViewMode,
    expandedCities,
    showMoreOptions,
    setShowMoreOptions,
    isGenerating,
    setIsGenerating,
    handleBackToHome,
    toggleCity,
  } = useTripViewState({
    initialViewMode: initialViewMode as "map" | "list",
    trip,
  });

  // Initialize map region
  const { mapRegion, handleRegionChangeComplete, handleFilterChangeForRegion } =
    useTripMapRegion({
      tripId: tripId as string,
      trip,
    });

  // Initialize filter handlers
  const {
    selectedDay,
    selectedCity,
    selectedStatus,
    showDayPicker,
    showCityPicker,
    showStatusPicker,
    setShowDayPicker,
    setShowCityPicker,
    setShowStatusPicker,
    onCitySelected,
    onDaySelected,
    onStatusSelected,
    closeAllPickers,
  } = useFilterHandlers({
    trip,
    handleFilterChangeForRegion,
  });









  // Initialize activity completion
  const {
    completedActivities,
    activityInProgress,
    progressAnimation,
    completionAnimation,
    handleActivityCompletion,
    handleActivityUncompletion,
    handleLongPress,
    handlePressOut,
    toggleActivityCompletion,
  } = useActivityCompletion({
    tripId: tripId as string,
    onTripUpdate: loadTrip,
  });

  // Reload trip data when the screen is focused
  useFocusEffect(
    useCallback(() => {
      loadTrip();
      return () => { };
    }, [loadTrip]),
  );

  // Use socket hook for handling trip updates
  useTripSocket({
    tripId,
    screenType: ScreenType.TRIP_VIEW,
    handlers: {
      onInProgress: useCallback(
        (_content: string) => {
          setIsGenerating(true);
          loadTrip();
        },
        [loadTrip, setIsGenerating],
      ),
      onError: useCallback(
        (content: string, context?: any) => {
          setIsGenerating(false);

          // Only handle insufficient balance errors if context confirms it
          if (context?.isInsufficientBalance) {
            console.info(
              "Detected insufficient balance error from socket with context in trip view",
            );
            // Pass both content and context to the handler
            handleInsufficientBalanceError(content, context);
          } else {
            console.info(
              "Received general error in trip view (not insufficient balance):",
              { content, context }
            );
          }

          loadTrip();
        },
        [loadTrip, setIsGenerating, handleInsufficientBalanceError],
      ),
      onSuccess: useCallback(
        (_content: string) => {
          setIsGenerating(false);
          loadTrip();
        },
        [loadTrip, setIsGenerating],
      ),
    },
  });

  // Create header component
  const header = (
    <TripViewHeader
      tripName="Trip View"
      tripDestination={trip?.name || "Loading trip details..."}
      viewMode={viewMode}
      setViewMode={setViewMode}
      showMoreOptions={showMoreOptions}
      setShowMoreOptions={setShowMoreOptions}
      tripId={tripId as string}
      onBackPress={handleBackToHome}
    />
  );

  // Show error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        {header}
        <ErrorState error={error.message} onRetry={loadTrip} />
      </SafeAreaView>
    );
  }

  // Show loading state
  if (!trip) {
    return <LoadingView header={header} />;
  }

  // Show map view
  if (viewMode === "map") {
    return (
      <Fragment>
        <MapView
          trip={trip}
          tripId={tripId as string}
          mapRegion={mapRegion!}
          selectedDay={selectedDay}
          selectedCity={selectedCity}
          selectedStatus={selectedStatus}
          showDayPicker={showDayPicker}
          showCityPicker={showCityPicker}
          showStatusPicker={showStatusPicker}
          completedActivities={completedActivities}
          activityInProgress={activityInProgress}
          progressAnimation={progressAnimation}
          completionAnimation={completionAnimation}
          handleLongPress={handleLongPress}
          handlePressOut={handlePressOut}
          toggleActivityCompletion={toggleActivityCompletion}
          handleRegionChangeComplete={handleRegionChangeComplete}
          setShowDayPicker={setShowDayPicker}
          setShowCityPicker={setShowCityPicker}
          setShowStatusPicker={setShowStatusPicker}
          onDaySelected={onDaySelected}
          onCitySelected={onCitySelected}
          onStatusSelected={onStatusSelected}
          closeAllPickers={closeAllPickers}
          header={header}
          top={top}
        />


      </Fragment>
    );
  }

  // Show list view
  return (
    <>
      <ListView
        trip={trip}
        tripId={tripId as string}
        itineraryViewMode={itineraryViewMode}
        setItineraryViewMode={setItineraryViewMode}
        expandedCities={expandedCities}
        toggleCity={toggleCity}
        completedActivities={completedActivities}
        handleActivityCompletion={handleActivityCompletion}
        handleActivityUncompletion={handleActivityUncompletion}
        showDayPicker={showDayPicker}
        showCityPicker={showCityPicker}
        showStatusPicker={showStatusPicker}
        closeAllPickers={closeAllPickers}
        header={header}
        top={top}
        bottom={bottom}
        left={left}
        right={right}
        onInsufficientBalance={() => handleInsufficientBalanceError("Insufficient balance", { isInsufficientBalance: true })}
        isGenerating={isGenerating}
        setIsGenerating={setIsGenerating}
      />


    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
});
