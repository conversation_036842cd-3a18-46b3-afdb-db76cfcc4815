import { router, useLocalSearchParams } from "expo-router";
import { useMemo } from "react";
import { RefreshControl, ScrollView, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTrips } from "../lib/hooks/use-trips";
import { useTripActions } from "../lib/hooks/useTripActions";
import { DbTrip } from "../lib/types";
import ErrorState from "../ui/common/ErrorState";
import ScreenHeader from "../ui/common/ScreenHeader";
import { TripCardWithImage } from "../ui/trips/TripCardWithImage";

/**
 * Country trips modal screen
 * Shows trips for a selected country in a modal presentation
 */
export default function CountryTripsScreen() {
  const { country } = useLocalSearchParams<{
    country: string;
  }>();

  const { bottom } = useSafeAreaInsets();
  const { trips: allTrips, loading, error, loadTrips } = useTrips({});
  const { handleTripSelect, handleDeleteTrip, handleToggleFavorite } = useTripActions(loadTrips);

  // Filter trips by country
  const trips = useMemo(() => {
    if (!allTrips || !country) return [];
    return allTrips.filter(trip => {
      const tripCountry = trip.tripDetails?.destination?.split(",").pop()?.trim();
      return tripCountry === country;
    });
  }, [allTrips, country]);

  const handleTripPress = (trip: DbTrip) => {
    handleTripSelect("map")(trip._id);
  };

  const handleClose = () => {
    router.back();
  };

  if (error) {
    return (
      <View style={[styles.container]}>
        <ScreenHeader
          title={`${country} Trips`}
          subtitle="Unable to load trips"
          showBackButton
          onBackPress={handleClose}
        />
        <ErrorState
          error="We're having trouble loading your trips for this country. Please try again."
          onRetry={loadTrips}
        />
      </View>
    );
  }

  return (
    <View style={[styles.container]}>
      <ScreenHeader
        title={`${country} Trips`}
        subtitle={loading ? "Loading..." : `${trips.length} ${trips.length === 1 ? 'trip' : 'trips'}`}
        showBackButton
        onBackPress={handleClose}
      />

      <View style={[styles.content, { paddingBottom: bottom + 20 }]}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={loadTrips} />
          }
        >
          {trips.map((trip) => (
            <TripCardWithImage
              key={trip._id}
              trip={trip}
              onDelete={handleDeleteTrip}
              handleDeleteTrip={handleDeleteTrip}
              onPress={handleTripPress}
              onToggleFavorite={handleToggleFavorite}
              isFavorite={trip.isFavorite}
            />
          ))}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 8,
  },
});
