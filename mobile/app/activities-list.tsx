import { router, useLocalSearchParams } from "expo-router";
import { RefreshControl, ScrollView, StyleSheet, Text, View, ActivityIndicator } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useState, useEffect } from "react";
import { Activity } from "../lib/types";
import ActivityCard from "../ui/common/ActivityCard";
import ScreenHeader from "../ui/common/ScreenHeader";
import { ActivityTimeline } from "../ui/daily-itinerary/ActivityTimeline";
import ErrorState from "../ui/common/ErrorState";

import { TripService } from "../lib/services/trip.service";
const tripService = TripService.getInstance();

/**
 * Activities list modal screen
 * Shows activities in a modal presentation for both daily itinerary and city map contexts
 */
export default function ActivitiesListScreen() {
  const {
    context,
    title,
    subtitle,
    tripId,
    day,
    cityName,
  } = useLocalSearchParams<{
    context: "timeline" | "city";
    title: string;
    subtitle?: string;
    tripId: string;
    day?: string;
    cityName?: string;
  }>();

  const { bottom } = useSafeAreaInsets();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // Fetch activities based on context
  useEffect(() => {
    const fetchActivities = async () => {
      if (!tripId) return;

      setLoading(true);
      setError(null);

      try {
        if (context === "timeline" && day) {
          // Fetch activities for a specific day
          const trip = await tripService.fetchTripById(tripId);
          if (trip) {
            const dayItinerary = trip.itinerary?.find(item => item.day === parseInt(day));
            setActivities(dayItinerary?.activities || []);
          }
        } else if (context === "city" && cityName) {
          // Fetch activities for a specific city
          const result = await tripService.fetchActivitiesByCity(tripId, cityName);
          const cityActivities = result[cityName.toLowerCase()] || [];
          setActivities(cityActivities);
        }
      } catch (err) {
        console.error("Error fetching activities:", err);
        setError("Failed to load activities");
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [tripId, day, cityName, context]);

  const handleActivitySelect = (_index: number) => {
    // For now, just close the modal
    // In the future, this could navigate to activity details or center map
    router.back();
  };

  const handleRefresh = () => {
    // Re-fetch activities
    const fetchActivities = async () => {
      if (!tripId) return;

      setLoading(true);
      setError(null);

      try {
        if (context === "timeline" && day) {
          const trip = await tripService.fetchTripById(tripId);
          if (trip) {
            const dayItinerary = trip.itinerary?.find(item => item.day === parseInt(day));
            setActivities(dayItinerary?.activities || []);
          }
        } else if (context === "city" && cityName) {
          const result = await tripService.fetchActivitiesByCity(tripId, cityName);
          const cityActivities = result[cityName.toLowerCase()] || [];
          setActivities(cityActivities);
        }
      } catch (err) {
        console.error("Error refreshing activities:", err);
        setError("Failed to refresh activities");
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  };

  const handleClose = () => {
    router.back();
  };

  if (error) {
    return (
      <View style={[styles.container]}>
        <ScreenHeader
          title={title || "Activities"}
          subtitle="Unable to load activities"
          showBackButton
          onBackPress={handleClose}
        />
        <ErrorState
          error={error}
          onRetry={handleRefresh}
        />
      </View>
    );
  }

  const renderContent = () => {
    if (context === "timeline") {
      // Use ActivityTimeline for daily itinerary context
      return (
        <ActivityTimeline
          activities={activities}
          loading={loading}
          onRefresh={handleRefresh}
          onActivitySelect={handleActivitySelect}
          onClose={handleClose}
          bottomPadding={bottom}
        />
      );
    } else {
      // Use ScrollView with ActivityCards for city context
      return (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[
            styles.activitiesList,
            { paddingBottom: bottom + 32 },
          ]}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
          }
        >
          {activities.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No Activities Found</Text>
              <Text style={styles.emptyText}>
                No activities found for this location
              </Text>
            </View>
          ) : (
            activities.map((activity, index) => {
              // Create a unique key combining activity id and index to avoid duplicates
              const uniqueKey = `${activity.id || 'unknown'}-${index}`;

              return (
                <View key={uniqueKey} style={styles.activityWrapper}>
                  <ActivityCard
                    activity={activity}
                    context="city"
                    isCompleted={false} // We don't track completion in city map view
                    onPress={() => handleActivitySelect(index)}
                    dayNumber={activity.dayNumber}
                    date={activity.date}
                  />
                </View>
              );
            })
          )}
        </ScrollView>
      );
    }
  };

  return (
    <View style={[styles.container]}>
      <ScreenHeader
        title={title || "Activities"}
        subtitle={subtitle!}
        showBackButton
        onBackPress={handleClose}
      />

      <View style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          renderContent()
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  activitiesList: {
    padding: 16,
  },
  activityWrapper: {
    marginBottom: 12,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
