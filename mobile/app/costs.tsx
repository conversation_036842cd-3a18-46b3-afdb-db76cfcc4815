import { useLocalSearchParams } from "expo-router";
import React from "react";
import {
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { useTrip } from "../lib/hooks/use-trip";
import ErrorState from "../ui/common/ErrorState";
import ScreenHeader from "../ui/common/ScreenHeader";

export default function CostBreakdownScreen() {
  const { tripId } = useLocalSearchParams<{ tripId: string }>();
  const { trip, loading: isLoading, error, loadTrip } = useTrip({ tripId });

  const costs = trip?.costBreakdown;

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          title="Cost Breakdown"
          subtitle="Estimated costs for your trip"
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000" />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          title="Cost Breakdown"
          subtitle="Estimated costs for your trip"
        />
        <ErrorState error={error.message} onRetry={loadTrip} />
      </SafeAreaView>
    );
  }

  const CostItem = ({ label, amount }: any) => (
    <View style={styles.costItem}>
      <Text style={styles.costLabel}>{label}</Text>
      <Text style={styles.costAmount}>${amount}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Cost Breakdown"
        subtitle="Estimated costs for your trip"
      />
      <ScrollView style={styles.content}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Total Trip Cost</Text>
          <Text style={styles.totalAmount}>
            ${costs?.total_estimated_cost.toFixed(2)}
          </Text>
        </View>

        <View style={styles.breakdownContainer}>
          <CostItem label="Activities" amount={costs?.activities} />
          <CostItem label="Meals" amount={costs?.meals} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  totalContainer: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    color: "#666",
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: "700",
    color: "#333",
  },
  breakdownContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  costItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  costLabel: {
    fontSize: 16,
    color: "#333",
  },
  costAmount: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
});
