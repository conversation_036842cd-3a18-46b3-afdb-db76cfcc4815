import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useState } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { INTERESTS } from "../lib/constants";
import { useTripStore } from "../lib/store";
import {
  mediumHapticFeedback,
  successHapticFeedback,
  selectionHapticFeedback,
} from "../lib/utils/haptics";
import ScreenHeader from "../ui/common/ScreenHeader";
import { KeyboardAvoidingWrapper } from "../ui/common/KeyboardAvoidingWrapper";

export default function InterestsScreen() {
  const { addInterest, removeInterest, tripDetails, customInterests } =
    useTripStore();
  const [newInterest, setNewInterest] = useState("");
  const { bottom } = useSafeAreaInsets();

  const allInterests = [...INTERESTS, ...customInterests];

  const toggleInterest = (id: string) => {
    const interest = allInterests.find((i) => i.id === id);
    if (!interest) return;

    // Provide haptic feedback when toggling an interest
    selectionHapticFeedback();

    const isSelected = tripDetails.interests.includes(interest.name);
    if (isSelected) {
      removeInterest(interest.id);
    } else {
      addInterest({
        id: interest.id,
        name: interest.name,
        icon: interest.icon,
      });
    }
  };

  const handleAddInterest = () => {
    if (newInterest.trim()) {
      const newId = `custom-${Date.now()}`;
      const newInterestObject = {
        id: newId,
        name: newInterest.trim(),
        icon: "add-outline",
      };
      addInterest(newInterestObject);
      setNewInterest("");
    }
  };

  const handleSelectAll = () => {
    const allSelected = allInterests.length === tripDetails.interests.length;
    if (allSelected) {
      allInterests.forEach((interest) => removeInterest(interest.id));
    } else {
      // Select all
      allInterests.forEach((interest) =>
        addInterest({
          id: interest.id,
          name: interest.name,
          icon: interest.icon,
        }),
      );
    }
  };

  const handleContinue = async () => {
    if (tripDetails.interests.length > 0) {
      // Provide haptic feedback when continuing
      successHapticFeedback();

      // Navigate to processing screen
      router.navigate("/trip-confirmation");
    } else {
      console.error("No interests selected");
    }
  };

  const allSelected = tripDetails.interests.length === allInterests.length;

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="What interests you?"
        subtitle="Select your interests to help us personalize your trip"
        showBackButton
        rightComponent={
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => {
              mediumHapticFeedback();
              router.back();
            }}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        }
      />

      <KeyboardAvoidingWrapper
        style={{ flex: 1 }}
        keyboardVerticalOffset={64}
        scrollEnabled={false}
      >
        <View style={styles.topBar}>
          <TextInput
            style={styles.input}
            placeholder="Add your own interest..."
            value={newInterest}
            onChangeText={setNewInterest}
            onSubmitEditing={handleAddInterest}
          />
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              selectionHapticFeedback();
              handleAddInterest();
            }}
          >
            <Ionicons name="add-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: bottom + 100 }}
        >
          <View style={styles.interestsGrid}>
            <TouchableOpacity
              style={[styles.interestItem]}
              onPress={handleSelectAll}
            >
              <View
                style={[
                  styles.iconContainer,
                  allSelected && styles.selectedIconContainer,
                ]}
              >
                <Ionicons
                  name="apps-outline"
                  size={32}
                  color={allSelected ? "#fff" : "#666"}
                />
              </View>
              <Text
                style={[
                  styles.interestName,
                  allSelected && styles.selectedInterestName,
                ]}
              >
                {allSelected ? "Deselect All" : "Select All"}
              </Text>
            </TouchableOpacity>

            {allInterests.reverse().map((interest) => (
              <TouchableOpacity
                key={interest.id}
                style={[
                  styles.interestItem,
                  tripDetails.interests.includes(interest.name) &&
                  styles.selectedItem,
                ]}
                onPress={() => toggleInterest(interest.id)}
              >
                <View
                  style={[
                    styles.iconContainer,
                    tripDetails.interests.includes(interest.name) &&
                    styles.selectedIconContainer,
                  ]}
                >
                  <Ionicons
                    name={interest.icon as any}
                    size={32}
                    color={
                      tripDetails.interests.includes(interest.name)
                        ? "#fff"
                        : "#666"
                    }
                  />
                </View>
                <Text
                  style={[
                    styles.interestName,
                    tripDetails.interests.includes(interest.name) &&
                    styles.selectedInterestName,
                  ]}
                >
                  {interest.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        <View style={[styles.bottomButtonContainer]}>
          <TouchableOpacity
            style={[
              styles.continueButton,
              tripDetails.interests.length === 0 &&
              styles.continueButtonDisabled,
            ]}
            onPress={handleContinue}
            disabled={tripDetails.interests.length === 0}
          >
            <Text
              style={[
                styles.continueButtonText,
                tripDetails.interests.length === 0 &&
                styles.continueButtonTextDisabled,
              ]}
            >
              Continue
            </Text>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={tripDetails.interests.length === 0 ? "#999" : "#fff"}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingWrapper>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  headerTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
  },
  topBar: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    gap: 12,
  },
  scrollView: {
    flex: 1,
  },
  input: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    marginRight: 8,
    fontSize: 16,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
  },
  bottomButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    paddingTop: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  continueButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#2196F3",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
    width: "100%",
  },
  continueButtonDisabled: {
    backgroundColor: "#E0E0E0",
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  continueButtonTextDisabled: {
    color: "#999",
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
  },
  interestsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 10,
  },
  interestItem: {
    width: "33.33%",
    padding: 10,
    alignItems: "center",
  },
  selectedItem: {
    transform: [{ scale: 1.05 }],
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  selectedIconContainer: {
    backgroundColor: "#2196F3",
  },
  interestName: {
    fontSize: 14,
    color: "#333",
    textAlign: "center",
  },
  selectedInterestName: {
    color: "#2196F3",
    fontWeight: "600",
  },
});
