import React, { useCallback } from "react";
import { View, StyleSheet, ScrollView, SafeAreaView } from "react-native";
import { StatusBar } from "expo-status-bar";

// Import components
import LocationStep from "../ui/onboarding/LocationStep";
import DatesStep from "../ui/onboarding/DatesStep";
import TravelStyleStep from "../ui/onboarding/TravelStyleStep";
import CuisinePreferencesStep from "../ui/onboarding/CuisinePreferencesStep";
import InterestsStep from "../ui/onboarding/InterestsStep";
import OnboardingHeader from "../ui/onboarding/OnboardingHeader";
import OnboardingProgress from "../ui/onboarding/OnboardingProgress";
import OnboardingFooter from "../ui/onboarding/OnboardingFooter";

// Import hooks
import { useOnboardingState } from "../lib/hooks/useOnboardingState";

/**
 * Onboarding screen component
 * Guides the user through the process of creating a new trip
 */
export default function OnboardingScreen() {
  // Get onboarding state from custom hook
  const {
    currentStep,
    formData,
    stepTitles,
    isLastStep,
    updateFormData,
    nextStep,
    prevStep,
    validateStep,
    handleSubmit,
  } = useOnboardingState();

  /**
   * Render the current step content based on the current step index
   */
  const renderStepContent = useCallback(() => {
    switch (currentStep) {
      case 0:
        return (
          <LocationStep formData={formData} onUpdateFormData={updateFormData} />
        );
      case 1:
        return (
          <DatesStep formData={formData} onUpdateFormData={updateFormData} />
        );
      case 2:
        return (
          <TravelStyleStep
            formData={formData}
            onUpdateFormData={updateFormData}
          />
        );
      case 3:
        return (
          <CuisinePreferencesStep
            formData={formData}
            onUpdateFormData={updateFormData}
          />
        );
      case 4:
        return (
          <InterestsStep
            formData={formData}
            onUpdateFormData={updateFormData}
          />
        );
      default:
        return null;
    }
  }, [currentStep, formData, updateFormData]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header with step title and back button */}
      <OnboardingHeader
        currentStep={currentStep}
        stepTitles={stepTitles}
        onBack={prevStep}
      />

      {/* Progress bar */}
      <OnboardingProgress
        currentStep={currentStep}
        totalSteps={stepTitles.length}
      />

      {/* Main content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderStepContent()}
        <View style={{ height: 100 }} />
      </ScrollView>

      {/* Footer with continue/submit button */}
      <OnboardingFooter
        currentStep={currentStep}
        totalSteps={stepTitles.length}
        formData={formData}
        isLastStep={isLastStep}
        onNext={nextStep}
        onSubmit={handleSubmit}
        validateStep={validateStep}
      />
    </SafeAreaView>
  );
}

/**
 * Styles for the onboarding screen
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
});
