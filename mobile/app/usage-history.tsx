import { Ionicons } from "@expo/vector-icons";
import { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { API_URL } from "../lib/constants";
import { useDaysBalance } from "../lib/hooks/use-days-balance";
import { CommonService } from "../lib/services/common.service";
import { DaysTransaction } from "../lib/services/days-balance.service";
import { mediumHapticFeedback } from "../lib/utils/haptics";
import ScreenHeader from "../ui/common/ScreenHeader";

interface BillingHistoryItem {
  id: string;
  date: string;
  description: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  transactionId: string;
  platform: string;
  packageType: string;
}

export default function UsageHistoryScreen() {
  const { transactions, pagination, fetchTransactions, isLoading } =
    useDaysBalance();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'usage' | 'billing'>('usage');
  const [billingHistory, setBillingHistory] = useState<BillingHistoryItem[]>([]);
  const [isBillingLoading, setIsBillingLoading] = useState(false);

  const loadTransactions = useCallback(
    async (reset = false) => {
      const nextPage = reset ? 1 : pagination.currentPage + 1;
      await fetchTransactions(20, nextPage, reset);
    },
    [fetchTransactions, pagination.currentPage],
  );

  useEffect(() => {
    // Only load initial transactions if we don't have any yet
    if (transactions.length === 0 && !isLoading) {
      loadTransactions(true);
    }
  }, [transactions.length, isLoading, loadTransactions]);

  console.log("Transactions: ", transactions);
  console.log("Billing History: ", billingHistory);


  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    if (activeTab === 'usage') {
      await loadTransactions(true);
    } else {
      await loadBillingHistory();
    }
    setRefreshing(false);
  }, [loadTransactions, activeTab]);

  const loadBillingHistory = async () => {
    try {
      setIsBillingLoading(true);
      const commonService = new CommonService();
      const headers = await commonService.setTokenInHeaders();

      const response = await fetch(`${API_URL}/payments/billing-history`, {
        method: 'GET',
        headers,
      });

      if (response.ok) {
        const data = await response.json();
        setBillingHistory(data.history || []);
      }
    } catch (error) {
      console.error('Error loading billing history:', error);
    } finally {
      setIsBillingLoading(false);
    }
  };

  const handleTabChange = (tab: 'usage' | 'billing') => {
    mediumHapticFeedback();
    setActiveTab(tab);
    if (tab === 'billing' && billingHistory.length === 0) {
      loadBillingHistory();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const getTransactionIcon = (transaction: DaysTransaction) => {
    if (transaction.type === "credit") {
      if (transaction.source === "initial_grant") {
        return "gift";
      } else if (transaction.source === "subscription") {
        return "star";
      } else if (transaction.source === "day_refill") {
        return "add-circle";
      } else {
        return "add-circle";
      }
    } else {
      return "remove-circle";
    }
  };

  const getTransactionColor = (transaction: DaysTransaction) => {
    return transaction.type === "credit" ? "#4CAF50" : "#FF5722";
  };

  const getTransactionTitle = (transaction: DaysTransaction) => {
    if (transaction.description) {
      return transaction.description;
    }

    if (transaction.type === "credit") {
      if (transaction.source === "initial_grant") {
        return "Welcome Days";
      } else if (transaction.source === "subscription") {
        return "Monthly Subscription";
      } else if (transaction.source === "day_refill") {
        return "Day Refill Purchase";
      } else {
        return "Days Added";
      }
    } else {
      if (transaction.source === "trip_generation") {
        return "Trip Generation";
      } else {
        return "Days Used";
      }
    }
  };

  const renderTransactionItem = ({ item }: { item: DaysTransaction }) => {
    const icon = getTransactionIcon(item);
    const color = getTransactionColor(item);
    const title = getTransactionTitle(item);

    return (
      <View style={styles.transactionItem}>
        <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
          <Ionicons name={icon} size={24} color={color} />
        </View>
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionTitle}>{title}</Text>
          <Text style={styles.transactionDate}>
            {formatDate(item.createdAt)} at {formatTime(item.createdAt)}
          </Text>
        </View>
        <View style={styles.amountContainer}>
          <Text
            style={[
              styles.transactionAmount,
              { color: item.type === "credit" ? "#4CAF50" : "#FF5722" },
            ]}
          >
            {item.type === "credit" ? "+" : "-"}
            {item.amount.toLocaleString()}
          </Text>
          <Text style={styles.balanceText}>
            Balance: {item.balanceAfter.toLocaleString()}
          </Text>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.emptyText}>Loading transaction history...</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="time" size={64} color="#ccc" />
        <Text style={styles.emptyTitle}>No Transactions Yet</Text>
        <Text style={styles.emptyText}>
          Your day usage history will appear here once you start generating
          trips.
        </Text>
      </View>
    );
  };

  const renderBillingItem = ({ item }: { item: BillingHistoryItem }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'completed': return '#4CAF50';
        case 'pending': return '#FF9800';
        case 'failed': return '#F44336';
        case 'refunded': return '#9C27B0';
        default: return '#666';
      }
    };

    const getStatusIcon = (status: string) => {
      switch (status) {
        case 'completed': return 'checkmark-circle';
        case 'pending': return 'time';
        case 'failed': return 'close-circle';
        case 'refunded': return 'return-up-back';
        default: return 'help-circle';
      }
    };

    const platform = item.platform === 'ios'
      ? 'App Store'
      : 'Google Play';


    return (
      <View style={styles.transactionItem}>
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <Text style={styles.transactionDescription}>{item.description}</Text>
            <Text style={styles.transactionDate}>
              {formatDate(item.date)} • {platform}
            </Text>
          </View>
          <View style={styles.transactionAmount}>
            <Text style={styles.amountText}>${item.amount.toFixed(2)}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Ionicons
                name={getStatusIcon(item.status) as any}
                size={12}
                color="#fff"
                style={styles.statusIcon}
              />
              <Text style={styles.statusText}>
                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const onEndReached = useCallback(() => {
    if (pagination.hasNextPage && !isLoading) {
      loadTransactions(false);
    }
  }, [pagination.hasNextPage, isLoading, loadTransactions])

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Payment & Usage History"
        subtitle="Track your payments and day usage"
        showBackButton
      />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'usage' && styles.activeTab]}
          onPress={() => handleTabChange('usage')}
        >
          <Ionicons
            name="time"
            size={20}
            color={activeTab === 'usage' ? '#2196F3' : '#666'}
          />
          <Text style={[styles.tabText, activeTab === 'usage' && styles.activeTabText]}>
            Usage History
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'billing' && styles.activeTab]}
          onPress={() => handleTabChange('billing')}
        >
          <Ionicons
            name="receipt"
            size={20}
            color={activeTab === 'billing' ? '#2196F3' : '#666'}
          />
          <Text style={[styles.tabText, activeTab === 'billing' && styles.activeTabText]}>
            Billing History
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'usage' ? (
        <FlatList
          data={transactions}
          renderItem={renderTransactionItem}
          keyExtractor={(item, index) => `${item._id}-${index}`}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          onEndReached={onEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoading && !refreshing ? (
              <View style={styles.footer}>
                <ActivityIndicator size="small" color="#2196F3" />
                <Text style={styles.footerText}>Loading more...</Text>
              </View>
            ) : !pagination.hasNextPage && transactions.length > 0 ? (
              <View style={styles.footer}>
                <Text style={styles.footerText}>No more transactions</Text>
              </View>
            ) : pagination.totalPages > 0 ? (
              <View style={styles.footer}>
                <Text style={styles.footerText}>
                  Page {pagination.currentPage} of {pagination.totalPages}
                </Text>
              </View>
            ) : null
          }
        />
      ) : (
        <FlatList
          data={billingHistory}
          renderItem={renderBillingItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              {isBillingLoading ? (
                <>
                  <ActivityIndicator size="large" color="#2196F3" />
                  <Text style={styles.emptyText}>Loading billing history...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="receipt-outline" size={64} color="#ccc" />
                  <Text style={styles.emptyText}>No billing history available</Text>
                  <Text style={styles.emptySubtext}>
                    Your payment history will appear here once you make your first purchase.
                  </Text>
                </>
              )}
            </View>
          )}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: "#666",
  },
  amountContainer: {
    alignItems: "flex-end",
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 4,
  },
  balanceText: {
    fontSize: 12,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
    minHeight: 300,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    lineHeight: 20,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  footerText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginLeft: 6,
  },
  activeTabText: {
    color: '#2196F3',
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
  },
  statusIcon: {
    marginRight: 2,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#fff',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  amountText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    textAlign: 'right',
  },
});
