import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useState } from "react";
import {
  Alert,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import ScreenHeader from "../ui/common/ScreenHeader";
import { KeyboardAvoidingWrapper } from "../ui/common/KeyboardAvoidingWrapper";

interface ContactCategory {
  id: string;
  title: string;
}

export default function ContactUs() {
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  const categories: ContactCategory[] = [
    { id: "account", title: "Account Issues" },
    { id: "billing", title: "Billing & Payments" },
    { id: "trip", title: "Trip Planning Help" },
    { id: "bug", title: "Report a Bug" },
    { id: "feature", title: "Feature Request" },
    { id: "other", title: "Other" },
  ];

  const handleSubmit = () => {
    if (!selectedCategory) {
      Alert.alert("Error", "Please select a category");
      return;
    }

    if (!subject.trim()) {
      Alert.alert("Error", "Please enter a subject");
      return;
    }

    if (!message.trim()) {
      Alert.alert("Error", "Please enter a message");
      return;
    }

    // In a real app, this would send the message to a backend
    Alert.alert(
      "Message Sent",
      "Thank you for contacting us. We'll get back to you as soon as possible.",
      [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ],
    );
  };

  const selectedCategoryTitle = selectedCategory
    ? categories.find((c) => c.id === selectedCategory)?.title
    : "Select a category";

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Contact Us"
        subtitle="We're here to help"
        showBackButton
      />
      <KeyboardAvoidingWrapper
        contentContainerStyle={styles.content}
        keyboardVerticalOffset={100}
      >
        <View style={styles.formGroup}>
          <Text style={styles.label}>Category</Text>
          <TouchableOpacity
            style={styles.categorySelector}
            onPress={() => setShowCategoryPicker(!showCategoryPicker)}
          >
            <Text
              style={[
                styles.categorySelectorText,
                !selectedCategory && styles.placeholderText,
              ]}
            >
              {selectedCategoryTitle}
            </Text>
            <Ionicons
              name={showCategoryPicker ? "chevron-up" : "chevron-down"}
              size={20}
              color="#666"
            />
          </TouchableOpacity>
          {showCategoryPicker && (
            <View style={styles.categoryDropdown}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.categoryOption}
                  onPress={() => {
                    setSelectedCategory(category.id);
                    setShowCategoryPicker(false);
                  }}
                >
                  <Text
                    style={[
                      styles.categoryOptionText,
                      selectedCategory === category.id &&
                      styles.selectedCategoryText,
                    ]}
                  >
                    {category.title}
                  </Text>
                  {selectedCategory === category.id && (
                    <Ionicons name="checkmark" size={20} color="#2196F3" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Subject</Text>
          <TextInput
            style={styles.input}
            value={subject}
            onChangeText={setSubject}
            placeholder="Enter the subject of your message"
            placeholderTextColor="#999"
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Message</Text>
          <TextInput
            style={styles.messageInput}
            value={message}
            onChangeText={setMessage}
            placeholder="Describe your issue or question in detail"
            placeholderTextColor="#999"
            multiline
            textAlignVertical="top"
          />
        </View>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
        >
          <Text style={styles.submitButtonText}>Send Message</Text>
        </TouchableOpacity>

        <View style={styles.alternativeContactContainer}>
          <Text style={styles.alternativeContactTitle}>
            Other Ways to Reach Us
          </Text>
          <View style={styles.contactMethod}>
            <Ionicons name="mail-outline" size={20} color="#2196F3" />
            <Text style={styles.contactMethodText}><EMAIL></Text>
          </View>
          <View style={styles.contactMethod}>
            <Ionicons name="call-outline" size={20} color="#2196F3" />
            <Text style={styles.contactMethodText}>+212665417670</Text>
          </View>
        </View>
      </KeyboardAvoidingWrapper>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },

  content: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    padding: 12,
    fontSize: 16,
  },
  messageInput: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    padding: 12,
    fontSize: 16,
    minHeight: 150,
  },
  categorySelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    padding: 12,
  },
  categorySelectorText: {
    fontSize: 16,
    color: "#1a1a1a",
  },
  placeholderText: {
    color: "#999",
  },
  categoryDropdown: {
    backgroundColor: "#fff",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    marginTop: 4,
    overflow: "hidden",
  },
  categoryOption: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  categoryOptionText: {
    fontSize: 16,
    color: "#1a1a1a",
  },
  selectedCategoryText: {
    color: "#2196F3",
    fontWeight: "500",
  },
  submitButton: {
    backgroundColor: "#2196F3",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 8,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  alternativeContactContainer: {
    marginTop: 32,
    padding: 16,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
  },
  alternativeContactTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  contactMethod: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  contactMethodText: {
    fontSize: 16,
    color: "#1a1a1a",
    marginLeft: 12,
  },
});
