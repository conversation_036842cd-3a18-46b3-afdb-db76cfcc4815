import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useState } from "react";
import {
  Animated,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { DbTrip } from "../../lib/types";
import { calculateTripDuration } from "../../lib/utils/date-constraints";

interface TripCardWithImageProps {
  trip: DbTrip;
  isExpanded?: boolean;
  onDelete: (tripId: string) => void;
  setExpandedTripId?: (id: string[]) => void;
  handleDeleteTrip: (id: string) => void;
  expandedTripId?: string[];
  onPress?: (trip: DbTrip) => void;
  onToggleFavorite?: (tripId: string) => void;
  isFavorite?: boolean;
}

export const TripCardWithImage = ({
  trip,
  handleDeleteTrip,
  onPress,
  onToggleFavorite,
  isFavorite = false,
}: TripCardWithImageProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [animatedHeight] = useState(new Animated.Value(300)); // Collapsed height

  const isGenerating = trip.daysProgress.some((day) => day.is_generating);
  const generatedDays = trip?.itinerary?.length || 0;

  // Recalculate total days to ensure accuracy
  const totalDays = trip?.tripDetails?.startDate && trip?.tripDetails?.endDate
    ? calculateTripDuration(trip.tripDetails.startDate, trip.tripDetails.endDate)
    : trip?.tripDetails?.totalDays || 0;

  const progress = totalDays > 0 ? Math.round((generatedDays / totalDays) * 100) : 0;
  const imageUrl = trip?.tripDetails?.imageUrl;

  // Format dates for better readability
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    });
  };

  // Get trip duration in a readable format
  const getTripDuration = () => {
    if (totalDays === 1) return "1 day";
    return `${totalDays} days`;
  };

  // Get budget display with icon
  const getBudgetDisplay = () => {
    const budget = trip?.tripDetails?.budget;
    switch (budget) {
      case 'budget':
        return { icon: 'wallet-outline' as const, text: 'Budget', color: '#4CAF50' };
      case 'luxury':
        return { icon: 'diamond-outline' as const, text: 'Luxury', color: '#9C27B0' };
      default:
        return { icon: 'card-outline' as const, text: 'Standard', color: '#2196F3' };
    }
  };

  const budgetInfo = getBudgetDisplay();

  // Toggle expand/collapse
  const toggleExpanded = () => {
    const toValue = isExpanded ? 300 : 600; // Collapsed vs expanded height

    Animated.timing(animatedHeight, {
      toValue,
      duration: 300,
      useNativeDriver: false,
    }).start();

    setIsExpanded(!isExpanded);
  };

  // Handle card press - either expand or navigate
  const handleCardPress = () => {
    if (onPress) {
      onPress(trip);
    } else {
      router.push({
        pathname: "/itinerary",
        params: { tripId: trip._id },
      });
    }
  };

  // Handle share functionality
  const handleShare = () => {
    router.navigate({
      pathname: "/share",
      params: {
        tripId: trip._id,
      },
    });
  };

  // Render compact image overlay content
  const renderImageOverlay = () => (
    <>
      {/* Simplified header content */}
      <TouchableOpacity
        style={styles.cardContent}
        onPress={handleCardPress}
        activeOpacity={0.9}
      >
        <View style={styles.compactHeader}>
          <View style={styles.titleSection}>
            <Text style={styles.tripNameCompact} numberOfLines={2}>
              {trip?.name}
            </Text>
            <Text style={styles.destinationCompact} numberOfLines={1}>
              {trip?.tripDetails?.destination}
            </Text>
          </View>

          <View style={styles.badgeSection}>
            {trip?.isExample && (
              <View style={styles.exampleBadgeCompact}>
                <Ionicons name="star" size={10} color="#FFD700" />
              </View>
            )}
            <View style={styles.durationBadgeCompact}>
              <Text style={styles.durationTextCompact}>{getTripDuration()}</Text>
            </View>
          </View>
        </View>

        {/* Progress indicator for generating trips */}
        {isGenerating && (
          <View style={styles.progressSectionCompact}>
            <View style={styles.progressBarCompact}>
              <View style={[styles.progressFillCompact, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.progressLabelCompact}>
              Generating...
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Expand/Collapse button */}
      <TouchableOpacity
        style={styles.expandButton}
        onPress={toggleExpanded}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons
          name={isExpanded ? "chevron-up" : "chevron-down"}
          size={20}
          color="white"
        />
      </TouchableOpacity>
    </>
  );

  return (
    <Animated.View style={[styles.tripCard, { height: animatedHeight }]}>
      {/* Image section - fixed height */}
      <View style={styles.imageContainer}>
        {imageUrl ? (
          <ImageBackground
            source={{ uri: imageUrl }}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            {/* Light overlay for better text readability */}
            <View style={styles.lightOverlay} />
            {renderImageOverlay()}
          </ImageBackground>
        ) : (
          <View style={[styles.backgroundImage, styles.fallbackBackground]}>
            {/* Gradient overlay for fallback */}
            <View style={styles.fallbackOverlay} />
            {renderImageOverlay()}
          </View>
        )}
      </View>

      {/* Expanded content section - below the image */}
      {isExpanded && (
        <View style={styles.expandedContentBelow}>
          {/* Trip route with transport modes */}
          <View style={styles.routeSection}>
            <View style={styles.routeContainer}>
              <View style={styles.cityContainer}>
                <Ionicons
                  name={
                    trip?.tripDetails?.arrivalMode === "Air"
                      ? "airplane-outline"
                      : trip?.tripDetails?.arrivalMode === "Land"
                        ? "car-outline"
                        : "boat-outline"
                  }
                  size={14}
                  color="#666"
                />
                <Text style={styles.cityText} numberOfLines={1}>
                  {trip?.tripDetails?.arrivalCity}
                </Text>
              </View>

              <View style={styles.routeLine}>
                <View style={styles.routeDot} />
                <View style={styles.routeDash} />
                <View style={styles.routeDot} />
              </View>

              <View style={styles.cityContainer}>
                <Ionicons
                  name={
                    trip?.tripDetails?.departureMode === "Air"
                      ? "airplane-outline"
                      : trip?.tripDetails?.departureMode === "Land"
                        ? "car-outline"
                        : "boat-outline"
                  }
                  size={14}
                  color="#666"
                />
                <Text style={styles.cityText} numberOfLines={1}>
                  {trip?.tripDetails?.departureCity}
                </Text>
              </View>
            </View>
          </View>

          {/* Trip details */}
          <View style={styles.content}>
            {/* Date range */}
            <View style={styles.dateSectionExpanded}>
              <Ionicons name="calendar-outline" size={16} color="#666" />
              <Text style={styles.dateTextExpanded}>
                {formatDate(trip?.tripDetails?.startDate)} - {formatDate(trip?.tripDetails?.endDate)}
              </Text>
              <View style={styles.durationBadge}>
                <Text style={styles.durationText}>{getTripDuration()}</Text>
              </View>
            </View>

            {/* Trip stats in a grid */}
            <View style={styles.statsGridExpanded}>
              <View style={styles.statCardExpanded}>
                <Ionicons name="people-outline" size={18} color={budgetInfo.color} />
                <Text style={styles.statValueExpanded}>
                  {(trip?.tripDetails?.people.adults || 0) + (trip?.tripDetails?.people.children || 0)}
                </Text>
                <Text style={styles.statLabelExpanded}>Travelers</Text>
              </View>

              <View style={styles.statCardExpanded}>
                <Ionicons name={budgetInfo.icon} size={18} color={budgetInfo.color} />
                <Text style={styles.statValueExpanded}>{budgetInfo.text}</Text>
                <Text style={styles.statLabelExpanded}>Budget</Text>
              </View>

              <View style={styles.statCardExpanded}>
                <Ionicons name="speedometer-outline" size={18} color={budgetInfo.color} />
                <Text style={styles.statValueExpanded}>{trip?.tripDetails?.intensity}/10</Text>
                <Text style={styles.statLabelExpanded}>Intensity</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Bottom actions section - inside the card */}
      <View style={styles.bottomActionsSection}>
        <TouchableOpacity
          style={[styles.actionButton, styles.favoriteButton, isFavorite && styles.favoriteButtonActive]}
          onPress={() => onToggleFavorite?.(trip._id)}
        >
          <Ionicons
            name={isFavorite ? "heart" : "heart-outline"}
            size={18}
            color={isFavorite ? "#FF3B30" : "#666"}
          />
          <Text style={[styles.actionButtonText, styles.favoriteButtonText]}>
            {isFavorite ? "Liked" : "Like"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.shareButton]}
          onPress={handleShare}
        >
          <Ionicons name="share-social-outline" size={18} color="#2196F3" />
          <Text style={[styles.actionButtonText, styles.shareButtonText]}>Share</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.costsButton]}
          onPress={() => router.navigate({
            pathname: "/costs",
            params: { tripId: trip._id },
          })}
        >
          <Ionicons name="wallet-outline" size={18} color="#2196F3" />
          <Text style={styles.actionButtonText}>Costs</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteTrip(trip._id)}
        >
          <Ionicons name="trash-outline" size={18} color="#FF3B30" />
          <Text style={[styles.actionButtonText, styles.deleteButtonText]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  tripCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  imageContainer: {
    height: 300, // Fixed height for image section
  },
  backgroundImage: {
    flex: 1,
    justifyContent: 'space-between',
  },
  backgroundImageStyle: {
    borderRadius: 16,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 16,
  },
  lightOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.2)', // Lighter overlay for clearer image
    borderRadius: 16,
  },
  fallbackBackground: {
    backgroundColor: '#4A90E2', // Nice blue gradient base
  },
  fallbackOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    borderRadius: 16,
    // Fallback for devices that don't support gradients
  },
  cardContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    minHeight: 50, // Ensure enough space for 2-line titles
  },
  titleSection: {
    flex: 1,
    marginRight: 12,
  },
  tripNameCompact: {
    fontSize: 18,
    fontWeight: "700",
    color: "white",
    marginBottom: 4,
    lineHeight: 22,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  destinationCompact: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    fontWeight: "500",
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  badgeSection: {
    alignItems: 'flex-end',
    gap: 8,
  },
  exampleBadgeCompact: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  durationBadgeCompact: {
    backgroundColor: 'rgba(33, 150, 243, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  durationTextCompact: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  progressSectionCompact: {
    marginTop: 12,
  },
  progressBarCompact: {
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 6,
  },
  progressFillCompact: {
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 2,
  },
  progressLabelCompact: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  expandButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandedContent: {
    padding: 20,
    paddingTop: 0,
  },
  dateSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  dateText: {
    fontSize: 14,
    color: 'white',
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    gap: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statValue: {
    fontSize: 14,
    fontWeight: '700',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statLabel: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  // New styles for expanded content below image
  expandedContentBelow: {
    backgroundColor: '#fff',
  },
  routeSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fafafa",
  },
  routeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  cityContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    flex: 1,
  },
  cityText: {
    fontSize: 13,
    color: "#666",
    fontWeight: "500",
    flex: 1,
  },
  routeLine: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    flex: 0.5,
  },
  routeDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#ddd",
  },
  routeDash: {
    flex: 1,
    height: 1,
    backgroundColor: "#ddd",
    marginHorizontal: 4,
  },
  content: {
    padding: 20,
  },
  dateSectionExpanded: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    gap: 8,
  },
  dateTextExpanded: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
    flex: 1,
  },
  durationBadge: {
    backgroundColor: "#E3F2FD",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  durationText: {
    fontSize: 12,
    color: "#2196F3",
    fontWeight: "600",
  },
  statsGridExpanded: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  statCardExpanded: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    gap: 6,
  },
  statValueExpanded: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1a1a1a",
  },
  statLabelExpanded: {
    fontSize: 11,
    color: "#666",
    fontWeight: "500",
    textAlign: "center",
  },
  progressSection: {
    marginTop: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#E3F2FD",
    borderRadius: 2,
    overflow: "hidden",
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#2196F3",
    borderRadius: 2,
  },
  progressLabel: {
    fontSize: 12,
    color: "#666",
    fontWeight: "500",
  },
  // Bottom actions section styles
  bottomActionsSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 8,
    backgroundColor: "#f8f9fa",
    borderRadius: 10,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#666",
  },
  favoriteButton: {
    backgroundColor: "#FFF0F0",
  },
  favoriteButtonActive: {
    backgroundColor: "#FFE5E5",
  },
  favoriteButtonText: {
    color: "#666",
  },
  shareButton: {
    backgroundColor: "#E3F2FD",
  },
  shareButtonText: {
    color: "#2196F3",
  },
  costsButton: {
    backgroundColor: "#E3F2FD",
  },
  deleteButton: {
    backgroundColor: "#FFF0F0",
  },
  deleteButtonText: {
    color: "#FF3B30",
  },
});
