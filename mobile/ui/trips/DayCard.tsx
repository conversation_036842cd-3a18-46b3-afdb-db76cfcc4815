import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { DayProgress } from "../../lib/types";
import { timeAgo } from "../../lib/utils/date";
import { router } from "expo-router";

interface DayCardProps {
  day: DayProgress;
  isSelected: boolean;
  onSelect: () => void;
  onInfoPress: () => void;
  tripId: string;
}

export default function DayCard({
  day,
  isSelected,
  onSelect,
  onInfoPress,
  tripId,
}: DayCardProps) {
  const isGenerating = day.is_generating;
  const wasSuccessfullyGenerated = day.tries > 0 && day.finished_at;
  const generatedTimeAgo = timeAgo(day.finished_at?.toString() ?? "");

  const getStatusColor = () => {
    if (day.is_generating) return "#5856D6";
    if (day.error) return "#FF3B30";
    if (day.finished_at && !day.error) return "#34C759";
    return "#8E8E93";
  };

  const getStatusIcon = () => {
    if (day.is_generating) return "reload";
    if (day.error) return "alert-circle";
    if (day.finished_at && !day.error) return "checkmark-circle";
    return "time";
  };

  const getStatusLabel = () => {
    if (day.is_generating) return "Currently generating itinerary";
    if (day.error) return "Failed to generate itinerary";
    if (day.finished_at && !day.error)
      return "Itinerary generated successfully";
    return "Itinerary not generated yet";
  };

  const statusColor = getStatusColor();
  const statusLabel = getStatusLabel();

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          isSelected && styles.selectedButton,
          {
            shadowColor: statusColor,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
            elevation: 3,
          },
        ]}
        onPress={onSelect}
        disabled={isGenerating}
        accessibilityRole="button"
        accessibilityState={{ selected: isSelected, disabled: isGenerating }}
        accessibilityLabel={`Day ${day.day} - ${statusLabel}`}
      >
        <View style={styles.content}>
          <Text
            style={[
              styles.dayText,
              isSelected && styles.selectedText,
              // Ensure text contrast meets WCAG standards
              { color: isSelected ? "#FFFFFF" : "#000000" },
            ]}
          >
            Day {day.day}
          </Text>
          {day.is_generating ? (
            <>
              <ActivityIndicator size="small" color={statusColor} />
              <Text style={styles.generatingText}>Generating...</Text>
            </>
          ) : (
            <Ionicons
              name={getStatusIcon()}
              size={24}
              color={isSelected ? "#FFFFFF" : statusColor}
              style={styles.statusIcon}
            />
          )}
          {wasSuccessfullyGenerated && (
            <Text
              style={[
                styles.timeText,
                isSelected && styles.selectedText,
                { color: isSelected ? "#FFFFFF" : "#666666" },
              ]}
            >
              {generatedTimeAgo}
            </Text>
          )}
        </View>
        {isGenerating && <View style={styles.generatingOverlay} />}
      </TouchableOpacity>

      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onInfoPress}
          accessibilityRole="button"
          accessibilityLabel="View day details"
        >
          <Ionicons name="information-circle" size={24} color="#007AFF" />
        </TouchableOpacity>

        {wasSuccessfullyGenerated && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() =>
              router.push({
                pathname: "/daily-itinerary",
                params: { tripId, day: day.day },
              })
            }
            accessibilityRole="button"
            accessibilityLabel="View daily itinerary"
          >
            <Ionicons name="calendar" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "31%",
    margin: "1%",
  },
  button: {
    width: "100%",
    aspectRatio: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: "relative",
  },
  selectedButton: {
    backgroundColor: "#007AFF",
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    padding: 8,
  },
  dayText: {
    fontSize: 28,
    fontWeight: "700",
    color: "#000000",
    marginBottom: 4,
  },
  selectedText: {
    color: "#FFFFFF",
  },
  statusIcon: {
    marginVertical: 4,
  },
  timeText: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 4,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
    paddingHorizontal: 4,
  },
  actionButton: {
    padding: 4,
  },
  generatingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(88, 86, 214, 0.1)",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  generatingText: {
    color: "#5856D6",
    fontSize: 12,
    fontWeight: "600",
    marginTop: 4,
  },
});
