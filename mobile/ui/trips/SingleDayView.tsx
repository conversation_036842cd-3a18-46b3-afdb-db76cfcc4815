import React from "react";
import { ScrollView, StyleSheet, Text, View, TouchableOpacity, Linking } from "react-native";
import { DailyItinerary, DbTrip } from "../../lib/types";
import ActivityItem from "../itinerary/ActivityItem";

interface SingleDayViewProps {
  dayData: DailyItinerary;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
  trip?: DbTrip; // Optional for backward compatibility
}

/**
 * Component for displaying a single day's activities directly
 */
const SingleDayView: React.FC<SingleDayViewProps> = ({
  dayData,
  completedActivities,
  onToggleCompletion,
  trip,
}) => {
  const [selectedCity, setSelectedCity] = React.useState<string | null>(null);

  // Set default selected city when component loads or dayData changes
  React.useEffect(() => {
    if (dayData && dayData.activities && dayData.activities.length > 0) {
      // Sort activities by start time to determine chronological order
      const sortedActivities = dayData.activities
        .filter(activity => activity.city && activity.startTime)
        .sort((a, b) => {
          const timeA = a.startTime || '00:00';
          const timeB = b.startTime || '00:00';
          return timeA.localeCompare(timeB);
        });

      const startCity = sortedActivities[0]?.city;
      if (startCity && selectedCity === null) {
        setSelectedCity(startCity);
      }
    }
  }, [dayData, selectedCity]);

  const handleBookTour = async (cityName: string) => {
    try {
      const url = `https://www.getyourguide.com/s?partner_id=EQK3T6V&cmp=share_to_earn&q=${encodeURIComponent(cityName)}`;
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error opening GetYourGuide:', error);
    }
  };

  const getCityDateRangeFromItinerary = (cityName: string) => {
    if (!trip?.itinerary) {
      return {
        checkInDate: dayData.date,
        checkOutDate: new Date(new Date(dayData.date).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };
    }

    // Find all days that have activities in this city
    const cityDays = trip.itinerary.filter(day =>
      day.activities && day.activities.some(activity =>
        activity.city?.toLowerCase() === cityName.toLowerCase()
      )
    );

    if (cityDays.length === 0) {
      return {
        checkInDate: dayData.date,
        checkOutDate: new Date(new Date(dayData.date).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };
    }

    // Get first and last dates
    const dates = cityDays.map(day => day.date).sort();
    const checkInDate = dates[0];
    const lastDate = dates[dates.length - 1];
    const checkOutDate = new Date(new Date(lastDate).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    return { checkInDate, checkOutDate };
  };

  const getCityDateRange = (cityName: string) => {
    if (!trip?.highLevelPlan) {
      // Fallback: use actual itinerary to find city date range
      return getCityDateRangeFromItinerary(cityName);
    }

    try {
      const parsedPlan = JSON.parse(trip.highLevelPlan);
      const itinerary = parsedPlan.itinerary || [];

      // Find all days where the user is in this city (either start or end city)
      const cityDays = itinerary.filter((day: any) => {
        const isStartCity = day.start_city?.toLowerCase() === cityName.toLowerCase();
        const isEndCity = day.end_city?.toLowerCase() === cityName.toLowerCase();
        return isStartCity || isEndCity;
      });

      if (cityDays.length === 0) {
        // Fallback if city not found in high-level plan
        return getCityDateRangeFromItinerary(cityName);
      }

      // Get first and last dates for this city
      const dates = cityDays.map((day: any) => day.date).sort();
      const checkInDate = dates[0];
      const lastDate = dates[dates.length - 1];

      // Check-out is the day after the last day in the city
      const checkOutDate = new Date(new Date(lastDate).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      return { checkInDate, checkOutDate };
    } catch (error) {
      console.error('Error parsing high-level plan:', error);
      // Fallback to itinerary-based calculation
      return getCityDateRangeFromItinerary(cityName);
    }
  };

  const handleBookAccommodation = async (cityName: string) => {
    try {
      const { checkInDate, checkOutDate } = getCityDateRange(cityName);

      // Get number of adults from trip details (default to 2 if not available)
      const adults = trip?.tripDetails?.people?.adults || 2;

      // Add country information to avoid confusion with cities in other countries
      const country = trip?.tripDetails?.destination || '';
      const cityWithCountry = country ? `${cityName}, ${country}` : cityName;

      const url = `https://expedia.com/Hotel-Search?destination=${encodeURIComponent(cityWithCountry)}&startDate=${checkInDate}&endDate=${checkOutDate}&rooms=1&adults=${adults}&rfrr=ZMHK6lp`;

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error opening Expedia:', error);
    }
  };

  if (!dayData || !dayData.activities || dayData.activities.length === 0) {
    return (
      <View style={styles.emptyState}>
        <Text style={styles.emptyStateText}>
          No activities for this day
        </Text>
      </View>
    );
  }

  // Get start and end cities based on chronological order of activities
  const dayDate = dayData.date;

  // Sort activities by start time to determine chronological order
  const sortedActivities = dayData.activities
    .filter(activity => activity.city && activity.startTime)
    .sort((a, b) => {
      const timeA = a.startTime || '00:00';
      const timeB = b.startTime || '00:00';
      return timeA.localeCompare(timeB);
    });

  const startCity = sortedActivities[0]?.city;
  const endCity = sortedActivities[sortedActivities.length - 1]?.city;

  // Determine unique cities for the day
  const uniqueCities: { city: string; label: string }[] = [];
  if (startCity) {
    uniqueCities.push({ city: startCity, label: 'Start' });
    if (endCity && endCity !== startCity) {
      uniqueCities.push({ city: endCity, label: 'End' });
    }
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {uniqueCities.length > 0 && dayDate && (
        <View style={styles.dayHeader}>
          {/* City flow display */}
          <View style={styles.cityFlow}>
            <TouchableOpacity
              style={[
                styles.cityButton,
                selectedCity === startCity && styles.cityButtonSelected
              ]}
              onPress={() => setSelectedCity(selectedCity === startCity ? null : startCity)}
            >
              <Text style={[
                styles.cityButtonText,
                selectedCity === startCity && styles.cityButtonTextSelected
              ]}>
                {startCity}
              </Text>
            </TouchableOpacity>

            {endCity && endCity !== startCity && (
              <>
                <Text style={styles.arrowText}>→</Text>
                <TouchableOpacity
                  style={[
                    styles.cityButton,
                    selectedCity === endCity && styles.cityButtonSelected
                  ]}
                  onPress={() => setSelectedCity(selectedCity === endCity ? null : endCity)}
                >
                  <Text style={[
                    styles.cityButtonText,
                    selectedCity === endCity && styles.cityButtonTextSelected
                  ]}>
                    {endCity}
                  </Text>
                </TouchableOpacity>
              </>
            )}
          </View>

          {/* Booking buttons for selected city */}
          {selectedCity && (
            <View style={styles.bookingSection}>
              <Text style={styles.bookingTitle}>Book in {selectedCity}</Text>
              <View style={styles.dayActions}>
                <TouchableOpacity
                  style={styles.bookTourButton}
                  onPress={() => handleBookTour(selectedCity)}
                >
                  <Text style={styles.bookTourText}>Book a tour</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.bookAccommodationButton}
                  onPress={() => handleBookAccommodation(selectedCity)}
                >
                  <Text style={styles.bookAccommodationText}>Book hotel</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      )}

      {/* All activities for the day */}
      {dayData.activities.map((activity, index) => (
        <ActivityItem
          key={activity.id || index}
          activity={activity}
          isCompleted={completedActivities.has(activity.id)}
          onToggleCompletion={onToggleCompletion}
        />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  dayHeader: {
    paddingVertical: 12,
    marginBottom: 16,
  },
  cityFlow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
    gap: 12,
  },
  cityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#f8f9fa",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  cityButtonSelected: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  cityButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  cityButtonTextSelected: {
    color: "#fff",
  },
  arrowText: {
    fontSize: 18,
    color: "#666",
    fontWeight: "bold",
  },
  bookingSection: {
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  bookingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    textAlign: "center",
    marginBottom: 12,
  },
  dayActions: {
    flexDirection: "row",
    gap: 12,
  },
  bookTourButton: {
    flex: 1,
    backgroundColor: "#FF6B35",
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  bookTourText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  bookAccommodationButton: {
    flex: 1,
    backgroundColor: "#007AFF",
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  bookAccommodationText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});

export default SingleDayView;
