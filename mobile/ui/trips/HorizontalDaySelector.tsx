import React from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { DbTrip } from "../../lib/types";
import { TripService } from "../../lib/services/trip.service";
import { useGlobalInsufficientDays } from "../../lib/hooks/useGlobalInsufficientDays";

interface HorizontalDaySelectorProps {
  trip: DbTrip | null;
  selectedDayIndex: number;
  onDaySelect: (dayIndex: number) => void;
  onInsufficientBalance?: () => void;
  isGenerating?: boolean;
  setIsGenerating?: (isGenerating: boolean) => void;
}

const tripService = TripService.getInstance();


/**
 * Horizontal scrollable day selector component
 * Shows days with dates in a format similar to the screenshot
 */
const HorizontalDaySelector: React.FC<HorizontalDaySelectorProps> = ({
  trip,
  selectedDayIndex,
  onDaySelect,
  onInsufficientBalance,
  isGenerating: parentIsGenerating,
  setIsGenerating,
}) => {
  const { checkAndHandleInsufficientBalance } = useGlobalInsufficientDays();
  const isGenerating = parentIsGenerating ?? trip?.daysProgress.some((day) => day.is_generating);

  const formatDayDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    return { day, month };
  };

  const getDayName = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { weekday: "short" }).toUpperCase();
  };


  // Check if trip generation has stopped and needs restart
  const needsRestart = React.useMemo(() => {
    if (!trip) return false;
    if (isGenerating) return false;

    // Check if trip has failed days
    const hasFailedDays = trip.daysProgress?.some((day) => day.error && !day.finished_at);

    // Check if trip is incomplete (has fewer days than expected)
    const expectedDays = trip.tripDetails?.totalDays || 0;
    const actualDays = trip.itinerary?.length || 0;
    const isIncomplete = expectedDays > 0 && actualDays < expectedDays;

    return hasFailedDays || isIncomplete;
  }, [trip, isGenerating]);

  const handleRestartGeneration = async () => {
    // Prevent multiple simultaneous calls
    if (!trip?._id || isGenerating) {
      console.log("Generation blocked:", {
        hasTrip: !!trip?._id,
        isGenerating,
      });
      return;
    }

    setIsGenerating?.(true);
    try {
      await tripService.resumeGeneration(trip._id);
      // Success - the parent state will be updated through trip data changes
      // The useTripViewState hook will detect the trip.daysProgress changes and update isGenerating
    } catch (error) {
      console.error("Error restarting generation:", error);

      // Check if this is an insufficient balance error
      const isInsufficientBalanceError =
        (error as any)?.response?.status === 402 ||
        (error as any)?.status === 402;

      // Check if this is a "generation already in progress" error
      const isAlreadyGeneratingError =
        (error as any)?.message?.includes('already in progress') ||
        (error as any)?.response?.data?.message?.includes('already in progress');

      if (isInsufficientBalanceError) {
        // Use global handler first
        checkAndHandleInsufficientBalance(error).then((handled) => {
          // Also call the local callback for backward compatibility
          if (!handled && onInsufficientBalance) {
            onInsufficientBalance();
          }
        }).catch((error) => {
          console.error("Error handling insufficient balance:", error);
          // Fallback to local callback
          if (onInsufficientBalance) {
            onInsufficientBalance();
          }
        });
      } else if (isAlreadyGeneratingError) {
        console.log("Generation already in progress, ignoring duplicate request");
      }

      // Reset state on error
      setIsGenerating?.(false);
    }
  };

  const getGenerationButtonState = () => {
    if (isGenerating) {
      return {
        icon: "reload",
        text: "Generating...",
        color: "#5856D6",
        disabled: true,
        showSpinner: true,
      };
    }

    if (needsRestart) {
      return {
        icon: "play",
        text: "Continue",
        color: "#FF9500",
        disabled: false,
        showSpinner: false,
      };
    }

    return {
      icon: "checkmark-circle",
      text: "Complete",
      color: "#34C759",
      disabled: true,
      showSpinner: false,
    };
  };



  const generationButtonState = getGenerationButtonState();

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {/* Generation Button */}
        <TouchableOpacity
          style={[
            styles.dayItem,
            styles.generationButton,
            {
              borderColor: generationButtonState.color,
              borderWidth: 2,
            },
          ]}
          onPress={handleRestartGeneration}
          disabled={generationButtonState.disabled}
        >
          <View style={styles.dayContent}>
            <Text style={[styles.dayName, { color: generationButtonState.color }]}>
              TRIP
            </Text>
            {generationButtonState.showSpinner ? (
              <ActivityIndicator size="small" color={generationButtonState.color} />
            ) : (
              <Ionicons
                name={generationButtonState.icon as any}
                size={24}
                color={generationButtonState.color}
              />
            )}
            <Text style={[styles.generationText, { color: generationButtonState.color }]}>
              {generationButtonState.text}
            </Text>
          </View>
        </TouchableOpacity>

        {trip?.itinerary && trip?.itinerary.length > 0 && trip.itinerary.map((dayData, index) => {
          const { day, month } = formatDayDate(dayData.date);
          const dayName = getDayName(dayData.date);
          const isSelected = index === selectedDayIndex;

          return (
            <View
              key={index}
              style={[
                styles.dayItem,
                isSelected && styles.dayItemSelected,
              ]}
            >
              <TouchableOpacity
                style={styles.dayContent}
                onPress={() => onDaySelect(index)}
                onLongPress={() =>
                  router.navigate({
                    pathname: "/daily-itinerary",
                    params: { day: dayData.day + "", tripId: trip._id },
                  })
                }
              >
                <Text
                  style={[
                    styles.dayName,
                    isSelected && styles.dayNameSelected,
                  ]}
                >
                  {dayName}
                </Text>
                <Text
                  style={[
                    styles.dayNumber,
                    isSelected && styles.dayNumberSelected,
                  ]}
                >
                  {day}.{month}.
                </Text>
              </TouchableOpacity>

              {/* Navigation button to daily itinerary */}
              <TouchableOpacity
                style={[
                  styles.navButton,
                  isSelected && styles.navButtonSelected,
                ]}
                onPress={() =>
                  router.navigate({
                    pathname: "/daily-itinerary",
                    params: { day: dayData.day + "", tripId: trip._id },
                  })
                }
              >
                <Ionicons
                  name="map"
                  size={18}
                  color={isSelected ? "#fff" : "#007AFF"}
                />
              </TouchableOpacity>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#f8f9fa",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  dayItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    minWidth: 90,
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dayContent: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  navButton: {
    padding: 6,
    marginLeft: 8,
    borderRadius: 6,
    backgroundColor: "rgba(0, 122, 255, 0.1)",
  },
  navButtonSelected: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  dayItemSelected: {
    backgroundColor: "#007AFF",
    shadowColor: "#007AFF",
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  dayName: {
    fontSize: 12,
    fontWeight: "600",
    color: "#8E8E93",
    marginBottom: 4,
  },
  dayNameSelected: {
    color: "#fff",
  },
  dayNumber: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1C1C1E",
  },
  dayNumberSelected: {
    color: "#fff",
  },
  generationButton: {
    backgroundColor: "#fff",
    borderStyle: "dashed",
  },
  generationText: {
    fontSize: 10,
    fontWeight: "600",
    marginTop: 2,
    textAlign: "center",
  },
});

export default HorizontalDaySelector;
