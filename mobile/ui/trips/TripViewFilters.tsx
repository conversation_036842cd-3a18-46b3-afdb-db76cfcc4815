import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { DbTrip } from "../../lib/types";
import { TripService } from "../../lib/services/trip.service";
import { getAvailableCities } from "../../ui/trips/TripViewUtils";

interface TripViewFiltersProps {
  trip: DbTrip;
  selectedDay: number;
  selectedCity: string;
  selectedStatus: "all" | "completed" | "pending";
  showDayPicker: boolean;
  showCityPicker: boolean;
  showStatusPicker: boolean;
  setShowDayPicker: (show: boolean) => void;
  setShowCityPicker: (show: boolean) => void;
  setShowStatusPicker: (show: boolean) => void;
  onDaySelected: (day: number) => void;
  onCitySelected: (city: string) => void;
  onStatusSelected: (status: "all" | "completed" | "pending") => void;
}

const TripViewFilters: React.FC<TripViewFiltersProps> = ({
  trip,
  selectedDay,
  selectedCity,
  selectedStatus,
  showDayPicker,
  showCityPicker,
  showStatusPicker,
  setShowDayPicker,
  setShowCityPicker,
  setShowStatusPicker,
  onDaySelected,
  onCitySelected,
  onStatusSelected,
}) => {
  return (
    <View style={styles.filtersContainer}>
      <DayFilter
        trip={trip}
        selectedDay={selectedDay}
        showDayPicker={showDayPicker}
        setShowDayPicker={setShowDayPicker}
        setShowCityPicker={setShowCityPicker}
        setShowStatusPicker={setShowStatusPicker}
        onDaySelected={onDaySelected}
      />

      <CityFilter
        trip={trip}
        selectedCity={selectedCity}
        showCityPicker={showCityPicker}
        setShowDayPicker={setShowDayPicker}
        setShowCityPicker={setShowCityPicker}
        setShowStatusPicker={setShowStatusPicker}
        onCitySelected={onCitySelected}
      />

      <StatusFilter
        selectedStatus={selectedStatus}
        showStatusPicker={showStatusPicker}
        setShowDayPicker={setShowDayPicker}
        setShowCityPicker={setShowCityPicker}
        setShowStatusPicker={setShowStatusPicker}
        onStatusSelected={onStatusSelected}
      />
    </View>
  );
};

interface DayFilterProps {
  trip: DbTrip;
  selectedDay: number;
  showDayPicker: boolean;
  setShowDayPicker: (show: boolean) => void;
  setShowCityPicker: (show: boolean) => void;
  setShowStatusPicker: (show: boolean) => void;
  onDaySelected: (day: number) => void;
}

const DayFilter: React.FC<DayFilterProps> = ({
  trip,
  selectedDay,
  showDayPicker,
  setShowDayPicker,
  setShowCityPicker,
  setShowStatusPicker,
  onDaySelected,
}) => {
  return (
    <View style={styles.filterWrapper}>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => {
          setShowDayPicker(!showDayPicker);
          setShowCityPicker(false);
          setShowStatusPicker(false);
        }}
      >
        <Text style={styles.filterButtonText}>
          {selectedDay === 0 ? "All Days" : `Day ${selectedDay}`}
        </Text>
        <Ionicons
          name={showDayPicker ? "chevron-up" : "chevron-down"}
          size={20}
          color="#666"
        />
      </TouchableOpacity>
      {showDayPicker && trip?.itinerary && (
        <View style={styles.dropdownMenu}>
          <ScrollView>
            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={() => {
                onDaySelected(0);
                setShowDayPicker(false);
              }}
            >
              <Text
                style={[
                  styles.dropdownText,
                  selectedDay === 0 && styles.selectedText,
                ]}
              >
                All Days
              </Text>
            </TouchableOpacity>
            {trip.itinerary.map(({ day }) => (
              <TouchableOpacity
                key={day}
                style={styles.dropdownItem}
                onPress={() => {
                  onDaySelected(day);
                  setShowDayPicker(false);
                }}
              >
                <Text
                  style={[
                    styles.dropdownText,
                    selectedDay === day && styles.selectedText,
                  ]}
                >{`Day ${day}`}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

interface CityFilterProps {
  trip: DbTrip;
  selectedCity: string;
  showCityPicker: boolean;
  setShowDayPicker: (show: boolean) => void;
  setShowCityPicker: (show: boolean) => void;
  setShowStatusPicker: (show: boolean) => void;
  onCitySelected: (city: string) => void;
}

const tripService = TripService.getInstance();

const CityFilter: React.FC<CityFilterProps> = ({
  trip,
  selectedCity,
  showCityPicker,
  setShowDayPicker,
  setShowCityPicker,
  setShowStatusPicker,
  onCitySelected,
}) => {
  const [cities, setCities] = useState<string[]>(["all"]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch cities from the API when the picker is shown
  useEffect(() => {
    if (showCityPicker && trip?._id) {
      setLoading(true);
      setError(null);

      tripService
        .fetchTripCities(trip._id)
        .then((result) => {
          // Add 'all' to the beginning of the array
          setCities(["all", ...result]);
        })
        .catch((err) => {
          console.error("Error fetching cities:", err);
          setError("Failed to load cities");
          // Fall back to client-side city extraction
          setCities([
            "all",
            ...getAvailableCities(trip.itinerary).filter(
              (city) => city !== "all",
            ),
          ]);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [showCityPicker, trip?._id]);

  return (
    <View style={styles.filterWrapper}>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={(e) => {
          e.stopPropagation();
          setShowCityPicker(!showCityPicker);
          setShowDayPicker(false);
          setShowStatusPicker(false);
        }}
      >
        <Text style={styles.filterButtonText}>
          {selectedCity === "all" ? "All Cities" : (selectedCity || "").toUpperCase()}
        </Text>
        <Ionicons
          name={showCityPicker ? "chevron-up" : "chevron-down"}
          size={20}
          color="#666"
        />
      </TouchableOpacity>
      {showCityPicker && trip?.itinerary && (
        <View style={styles.dropdownMenu}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#2196F3" />
              <Text style={styles.loadingText}>Loading cities...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : (
            <ScrollView>
              {cities.map((city) => (
                <TouchableOpacity
                  key={city}
                  style={styles.dropdownItem}
                  onPress={() => {
                    onCitySelected(city);
                    setShowCityPicker(false);
                  }}
                >
                  <Text
                    style={[
                      styles.dropdownText,
                      selectedCity === city && styles.selectedText,
                    ]}
                  >
                    {city === "all" ? "All Cities" : (city || "").toUpperCase()}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>
      )}
    </View>
  );
};

interface StatusFilterProps {
  selectedStatus: "all" | "completed" | "pending";
  showStatusPicker: boolean;
  setShowDayPicker: (show: boolean) => void;
  setShowCityPicker: (show: boolean) => void;
  setShowStatusPicker: (show: boolean) => void;
  onStatusSelected: (status: "all" | "completed" | "pending") => void;
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  selectedStatus,
  showStatusPicker,
  setShowDayPicker,
  setShowCityPicker,
  setShowStatusPicker,
  onStatusSelected,
}) => {
  return (
    <View style={styles.filterWrapper}>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={(e) => {
          e.stopPropagation();
          setShowStatusPicker(!showStatusPicker);
          setShowDayPicker(false);
          setShowCityPicker(false);
        }}
      >
        <Text style={styles.filterButtonText}>
          {selectedStatus === "all"
            ? "All Status"
            : selectedStatus === "completed"
              ? "Completed"
              : "Pending"}
        </Text>
        <Ionicons
          name={showStatusPicker ? "chevron-up" : "chevron-down"}
          size={20}
          color="#666"
        />
      </TouchableOpacity>
      {showStatusPicker && (
        <View style={styles.dropdownMenu}>
          <ScrollView>
            {[
              { value: "all", label: "All Status" },
              { value: "completed", label: "Completed" },
              { value: "pending", label: "Pending" },
            ].map(({ value, label }) => (
              <TouchableOpacity
                key={value}
                style={styles.dropdownItem}
                onPress={(e) => {
                  e.stopPropagation();
                  onStatusSelected(value as "all" | "completed" | "pending");
                  setShowStatusPicker(false);
                }}
              >
                <Text
                  style={[
                    styles.dropdownText,
                    selectedStatus === value && styles.selectedText,
                  ]}
                >
                  {label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  filtersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    zIndex: 2,
  },
  filterWrapper: {
    position: "relative",
    zIndex: 3,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
    minWidth: 120,
  },
  filterButtonText: {
    fontSize: 12,
    color: "#333",
    flexShrink: 1,
    flexGrow: 0,
  },
  dropdownMenu: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: "white",
    borderRadius: 8,
    marginTop: 4,
    padding: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxHeight: 200,
    zIndex: 3,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    gap: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    minWidth: 120,
  },
  dropdownText: {
    fontSize: 12,
    color: "#333",
    flexShrink: 1,
    flexGrow: 0,
  },
  selectedText: {
    color: "#2196F3",
    fontWeight: "bold",
  },
  // Loading state styles
  loadingContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    fontSize: 12,
    color: "#666",
    marginTop: 8,
  },
  // Error state styles
  errorContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  errorText: {
    fontSize: 12,
    color: "#FF6B6B",
    textAlign: "center",
  },
});

export default TripViewFilters;
