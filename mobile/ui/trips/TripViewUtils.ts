import { Activity } from "../../lib/types";
import { Region } from "../map/MapUtils";

/**
 * Adjusts the map region to show all activities
 */
export const adjustMapToShowActivities = (
  activities: Activity[],
): Region | null => {
  if (activities.length === 0) return null;

  const validCoordinates = activities
    .map((a) => a.coordinates)
    .filter(
      (coord): coord is { lat: number; lng: number } =>
        coord !== undefined &&
        typeof coord?.lat === "number" &&
        typeof coord?.lng === "number",
    );

  if (validCoordinates.length === 0) return null;

  const minLat = Math.min(...validCoordinates.map((c) => c.lat));
  const maxLat = Math.max(...validCoordinates.map((c) => c.lat));
  const minLng = Math.min(...validCoordinates.map((c) => c.lng));
  const maxLng = Math.max(...validCoordinates.map((c) => c.lng));

  const centerLat = (minLat + maxLat) / 2;
  const centerLng = (minLng + maxLng) / 2;
  const deltaLat = (maxLat - minLat) * 1.5;
  const deltaLng = (maxLng - minLng) * 1.5;

  return {
    latitude: centerLat,
    longitude: centerLng,
    latitudeDelta: Math.max(deltaLat, 0.02),
    longitudeDelta: Math.max(deltaLng, 0.02),
  };
};

/**
 * Gets the available days for filtering based on the selected city
 */
export const getAvailableDays = (itinerary: any[], city: string = "all") => {
  if (!itinerary) return [0];
  if (city === "all") return itinerary.map(({ day }) => day);

  // Check which days have activities in the selected city
  return [
    0,
    ...itinerary
      .map((dayData) => {
        const hasActivityInCity = dayData.activities?.some((activity: any) =>
          activity.city?.toLowerCase() === city.toLowerCase()
        );
        return hasActivityInCity ? dayData.day : null;
      })
      .filter((day): day is number => day !== null),
  ];
};

/**
 * Gets the available cities for filtering based on the selected day
 * This is a client-side fallback in case the API call fails
 */
export const getAvailableCities = (itinerary: any[], day: number = 0) => {
  if (!itinerary) return ["all"];
  const cities = new Set<string>();
  cities.add("all");

  // Get cities from activities only
  if (day === 0) {
    // Show all days
    itinerary.forEach((d) => {
      d.activities.forEach((activity: any) => {
        if (activity.city) {
          cities.add(activity.city);
        }
      });
    });
  } else {
    // Show specific day
    const dayData = itinerary[day - 1];
    if (dayData) {
      dayData.activities.forEach((activity: any) => {
        if (activity.city) {
          cities.add(activity.city);
        }
      });
    }
  }

  return Array.from(cities);
};

/**
 * Gets the filtered activities based on the selected filters
 */
export const getFilteredActivities = (
  itinerary: any[],
  completedActivities: Set<string>,
  city: string = "all",
  day: number = 0,
  status: "all" | "completed" | "pending" = "all",
) => {
  if (!itinerary) return [];

  // Function to check if an activity belongs to the selected city
  const activityMatchesCity = (
    activity: Activity,
    selectedCity: string,
  ): boolean => {
    if (selectedCity === "all") return true;

    // All activities now have a city field
    return activity.city === selectedCity;
  };

  let activities: Activity[] = [];

  if (day === 0) {
    // Show all days
    activities = itinerary.flatMap((d) => {
      return d.activities.filter((activity: Activity) => {
        const activityStatus = completedActivities.has(activity.id)
          ? "completed"
          : "pending";
        const statusMatch = status === "all" || activityStatus === status;
        const cityMatch = activityMatchesCity(activity, city);

        return cityMatch && statusMatch;
      });
    });
  } else {
    // Show specific day
    const dayData = itinerary[day - 1];
    if (!dayData) return [];

    activities = (dayData.activities || []).filter((activity: Activity) => {
      const activityStatus = completedActivities.has(activity.id)
        ? "completed"
        : "pending";
      const statusMatch = status === "all" || activityStatus === status;
      const cityMatch = activityMatchesCity(activity, city);

      return cityMatch && statusMatch;
    });
  }

  return activities;
};

/**
 * Gets the appropriate icon for an activity type
 */
export const getActivityIcon = (type: string) => {
  switch (type) {
    case "activity":
      return "walk-outline";
    case "meal":
      return "restaurant-outline";
    default:
      return "ellipse-outline";
  }
};
