import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { DbTrip } from "../../lib/types";
import { TripService } from "../../lib/services/trip.service";
import Modal from "../common/Modal";

interface TripActionsProps {
  trip: DbTrip;
  onDelete: (id: string) => void;
  onToggleFavorite?: (id: string) => void;
  isFavorite?: boolean;
  onClose?: () => void;
  onExampleStatusChange?: () => void;
}

export const TripActions = ({
  trip,
  onDelete,
  onToggleFavorite,
  isFavorite = false,
  onClose,
  onExampleStatusChange,
}: TripActionsProps) => {
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const tripService = TripService.getInstance();

  const handleShare = async () => {
    router.navigate({
      pathname: "/share",
      params: {
        tripId: trip._id,
      },
    });
    onClose?.();
  };

  const handleShowCosts = () => {
    router.navigate({
      pathname: "/costs",
      params: {
        tripId: trip._id,
      },
    });
    onClose?.();
    setShowMoreOptions(false);
  };

  const handleDeleteTrip = () => {
    onDelete(trip._id);
    setShowMoreOptions(false);
  };

  const handleToggleExample = async () => {
    try {
      await tripService.updateExampleStatus(trip._id, !trip.isExample);
      onExampleStatusChange?.();
      setShowMoreOptions(false);
    } catch (error) {
      console.error("Failed to update example status:", error);
    }
  };

  return (
    <>
      <View style={styles.container}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.favoriteButton,
            isFavorite && styles.favoriteButtonActive,
          ]}
          onPress={() => onToggleFavorite?.(trip._id)}
        >
          <Ionicons
            name={isFavorite ? "heart" : "heart-outline"}
            size={24}
            color={isFavorite ? "#FF3B30" : "#666"}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.shareButton]}
          onPress={handleShare}
        >
          <Ionicons name="share-social-outline" size={24} color="#2196F3" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.moreButton]}
          onPress={() => setShowMoreOptions(true)}
        >
          <Ionicons name="ellipsis-horizontal" size={24} color="#666" />
        </TouchableOpacity>
      </View>

      {/* More Options Modal */}
      <Modal
        visible={showMoreOptions}
        onClose={() => setShowMoreOptions(false)}
        initialSnap="half"
      >
        <View style={styles.moreOptionsContainer}>
          <TouchableOpacity
            style={styles.moreOptionButton}
            onPress={handleShowCosts}
          >
            <Ionicons name="wallet-outline" size={22} color="#2196F3" />
            <Text style={styles.moreOptionText}>Costs</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.moreOptionButton}
            onPress={handleToggleExample}
          >
            <Ionicons
              name={trip.isExample ? "star" : "star-outline"}
              size={22}
              color="#FFD700"
            />
            <Text style={[styles.moreOptionText, { color: "#FFD700" }]}>
              {trip.isExample ? "Remove from Examples" : "Make Example"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.moreOptionButton, styles.deleteButton]}
            onPress={handleDeleteTrip}
          >
            <Ionicons name="trash-outline" size={22} color="#FF3B30" />
            <Text style={[styles.moreOptionText, { color: "#FF3B30" }]}>
              Delete Trip
            </Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    gap: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 50,
    backgroundColor: "#f8f8f8",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  favoriteButton: {
    marginRight: "auto",
    backgroundColor: "#FFF0F0",
  },
  favoriteButtonActive: {
    backgroundColor: "#FFE5E5",
  },
  shareButton: {
    backgroundColor: "#E3F2FD",
  },
  moreButton: {
    backgroundColor: "#f8f8f8",
  },
  moreOptionsContainer: {
    padding: 16,
    gap: 12,
  },
  moreOptionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    gap: 12,
    backgroundColor: "#f8f8f8",
    borderRadius: 12,
  },
  deleteButton: {
    marginTop: 8,
    backgroundColor: "#FFF0F0",
  },
  moreOptionText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#2196F3",
  },
  modalContainer: {
    padding: 16,
    gap: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#000",
    marginBottom: 8,
  },
  modalButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    gap: 12,
    backgroundColor: "#f8f8f8",
    borderRadius: 12,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#2196F3",
  },
});
