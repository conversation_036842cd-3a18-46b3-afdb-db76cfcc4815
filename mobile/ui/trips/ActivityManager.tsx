import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  Animated as RNAnimated,
  GestureResponderEvent,
} from "react-native";
import { TripService } from "../../lib/services/trip.service";
import {
  successHapticFeedback,
  mediumHapticFeedback,
  lightHapticFeedback,
} from "../../lib/utils/haptics";

const tripService = TripService.getInstance();

interface UseActivityCompletionProps {
  tripId: string;
  onTripUpdate?: () => void;
  initialCompletedActivities?: string[];
}

export const useActivityCompletion = ({
  tripId,
  onTripUpdate,
  initialCompletedActivities = [],
}: UseActivityCompletionProps) => {
  const [completedActivities, setCompletedActivities] = React.useState<
    Set<string>
  >(new Set(initialCompletedActivities));
  const [activityInProgress, setActivityInProgress] = React.useState<
    string | null
  >(null);
  const progressAnimation = React.useState(new RNAnimated.Value(0))[0];
  const completionAnimation = React.useState(new RNAnimated.Value(0))[0];

  // Load completed activities when the component mounts
  useEffect(() => {
    if (!tripId) return;

    const loadCompletedActivities = async () => {
      try {
        const trip = await tripService.fetchTripById(tripId);
        if (trip && trip.itinerary) {
          // Find all completed activities in the itinerary
          const completedIds = new Set<string>();
          trip.itinerary.forEach((day) => {
            day.activities.forEach((activity) => {
              if (activity.completed) {
                completedIds.add(activity.id);
              }
            });
          });
          setCompletedActivities(completedIds);
        }
      } catch (error) {
        console.error("Error loading completed activities:", error);
      }
    };

    loadCompletedActivities();
  }, [tripId, tripService]);

  const handleActivityCompletion = async (
    tripId: string,
    activityId: string,
  ) => {
    try {
      // Trigger success haptic feedback
      successHapticFeedback();

      // First update local state for immediate feedback
      setCompletedActivities((prev) => {
        const newSet = new Set(prev);
        newSet.add(activityId);
        return newSet;
      });

      // Then update the backend
      await tripService.updateActivityCompletion(tripId, activityId, true);
      onTripUpdate?.();
    } catch (error) {
      // Revert local state if the backend update fails
      setCompletedActivities((prev) => {
        const newSet = new Set(prev);
        newSet.delete(activityId);
        return newSet;
      });
      console.error("Error completing activity:", error);
    }
  };

  const handleActivityUncompletion = async (
    tripId: string,
    activityId: string,
  ) => {
    try {
      // Trigger medium haptic feedback for uncompleting
      mediumHapticFeedback();

      // First update local state for immediate feedback
      setCompletedActivities((prev) => {
        const newSet = new Set(prev);
        newSet.delete(activityId);
        return newSet;
      });

      // Then update the backend
      await tripService.updateActivityCompletion(tripId, activityId, false);
      onTripUpdate?.();
    } catch (error) {
      // Revert local state if the backend update fails
      setCompletedActivities((prev) => {
        const newSet = new Set(prev);
        newSet.add(activityId);
        return newSet;
      });
      console.error("Error uncompleting activity:", error);
    }
  };

  const startCompletionAnimation = (tripId: string, activityId: string) => {
    if (completedActivities.has(activityId)) return;

    // Trigger light haptic feedback when starting the animation
    lightHapticFeedback();

    setActivityInProgress(activityId);
    progressAnimation.setValue(0);

    RNAnimated.timing(progressAnimation, {
      toValue: 1,
      duration: 2000,
      useNativeDriver: true,
    }).start(({ finished }) => {
      if (finished) {
        handleActivityCompletion(tripId, activityId);
      }
      setActivityInProgress(null);
    });
  };

  const cancelCompletionAnimation = () => {
    progressAnimation.setValue(0);
    setActivityInProgress(null);
  };

  const handleLongPress =
    (tripId: string, activityId: string, isCompleted: boolean) =>
    (event: GestureResponderEvent) => {
      event.stopPropagation();
      if (isCompleted) {
        Alert.alert(
          "Uncomplete Activity",
          "Do you want to mark this activity as not completed?",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Uncomplete",
              style: "destructive",
              onPress: async () => {
                try {
                  const updatedTrip =
                    await tripService.updateActivityCompletion(
                      tripId,
                      activityId,
                      false,
                    );
                  if (updatedTrip) {
                    onTripUpdate?.();
                    setCompletedActivities((prev) => {
                      const newSet = new Set(prev);
                      newSet.delete(activityId);
                      return newSet;
                    });
                  }
                } catch (error) {
                  console.error("Failed to update activity completion:", error);
                  Alert.alert(
                    "Error",
                    "Failed to update activity completion status",
                  );
                }
              },
            },
          ],
        );
      } else if (!activityInProgress) {
        startCompletionAnimation(tripId, activityId);
      }
    };

  const handlePressOut = (activityId: string, isCompleted: boolean) => () => {
    if (!isCompleted && activityInProgress === activityId) {
      cancelCompletionAnimation();
    }
  };

  // Function to toggle activity completion with a quick animation
  const toggleActivityCompletion = async (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => {
    // Trigger appropriate haptic feedback based on the action
    if (isCompleted) {
      mediumHapticFeedback(); // Feedback for uncompleting
    } else {
      successHapticFeedback(); // Feedback for completing
    }

    // Play a quick animation
    completionAnimation.setValue(0);
    RNAnimated.timing(completionAnimation, {
      toValue: 1,
      duration: 300, // Quick animation
      useNativeDriver: true,
    }).start();

    // Call the appropriate handler
    if (isCompleted) {
      await handleActivityUncompletion(tripId, activityId);
    } else {
      await handleActivityCompletion(tripId, activityId);
    }
  };

  return {
    completedActivities,
    setCompletedActivities,
    activityInProgress,
    progressAnimation,
    completionAnimation,
    handleActivityCompletion,
    handleActivityUncompletion,
    handleLongPress,
    handlePressOut,
    toggleActivityCompletion,
  };
};
