import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { DbTrip } from "../../lib/types";
import GeneratedItineraryByCity from "../itinerary/GeneratedItineraryByCity";
import HorizontalDaySelector from "./HorizontalDaySelector.tsx";
import SingleDayView from "./SingleDayView.tsx";

interface TripListViewProps {
  trip: DbTrip;
  itineraryViewMode: "day" | "city";
  setItineraryViewMode: (mode: "day" | "city") => void;
  expandedCities: { [key: string]: boolean };
  toggleCity: (cityKey: string) => void;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
  onInsufficientBalance?: () => void;
  isGenerating?: boolean;
  setIsGenerating?: (isGenerating: boolean) => void;
}

const TripListView: React.FC<TripListViewProps> = ({
  trip,
  itineraryViewMode,
  setItineraryViewMode,
  expandedCities,
  toggleCity,
  completedActivities,
  onToggleCompletion,
  onInsufficientBalance,
  isGenerating,
  setIsGenerating,
}) => {
  return (
    <>
      <View style={styles.viewModeToggle}>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            itineraryViewMode === "day" && styles.viewModeButtonActive,
          ]}
          onPress={() => setItineraryViewMode("day")}
        >
          <Text
            style={[
              styles.viewModeButtonText,
              itineraryViewMode === "day" && styles.viewModeButtonTextActive,
            ]}
          >
            By Day
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            itineraryViewMode === "city" && styles.viewModeButtonActive,
          ]}
          onPress={() => setItineraryViewMode("city")}
        >
          <Text
            style={[
              styles.viewModeButtonText,
              itineraryViewMode === "city" && styles.viewModeButtonTextActive,
            ]}
          >
            By City
          </Text>
        </TouchableOpacity>
      </View>

      {itineraryViewMode === "day" ? (
        <DayModeView
          trip={trip}
          completedActivities={completedActivities}
          onToggleCompletion={onToggleCompletion}
          onInsufficientBalance={onInsufficientBalance}
          isGenerating={isGenerating}
          setIsGenerating={setIsGenerating}
        />
      ) : (
        <CityModeView
          trip={trip}
          expandedCities={expandedCities}
          toggleCity={toggleCity}
          completedActivities={completedActivities}
          onToggleCompletion={onToggleCompletion}
        />
      )}
    </>
  );
};

interface DayModeViewProps {
  trip: DbTrip;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
  onInsufficientBalance?: () => void;
  isGenerating?: boolean;
  setIsGenerating?: (isGenerating: boolean) => void;
}

const DayModeView: React.FC<DayModeViewProps> = ({
  trip,
  completedActivities,
  onToggleCompletion,
  onInsufficientBalance,
  isGenerating,
  setIsGenerating,
}) => {
  const [selectedDayIndex, setSelectedDayIndex] = useState<number>(0);

  // Get the selected day's data
  const selectedDayData = trip?.itinerary?.[selectedDayIndex];

  return (
    <>
      {/* Horizontal Day Selector */}
      <HorizontalDaySelector
        trip={trip}
        selectedDayIndex={selectedDayIndex}
        onDaySelect={setSelectedDayIndex}
        onInsufficientBalance={onInsufficientBalance}
        isGenerating={isGenerating}
        setIsGenerating={setIsGenerating}
      />

      {/* Activities for selected day */}
      <View style={styles.selectedDayContent}>
        {selectedDayData ? (
          <SingleDayView
            dayData={selectedDayData}
            completedActivities={completedActivities}
            onToggleCompletion={onToggleCompletion}
            trip={trip}
          />
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No activities for this day
            </Text>
          </View>
        )}
      </View>
    </>
  );
};

interface CityModeViewProps {
  trip: DbTrip;
  expandedCities: { [key: string]: boolean };
  toggleCity: (city: string) => void;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
}

const CityModeView: React.FC<CityModeViewProps> = ({
  trip,
  expandedCities,
  toggleCity,
  completedActivities,
  onToggleCompletion,
}) => {
  return (
    <GeneratedItineraryByCity
      trip={trip}
      expandedCities={expandedCities}
      onToggleExpand={toggleCity}
      completedActivities={completedActivities}
      onToggleCompletion={onToggleCompletion}
    />
  );
};

const styles = StyleSheet.create({
  viewModeToggle: {
    flexDirection: "row",
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    margin: 16,
    padding: 4,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: "center",
  },
  viewModeButtonActive: {
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  viewModeButtonText: {
    fontSize: 14,
    color: "#666",
  },
  viewModeButtonTextActive: {
    color: "#000",
    fontWeight: "500",
  },
  selectedDayContent: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});

export default TripListView;
