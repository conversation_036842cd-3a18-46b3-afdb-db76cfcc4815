import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { DbTrip } from "../../lib/types";
import { TripActions } from "./TripActions";
import { calculateTripDuration } from "../../lib/utils/date-constraints";

interface TripCardProps {
  trip: DbTrip;
  isExpanded?: boolean;
  onDelete: (tripId: string) => void;
  setExpandedTripId?: (id: string[]) => void;
  handleDeleteTrip: (id: string) => void;
  expandedTripId?: string[];
  onPress?: (trip: DbTrip) => void;
  onToggleFavorite?: (tripId: string) => void;
  isFavorite?: boolean;
  onClose?: () => void;
  onExampleStatusChange?: () => void;
}

export const TripCard = ({
  trip,
  handleDeleteTrip,
  onPress,
  onToggleFavorite,
  onClose,
  onExampleStatusChange,
}: TripCardProps) => {
  const isGenerating = trip.daysProgress.some((day) => day.is_generating);
  const generatedDays = trip?.itinerary?.length || 0;

  // Recalculate total days to ensure accuracy (don't rely on cached database value)
  const totalDays = trip?.tripDetails?.startDate && trip?.tripDetails?.endDate
    ? calculateTripDuration(trip.tripDetails.startDate, trip.tripDetails.endDate)
    : trip?.tripDetails?.totalDays || 0;

  const progress = totalDays > 0 ? Math.round((generatedDays / totalDays) * 100) : 0;
  const isFavorite = trip.isFavorite;

  // Format dates for better readability
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    });
  };

  // Get trip duration in a readable format
  const getTripDuration = () => {
    if (totalDays === 1) return "1 day";
    return `${totalDays} days`;
  };

  // Get budget display with icon
  const getBudgetDisplay = () => {
    const budget = trip?.tripDetails?.budget;
    switch (budget) {
      case 'budget':
        return { icon: 'wallet-outline' as const, text: 'Budget', color: '#4CAF50' };
      case 'luxury':
        return { icon: 'diamond-outline' as const, text: 'Luxury', color: '#9C27B0' };
      default:
        return { icon: 'card-outline' as const, text: 'Standard', color: '#2196F3' };
    }
  };

  const budgetInfo = getBudgetDisplay();
  return (
    <View style={styles.tripCard}>
      <TouchableOpacity
        style={styles.cardTouchable}
        onPress={() => {
          if (onPress) {
            onPress(trip);
          } else {
            router.push({
              pathname: "/itinerary",
              params: { tripId: trip._id },
            });
          }
        }}
      >
        {/* Header with trip name and destination */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.tripName} numberOfLines={2}>
              {trip?.name}
            </Text>
            <View style={styles.destinationRow}>
              <Text style={styles.destination} numberOfLines={1}>
                {trip?.tripDetails?.destination}
              </Text>
              {trip?.isExample && (
                <View style={styles.exampleBadge}>
                  <Ionicons name="star" size={12} color="#FFD700" />
                  <Text style={styles.exampleText}>Example</Text>
                </View>
              )}
            </View>
          </View>
          <View style={styles.statusIndicator}>
            {isGenerating ? (
              <View style={styles.generatingStatus}>
                <View style={styles.pulsingDot} />
              </View>
            ) : progress >= 100 ? (
              <View style={styles.readyStatus}>
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
              </View>
            ) : (
              <View style={styles.progressStatus}>
                <Text style={styles.progressText}>{progress}%</Text>
              </View>
            )}
          </View>
        </View>

        {/* Trip route with transport modes */}
        <View style={styles.routeSection}>
          <View style={styles.routeContainer}>
            <View style={styles.cityContainer}>
              <Ionicons
                name={
                  trip?.tripDetails?.arrivalMode === "Air"
                    ? "airplane-outline"
                    : trip?.tripDetails?.arrivalMode === "Land"
                      ? "car-outline"
                      : "boat-outline"
                }
                size={14}
                color="#666"
              />
              <Text style={styles.cityText} numberOfLines={1}>
                {trip?.tripDetails?.arrivalCity}
              </Text>
            </View>

            <View style={styles.routeLine}>
              <View style={styles.routeDot} />
              <View style={styles.routeDash} />
              <View style={styles.routeDot} />
            </View>

            <View style={styles.cityContainer}>
              <Ionicons
                name={
                  trip?.tripDetails?.departureMode === "Air"
                    ? "airplane-outline"
                    : trip?.tripDetails?.departureMode === "Land"
                      ? "car-outline"
                      : "boat-outline"
                }
                size={14}
                color="#666"
              />
              <Text style={styles.cityText} numberOfLines={1}>
                {trip?.tripDetails?.departureCity}
              </Text>
            </View>
          </View>
        </View>

        {/* Trip details */}
        <View style={styles.content}>
          {/* Date range */}
          <View style={styles.dateSection}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.dateText}>
              {formatDate(trip?.tripDetails?.startDate)} - {formatDate(trip?.tripDetails?.endDate)}
            </Text>
            <View style={styles.durationBadge}>
              <Text style={styles.durationText}>{getTripDuration()}</Text>
            </View>
          </View>

          {/* Trip stats in a grid */}
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Ionicons name="people-outline" size={18} color={budgetInfo.color} />
              <Text style={styles.statValue}>
                {(trip?.tripDetails?.people.adults || 0) + (trip?.tripDetails?.people.children || 0)}
              </Text>
              <Text style={styles.statLabel}>Travelers</Text>
            </View>

            <View style={styles.statCard}>
              <Ionicons name={budgetInfo.icon} size={18} color={budgetInfo.color} />
              <Text style={styles.statValue}>{budgetInfo.text}</Text>
              <Text style={styles.statLabel}>Budget</Text>
            </View>

            <View style={styles.statCard}>
              <Ionicons name="speedometer-outline" size={18} color={budgetInfo.color} />
              <Text style={styles.statValue}>{trip?.tripDetails?.intensity}/10</Text>
              <Text style={styles.statLabel}>Intensity</Text>
            </View>
          </View>

          {/* Progress section */}
          {isGenerating && (
            <View style={styles.progressSection}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${progress}%` }]} />
              </View>
              <Text style={styles.progressLabel}>
                Generating day {generatedDays + 1} of {totalDays}...
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      <TripActions
        trip={trip}
        onDelete={handleDeleteTrip}
        onToggleFavorite={onToggleFavorite}
        isFavorite={isFavorite}
        onClose={onClose}
        onExampleStatusChange={onExampleStatusChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  tripCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e8e8e8",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    overflow: "hidden",
  },
  cardTouchable: {
    flex: 1,
  },
  // Header styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f5f5f5",
  },
  headerContent: {
    flex: 1,
    marginRight: 12,
  },
  tripName: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 4,
    lineHeight: 24,
  },
  destination: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
    flex: 1,
  },
  statusIndicator: {
    alignItems: "center",
    justifyContent: "center",
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#f8f9fa",
  },
  errorStatus: {
    backgroundColor: "#FFF3E0",
    borderRadius: 16,
    padding: 8,
  },
  generatingStatus: {
    backgroundColor: "#E3F2FD",
    borderRadius: 16,
    padding: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  readyStatus: {
    backgroundColor: "#E8F5E8",
    borderRadius: 16,
    padding: 8,
  },
  progressStatus: {
    backgroundColor: "#E3F2FD",
    borderRadius: 16,
    padding: 4,
    minWidth: 32,
    alignItems: "center",
  },
  progressText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#2196F3",
  },
  pulsingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#2196F3",
    opacity: 0.8,
  },
  // Route section styles
  routeSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fafafa",
  },
  routeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  cityContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    flex: 1,
  },
  cityText: {
    fontSize: 13,
    color: "#666",
    fontWeight: "500",
    flex: 1,
  },
  routeLine: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    flex: 0.5,
  },
  routeDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#ddd",
  },
  routeDash: {
    flex: 1,
    height: 1,
    backgroundColor: "#ddd",
    marginHorizontal: 4,
  },
  // Content styles
  content: {
    padding: 20,
  },
  dateSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    gap: 8,
  },
  dateText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
    flex: 1,
  },
  durationBadge: {
    backgroundColor: "#E3F2FD",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  durationText: {
    fontSize: 12,
    color: "#2196F3",
    fontWeight: "600",
  },
  // Stats grid styles
  statsGrid: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    gap: 6,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1a1a1a",
  },
  statLabel: {
    fontSize: 11,
    color: "#666",
    fontWeight: "500",
    textAlign: "center",
  },
  // Progress styles
  progressSection: {
    marginTop: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#E3F2FD",
    borderRadius: 2,
    overflow: "hidden",
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#2196F3",
    borderRadius: 2,
  },
  progressLabel: {
    fontSize: 12,
    color: "#2196F3",
    fontWeight: "500",
    textAlign: "center",
  },
  // Status message styles
  statusMessage: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF3E0",
    padding: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  statusMessageText: {
    fontSize: 12,
    color: "#FF9800",
    fontWeight: "500",
    flex: 1,
  },
  // Example badge styles
  destinationRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 4,
  },
  exampleBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF8E1",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: "#FFD700",
    marginLeft: 8,
    flexShrink: 0,
  },
  exampleText: {
    fontSize: 10,
    color: "#F57C00",
    fontWeight: "600",
  },
});
