import React, { useMemo } from "react";
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
  Text,
  View,
  StyleSheet,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
  Easing,
} from "react-native-reanimated";
import ScreenHeader from "../common/ScreenHeader";
import { TripService } from "../../lib/services/trip.service";
import { ViewToggle } from "../home/<USER>";

interface TripViewHeaderProps {
  tripName: string;
  tripDestination: string;
  viewMode: "map" | "list";
  setViewMode: (mode: "map" | "list") => void;
  showMoreOptions: boolean;
  setShowMoreOptions: (show: boolean) => void;
  tripId: string;
  onBackPress?: () => void;
}

const TripViewHeader: React.FC<TripViewHeaderProps> = ({
  tripName,
  tripDestination,
  viewMode,
  setViewMode,
  showMoreOptions,
  setShowMoreOptions,
  tripId,
  onBackPress,
}) => {
  // Create a ref for the header container to help with dropdown positioning
  const headerRef = React.useRef<View>(null);
  const [headerHeight, setHeaderHeight] = React.useState(60);

  // Measure header height for proper dropdown positioning
  React.useEffect(() => {
    if (headerRef.current) {
      headerRef.current.measure((_x, _y, _width, height) => {
        if (height > 0) {
          setHeaderHeight(height);
        }
      });
    }
  }, []);

  return (
    <View ref={headerRef} style={{ zIndex: 1000 }}>
      <ScreenHeader
        title={tripName}
        subtitle={tripDestination}
        showBackButton
        onBackPress={onBackPress}
        rightComponent={
          <HeaderControls
            viewMode={viewMode}
            setViewMode={setViewMode}
            setShowMoreOptions={setShowMoreOptions}
            showMoreOptions={showMoreOptions}
          />
        }
      />

      {showMoreOptions && (
        <>
          <TouchableWithoutFeedback onPress={() => setShowMoreOptions(false)}>
            <View style={styles.overlay} />
          </TouchableWithoutFeedback>
          <MoreOptionsDropdown
            tripId={tripId}
            setShowMoreOptions={setShowMoreOptions}
            headerHeight={headerHeight}
          />
        </>
      )}
    </View>
  );
};

interface HeaderControlsProps {
  viewMode: "map" | "list";
  setViewMode: (mode: "map" | "list") => void;
  showMoreOptions: boolean;
  setShowMoreOptions: (show: boolean) => void;
}

const HeaderControls: React.FC<HeaderControlsProps> = ({
  viewMode,
  setViewMode,
  showMoreOptions,
  setShowMoreOptions,
}) => {
  // Initialize the toggle animation based on the current view mode
  const toggleAnimation = useSharedValue(viewMode === "list" ? 1 : 0);
  const handlePress = () => {
    const newMode = viewMode === "map" ? "list" : "map";
    setViewMode(newMode);
    toggleAnimation.value = newMode === "map" ? 0 : 1;
  };

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        gap: 16,
      }}
    >
      <ViewToggle
        viewMode={viewMode}
        toggleAnimation={toggleAnimation}
        onPress={handlePress}
      />
      <TouchableOpacity
        onPress={() => setShowMoreOptions(!showMoreOptions)}
        style={{
          padding: 8,
          borderRadius: 20,
          backgroundColor: showMoreOptions
            ? "rgba(33, 150, 243, 0.1)"
            : "transparent",
        }}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons name="ellipsis-vertical" size={22} color="#2196F3" />
      </TouchableOpacity>
    </View>
  );
};

interface MoreOptionsDropdownProps {
  tripId: string;
  setShowMoreOptions: (show: boolean) => void;
  headerHeight: number;
}

interface MenuItem {
  name: string;
  icon: string;
  path: string | null;
}

const tripService = TripService.getInstance();

const MoreOptionsDropdown: React.FC<MoreOptionsDropdownProps> = ({
  tripId,
  setShowMoreOptions,
  headerHeight,
}) => {
  // Animation values
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.95);

  // Animate in on mount - simplified animation to reduce glitches
  React.useEffect(() => {
    // Use a single animation for smoother performance
    opacity.value = withTiming(1, {
      duration: 150,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    scale.value = withTiming(1, {
      duration: 200,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, []);

  // Animated styles - simplified to reduce potential for glitches
  const animatedStyles = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ scale: scale.value }],
    };
  });

  const menuItems: MenuItem[] = useMemo(() => {
    return [
      { name: "Costs", icon: "cash-outline", path: "/costs" },
      { name: "Share", icon: "share-outline", path: "/share" },
      { name: "Delete", icon: "trash-outline", path: null },
    ];
  }, []);

  const handleMenuItemPress = (item: MenuItem) => () => {
    setShowMoreOptions(false);
    if (item.path) {
      router.push({ pathname: item.path, params: { tripId } });
      return;
    }
    if (item.name === "Delete") {
      Alert.alert("Delete Trip", "Are you sure you want to delete this trip?", [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const result = await tripService.archiveTrip(tripId);

              if (result.success) {
                // Show success alert instead of notification
                Alert.alert("Success", "Trip deleted successfully", [
                  {
                    text: "OK",
                    onPress: () => router.replace("/home"),
                  },
                ]);
              } else {
                // Show error alert
                Alert.alert(
                  "Error Deleting Trip",
                  result.message || "Failed to delete trip",
                  [{ text: "OK" }],
                );
              }
            } catch (error) {
              // Handle unexpected errors
              const errorMessage =
                error instanceof Error
                  ? error.message
                  : "An unexpected error occurred";
              Alert.alert("Error Deleting Trip", errorMessage, [
                { text: "OK" },
              ]);
            }
          },
        },
      ]);
    }
  };

  // Create dynamic styles based on header height
  const dropdownStyle = {
    ...styles.moreOptionsDropdown,
    top: headerHeight - 10, // Position just below the header
  };

  return (
    <Animated.View style={[dropdownStyle, animatedStyles]}>
      <View style={styles.dropdownArrow} />
      <View style={styles.dropdownHeader}>
        <Text style={styles.dropdownHeaderText}>Trip Options</Text>
      </View>
      {menuItems.map((item) => (
        <TouchableOpacity
          key={item.name}
          style={[
            styles.dropdownItem,
            item.name === "Delete" && styles.deleteItem,
          ]}
          onPress={handleMenuItemPress(item)}
        >
          <Ionicons
            name={item.icon as any}
            size={20}
            color={item.name === "Delete" ? "#FF3B30" : "#2196F3"}
          />
          <Text
            style={[
              styles.dropdownText,
              item.name === "Delete" && styles.deleteText,
            ]}
          >
            {item.name}
          </Text>
        </TouchableOpacity>
      ))}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  viewToggle: {
    width: 48,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    padding: 2,
  },
  viewToggleButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
    zIndex: 999,
  },
  moreOptionsDropdown: {
    position: "absolute",
    // top is set dynamically based on header height
    right: 4,
    backgroundColor: "#fff",
    borderRadius: 12,
    overflow: "visible",
    width: "80%", // Responsive width
    maxWidth: 250, // Maximum width
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1000,
    // Ensure the dropdown doesn't go off-screen on small devices
    marginHorizontal: 16,
  },
  dropdownArrow: {
    position: "absolute",
    top: -8,
    right: 16,
    width: 0,
    height: 0,
    backgroundColor: "transparent",
    borderStyle: "solid",
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: "#f5f5f5",
    zIndex: 1001,
  },
  dropdownHeader: {
    backgroundColor: "#f5f5f5",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  dropdownHeaderText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    gap: 12,
  },
  deleteItem: {
    borderBottomWidth: 0,
  },
  dropdownText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "500",
  },
  deleteText: {
    color: "#FF3B30",
  },
});

export default TripViewHeader;
