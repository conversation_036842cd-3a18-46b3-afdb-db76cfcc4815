import React from "react";
import { StyleSheet, View } from "react-native";
import DraggableFlatList, {
  RenderItemParams,
  DragEndParams,
} from "react-native-draggable-flatlist";
import { Activity } from "../../lib/types";
import DraggableActivity from "./DraggableActivity";
import { useSharedValue } from "react-native-reanimated";

interface DraggableActivityListProps {
  activities: Activity[];
  onDragEnd: (fromIndex: number, toIndex: number) => void;
  onEditActivity: (activity: Activity) => void;
  onRemoveActivity: (index: number) => void;
}

export default function DraggableActivityList({
  activities,
  onDragEnd,
  onEditActivity,
  onRemoveActivity,
}: DraggableActivityListProps) {
  const isDragging = useSharedValue(false);

  const renderItem = ({
    item,
    drag,
    isActive,
    getIndex,
  }: RenderItemParams<Activity>) => {
    if (isActive) {
      isDragging.value = true;
    }
    return (
      <DraggableActivity
        activity={item}
        drag={drag}
        isActive={isActive}
        onEdit={() => onEditActivity(item)}
        onRemove={() => {
          const index = getIndex();
          if (index !== undefined) {
            onRemoveActivity(index);
          }
        }}
      />
    );
  };

  const handleDragEnd = ({ from, to }: DragEndParams<Activity>) => {
    isDragging.value = false;
    onDragEnd(from, to);
  };

  return (
    <View style={styles.container}>
      <DraggableFlatList
        data={activities}
        onDragEnd={handleDragEnd}
        keyExtractor={(item, index) => `draggable-item-${index}`}
        renderItem={renderItem}
        containerStyle={styles.listContainer}
        dragHitSlop={{ top: -20, bottom: -20 }}
        activationDistance={10}
        simultaneousHandlers={[]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    // paddingHorizontal: 16,
    // paddingVertical: 8,
  },
});
