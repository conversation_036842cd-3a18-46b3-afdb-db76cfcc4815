import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Activity } from "../../lib/types";
import { formatDate } from "../../lib/utils/date";
import ActivityItem from "./ActivityItem";

interface ItineraryDayCardProps {
  day: number;
  date: string;
  activities: Activity[];
  isExpanded: boolean;
  onToggleExpand: () => void;
  tripId: string;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
}

export default function ItineraryDayCard({
  day,
  date,
  activities,
  isExpanded,
  onToggleExpand,
  tripId,
  completedActivities,
  onToggleCompletion,
}: ItineraryDayCardProps) {
  return (
    <View style={styles.dayContainer}>
      <TouchableOpacity
        style={styles.dayHeader}
        onPress={() =>
          router.navigate({
            pathname: "/daily-itinerary",
            params: { day: day + "", tripId },
          })
        }
      >
        <Text style={styles.dayTitle}>
          Day {day} - {formatDate(date)}
        </Text>
        <View style={styles.dayHeaderButtons}>
          <TouchableOpacity
            style={styles.expandButton}
            onPress={(e) => {
              e.stopPropagation();
              onToggleExpand();
            }}
          >
            <Ionicons
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={24}
              color="#333"
            />
          </TouchableOpacity>
          <Ionicons name="arrow-forward" size={24} color="#2196F3" />
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.activitiesList}>
          {activities.map((activity, index) => (
            <ActivityItem
              key={index}
              activity={activity}
              isCompleted={completedActivities.has(activity.id)}
              onToggleCompletion={onToggleCompletion}
            />
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  dayContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dayHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  dayHeaderButtons: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  expandButton: {
    padding: 4,
  },
  dayTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  activitiesList: {
    padding: 16,
  },
});
