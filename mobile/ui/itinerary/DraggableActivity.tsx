import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Activity } from "../../lib/types";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { ScaleDecorator } from "react-native-draggable-flatlist";

interface DraggableActivityProps {
  activity: Activity;
  drag: () => void;
  isActive: boolean;
  onEdit: () => void;
  onRemove: () => void;
}

export default function DraggableActivity({
  activity,
  drag,
  isActive,
  onEdit,
  onRemove,
}: DraggableActivityProps) {
  const getActivityIcon = () => {
    switch (activity.type) {
      case "activity":
        return "walk-outline";
      case "meal":
        return "restaurant-outline";
      default:
        return "ellipse-outline";
    }
  };

  const getActivityTitle = () => {
    switch (activity.type) {
      case "meal":
        return `${activity.mealType} at ${activity.name}`;
      default:
        return activity.name;
    }
  };

  return (
    <ScaleDecorator>
      <TouchableOpacity
        onLongPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          drag();
        }}
        delayLongPress={150}
        style={[styles.container, isActive && styles.dragging]}
        disabled={isActive}
      >
        <View style={styles.timeContainer}>
          <Text style={styles.time}>{activity.startTime}</Text>
          <View style={styles.dot} />
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.dragHandle}>
            <Ionicons name="menu" size={20} color="#999" />
          </View>

          <View style={styles.header}>
            <Ionicons name={getActivityIcon()} size={20} color="#666" />
            <Text style={styles.title}>{getActivityTitle()}</Text>
          </View>

          {activity.description && (
            <Text style={styles.description} numberOfLines={2}>
              {activity.description}
            </Text>
          )}

          <View style={styles.footer}>
            <Text style={styles.duration}>
              {activity.duration || "60 mins"}
            </Text>

            <View style={styles.actions}>
              <TouchableOpacity style={styles.actionButton} onPress={onRemove}>
                <Ionicons name="trash-outline" size={18} color="#FF4444" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </ScaleDecorator>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginBottom: 16,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dragging: {
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    transform: [{ scale: 1.05 }],
  },
  timeContainer: {
    width: 80,
    alignItems: "center",
    justifyContent: "flex-start",
    paddingTop: 12,
  },
  time: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#2196F3",
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
  },
  dragHandle: {
    position: "absolute",
    top: 8,
    left: 8,
    padding: 4,
    zIndex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    marginLeft: 28,
  },
  title: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginLeft: 8,
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  duration: {
    fontSize: 14,
    color: "#666",
  },
  actions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
});
