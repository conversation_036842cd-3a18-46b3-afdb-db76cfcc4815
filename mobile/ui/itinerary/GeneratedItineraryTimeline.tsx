import * as Haptics from "expo-haptics";
import React, { useMemo } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { TripService } from "../../lib/services/trip.service";
import { DbTrip } from "../../lib/types";
import ItineraryDayCard from "./ItineraryDayCard";
import { calculateTripDuration } from "../../lib/utils/date-constraints";

interface GeneratedItineraryTimelineProps {
  trip: DbTrip | null;
  expandedDays: { [key: string]: boolean };
  onToggleExpand: (day: string) => void;
  generating: boolean;
  setGenerating: (generating: boolean) => void;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
  onInsufficientBalance?: (errorMessage: string, context: any) => void;
}

const tripService = TripService.getInstance();

export default function GeneratedItineraryTimeline({
  trip,
  expandedDays,
  onToggleExpand,
  generating,
  setGenerating,
  completedActivities,
  onToggleCompletion,
  onInsufficientBalance,
}: GeneratedItineraryTimelineProps) {
  const showContinueGeneration = useMemo(() => {
    if (!trip) return false;

    // Recalculate total days to ensure accuracy
    const totalDays = trip?.tripDetails?.startDate && trip?.tripDetails?.endDate
      ? calculateTripDuration(trip.tripDetails.startDate, trip.tripDetails.endDate)
      : trip?.tripDetails?.totalDays || 0;

    return (
      trip?.status === "in_progress" &&
      trip?.currentDayProgress < totalDays &&
      trip?.itinerary?.length < totalDays
    );
  }, [trip]);

  const missingDays = useMemo(() => {
    if (!trip) return 0;

    // Recalculate total days to ensure accuracy
    const totalDays = trip?.tripDetails?.startDate && trip?.tripDetails?.endDate
      ? calculateTripDuration(trip.tripDetails.startDate, trip.tripDetails.endDate)
      : trip?.tripDetails?.totalDays || 0;

    return totalDays - trip?.itinerary?.length;
  }, [trip]);

  const continueGeneration = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (!trip) return;

    Alert.alert(
      "Continue Generation",
      "Are you sure you want to continue generation?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Continue",
          onPress: async () => {
            setGenerating(true);
            try {
              await tripService.resumeGeneration(trip._id);
            } catch (error) {
              console.error("Error resuming generation:", error);
              setGenerating(false);

              // Check if this is an insufficient balance error based on HTTP status code
              // Backend returns 402 Payment Required for InsufficientDaysException
              const isInsufficientBalanceError =
                (error as any)?.response?.status === 402 ||
                (error as any)?.status === 402;

              if (isInsufficientBalanceError && onInsufficientBalance) {
                console.info("HTTP 402 error detected during continue generation - insufficient balance");

                // Create a context object similar to socket errors
                const errorContext = {
                  isInsufficientBalance: true,
                  notificationType: "insufficient_balance",
                  payload: {
                    type: "insufficient_balance",
                    content: "Not enough days to continue generation",
                    available: 0, // Will be updated by fetchBalance call
                    needed: 1, // Default assumption
                  }
                };

                const errorMessage = error instanceof Error ? error.message : String(error);
                onInsufficientBalance(errorMessage, errorContext);
              }
            }
          },
        },
      ],
    );
  };

  return (
    <ScrollView style={styles.container}>
      {trip?.itinerary?.map(({ day, activities, date }, index) => (
        <ItineraryDayCard
          key={index}
          day={day}
          date={date}
          activities={activities}
          isExpanded={expandedDays[day]}
          onToggleExpand={() => onToggleExpand(day + "")}
          tripId={trip?._id}
          completedActivities={completedActivities}
          onToggleCompletion={onToggleCompletion}
        />
      ))}
      {showContinueGeneration && (
        <TouchableOpacity
          onPress={continueGeneration}
          disabled={generating}
          style={styles.continueGenerationButton}
        >
          {generating && (
            <View
              style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
            >
              <ActivityIndicator size="small" color="#fff" />
              <Text style={{ color: "#fff" }}>Generating itinerary...</Text>
            </View>
          )}
          {!generating && (
            <Text style={styles.continueGenerationButtonText}>
              Continue Generation ({missingDays} days left)
            </Text>
          )}
        </TouchableOpacity>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  continueGenerationButton: {
    padding: 16,
    backgroundColor: "#007bff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  continueGenerationButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
});
