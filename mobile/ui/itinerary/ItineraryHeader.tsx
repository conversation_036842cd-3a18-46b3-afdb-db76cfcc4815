import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ItineraryHeaderProps {
  tripId: string;
  showActions?: boolean;
  onClose?: () => void;
}

export default function ItineraryHeader({
  tripId,
  showActions = true,
  onClose,
}: ItineraryHeaderProps) {
  if (!showActions) return null;
  const handleClick = (path: string) => () => {
    router.navigate({
      pathname: path,
      params: { tripId },
    });
    onClose?.();
  };
  return (
    <View style={styles.header}>
      <View style={styles.headerButtons}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleClick("/customize")}
        >
          <Ionicons name="create-outline" size={24} color="#333" />
          <Text>Customize</Text>
        </TouchableOpacity>
        <Separator />
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleClick("/map")}
        >
          <Ionicons name="map-outline" size={24} color="#333" />
          <Text>Map</Text>
        </TouchableOpacity>
        <Separator />
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleClick("/costs")}
        >
          <Ionicons name="wallet-outline" size={24} color="#333" />
          <Text>Costs</Text>
        </TouchableOpacity>
        <Separator />
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleClick("/share")}
        >
          <Ionicons name="share-social-outline" size={24} color="#333" />
          <Text>Share</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const Separator = () => {
  return <View style={{ height: 1, backgroundColor: "#EEEEEE" }} />;
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: "white",
    position: "absolute",
    top: 130,
    right: 10,
    zIndex: 1000,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2196F3",
  },
  headerButtons: {
    gap: 1,
  },
  headerButton: {
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
});
