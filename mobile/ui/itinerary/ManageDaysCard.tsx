import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ManageDaysCardProps {
  totalDays: number;
  onManage: () => void;
}

export default function ManageDaysCard({
  totalDays,
  onManage,
}: ManageDaysCardProps) {
  return (
    <View style={styles.container}>
      <View style={styles.badge}>
        <Text style={styles.count}>{totalDays}</Text>
      </View>
      <View style={styles.content}>
        <Text style={styles.title}>Manage Your Days</Text>
        <Text style={styles.subtitle}>
          Generate, regenerate, or adjust your itinerary days
        </Text>
      </View>
      <TouchableOpacity style={styles.button} onPress={onManage}>
        <Ionicons name="settings-outline" size={20} color="#FFFFFF" />
        <Text style={styles.buttonText}>Manage</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    gap: 16,
    alignItems: "center",
    margin: 16,
    padding: 16,
    backgroundColor: "#E8F5E9",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    padding: 8,
    borderRadius: 12,
  },
  count: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2196F3",
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2196F3",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 13,
    color: "#666",
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2196F3",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    gap: 4,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
});
