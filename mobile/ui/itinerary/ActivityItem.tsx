import React from "react";
import { Activity } from "../../lib/types";
import ActivityCard from "../common/ActivityCard";

interface ActivityItemProps {
  activity: Activity;
  isCompleted?: boolean;
  onToggleCompletion?: (activityId: string, isCompleted: boolean) => void;
}

export default function ActivityItem({
  activity,
  isCompleted = false,
  onToggleCompletion,
}: ActivityItemProps) {
  return (
    <ActivityCard
      activity={activity}
      context="day"
      isCompleted={isCompleted}
      onToggleCompletion={onToggleCompletion}
    />
  );
}


