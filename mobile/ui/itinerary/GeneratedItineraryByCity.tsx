import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Linking,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Activity, DbTrip } from "../../lib/types";
import { TripService } from "../../lib/services/trip.service";
import ActivityCard from "../common/ActivityCard";

interface GeneratedItineraryByCityProps {
  trip: DbTrip | null;
  expandedCities: { [key: string]: boolean };
  onToggleExpand: (city: string) => void;
  completedActivities: Set<string>;
  onToggleCompletion: (activityId: string, isCompleted: boolean) => void;
}

type ActivityWithDay = Activity & { dayNumber: number; date: string };

const tripService = TripService.getInstance();

export default function GeneratedItineraryByCity({
  trip,
  expandedCities,
  onToggleExpand,
  completedActivities,
  onToggleCompletion,
}: GeneratedItineraryByCityProps) {
  const [activitiesByCity, setActivitiesByCity] = useState<
    Record<string, ActivityWithDay[]>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleBookTour = async (cityName: string) => {
    try {
      const url = `https://www.getyourguide.com/s?partner_id=EQK3T6V&cmp=share_to_earn&q=${encodeURIComponent(cityName)}`;
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error opening GetYourGuide:', error);
    }
  };

  const handleBookAccommodation = async (cityName: string, activities: ActivityWithDay[]) => {
    try {
      // Calculate check-in and check-out dates for this city
      const cityDates = activities
        .filter(activity => activity.date)
        .map(activity => activity.date)
        .sort();

      if (cityDates.length === 0) return;

      const checkInDate = cityDates[0]; // First day in city
      const checkOutDate = cityDates[cityDates.length - 1]; // Last day in city

      // Calculate checkout date (day after last day)
      const lastDate = new Date(checkOutDate);
      lastDate.setDate(lastDate.getDate() + 1);
      const formattedCheckOutDate = lastDate.toISOString().split('T')[0];

      // Get number of adults from trip details (default to 2 if not available)
      const adults = trip?.tripDetails?.people?.adults || 2;

      // Add country information to avoid confusion with cities in other countries
      const country = trip?.tripDetails?.destination || '';
      const cityWithCountry = country ? `${cityName}, ${country}` : cityName;

      const url = `https://expedia.com/Hotel-Search?destination=${encodeURIComponent(cityWithCountry)}&startDate=${checkInDate}&endDate=${formattedCheckOutDate}&rooms=1&adults=${adults}&rfrr=ZMHK6lp`;

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error opening Expedia:', error);
    }
  };

  useEffect(() => {
    async function fetchActivitiesByCity() {
      if (!trip?._id) return;

      setLoading(true);
      setError(null);

      try {
        const result = await tripService.fetchActivitiesByCity(trip._id);
        setActivitiesByCity(result);
      } catch (err) {
        console.error("Error fetching activities by city:", err);
        setError("Failed to load activities by city");
      } finally {
        setLoading(false);
      }
    }

    fetchActivitiesByCity();
  }, [trip?._id]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading activities by city...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            if (trip?._id) {
              setLoading(true);
              setError(null);
              tripService
                .fetchActivitiesByCity(trip._id)
                .then((result) => setActivitiesByCity(result))
                .catch((err) => {
                  console.error("Error fetching activities by city:", err);
                  setError("Failed to load activities by city");
                })
                .finally(() => setLoading(false));
            }
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (Object.keys(activitiesByCity).length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="location-outline" size={48} color="#999" />
        <Text style={styles.emptyText}>No activities found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {Object.entries(activitiesByCity).map(([city, activities]) => (
        <View key={city} style={styles.cityCard}>
          <View style={styles.cityHeader}>
            <TouchableOpacity
              style={styles.cityHeaderLeft}
              onPress={() => onToggleExpand(city)}
            >
              <Text style={styles.cityName}>{city.toUpperCase()}</Text>
              <Text style={styles.activityCount}>
                {activities.length}{" "}
                {activities.length === 1 ? "activity" : "activities"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.mapButton}
              onPress={() => {
                if (trip?._id) {
                  router.push({
                    pathname: "/city-map",
                    params: {
                      tripId: trip._id,
                      cityName: city,
                      totalActivities: activities.length.toString()
                    },
                  });
                }
              }}
            >
              <Ionicons name="map-outline" size={20} color="#007AFF" />
            </TouchableOpacity>
          </View>

          {/* Action buttons row */}
          <View style={styles.cityActions}>
            <TouchableOpacity
              style={styles.bookTourButton}
              onPress={() => handleBookTour(city)}
            >
              <Ionicons name="compass-outline" size={16} color="#fff" />
              <Text style={styles.bookTourText}>Book a tour</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.bookAccommodationButton}
              onPress={() => handleBookAccommodation(city, activities)}
            >
              <Ionicons name="bed-outline" size={16} color="#fff" />
              <Text style={styles.bookAccommodationText}>Book hotel</Text>
            </TouchableOpacity>
          </View>

          {/* Expand/Collapse button */}
          <View style={styles.expandSection}>
            <TouchableOpacity
              style={styles.expandButton}
              onPress={() => onToggleExpand(city)}
            >
              <Text style={styles.expandButtonText}>
                {expandedCities[city] ? 'Hide activities' : 'View activities'}
              </Text>
              <Ionicons
                name={expandedCities[city] ? "chevron-up" : "chevron-down"}
                size={16}
                color="#007AFF"
              />
            </TouchableOpacity>
          </View>
          {expandedCities[city] && (
            <View style={styles.activitiesList}>
              {activities.map((activity, index) => {
                const isCompleted = completedActivities.has(activity.id);

                // Ensure we have a valid activity object
                if (!activity || !activity.id) {
                  console.error('Invalid activity object:', activity);
                  return null;
                }

                return (
                  <ActivityCard
                    key={`${activity.id}-${index}`}
                    activity={activity}
                    context="city"
                    isCompleted={isCompleted}
                    onToggleCompletion={onToggleCompletion}
                    dayNumber={activity.dayNumber}
                    date={activity.date || ""}
                  />
                );
              }).filter(Boolean)}
            </View>
          )}
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cityCard: {
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 12,
    overflow: "hidden",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cityHeader: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: "#f8f9fa",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cityHeaderLeft: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginRight: 12,
  },
  cityActions: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingBottom: 8,
    gap: 12,
  },
  bookTourButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: "#FF6B35",
    borderRadius: 8,
    gap: 6,
  },
  bookTourText: {
    color: "#fff",
    fontSize: 13,
    fontWeight: "600",
  },
  bookAccommodationButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: "#007AFF",
    borderRadius: 8,
    gap: 6,
  },
  bookAccommodationText: {
    color: "#fff",
    fontSize: 13,
    fontWeight: "600",
  },
  expandSection: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  expandButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e9ecef",
    gap: 6,
  },
  expandButtonText: {
    color: "#007AFF",
    fontSize: 14,
    fontWeight: "500",
  },
  mapButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#fff",
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cityName: {
    fontSize: 18,
    fontWeight: "600",
  },
  activityCount: {
    fontSize: 14,
    color: "#666",
  },
  activitiesList: {
    padding: 16,
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  // Error state styles
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    marginBottom: 20,
    fontSize: 16,
    color: "#FF6B6B",
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#2196F3",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: "#FFF",
    fontSize: 16,
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    color: "#999",
  },
});
