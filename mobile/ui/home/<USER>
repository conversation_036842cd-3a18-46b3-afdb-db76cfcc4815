import React, { useEffect, useState } from "react";
import { StyleSheet, Text } from "react-native";
import { PointAnnotation } from "../map/MapUtils";
import Animated from "react-native-reanimated";
import { DbTrip } from "../../lib/types";
import { getCountryCode } from "../../lib/utils/country-codes";
import { MapRegionService } from "../../lib/services/map-region.service";

interface CountryMarkerProps {
  country: string;
  trips: DbTrip[];
  isZoomedOut: boolean;
  mapRegion: {
    latitudeDelta: number;
  };
  onPress: () => void;
}

export const CountryMarker: React.FC<CountryMarkerProps> = ({
  country,
  trips,
  isZoomedOut,
  mapRegion,
  onPress,
}) => {
  // Use the map region service to get country coordinates
  const [coordinates, setCoordinates] = useState<[number, number] | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCountryCoordinates = async () => {
      if (!country) return;

      setLoading(true);
      try {
        const mapRegionService = MapRegionService.getInstance();
        const region = await mapRegionService.getCountryMapRegion(country);

        if (region) {
          setCoordinates([region.longitude, region.latitude]); // Mapbox uses [lng, lat] format
        } else {
          console.error(`No coordinates found for country: ${country}`);
          setCoordinates(null);
        }
      } catch (error) {
        console.error(
          `Error fetching country coordinates for ${country}:`,
          error,
        );
        setCoordinates(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCountryCoordinates();
  }, [country]);

  if (loading || !coordinates) return null;

  return (
    <PointAnnotation
      id={`country-${country}`}
      coordinate={coordinates}
      onSelected={onPress}
      anchor={{ x: 0.5, y: 0.5 }}
    >
      <Animated.View
        style={[
          {
            transform: [
              {
                scale: isZoomedOut
                  ? Math.max(1, 1.2 - mapRegion.latitudeDelta / 180)
                  : Math.min(1.5, 1.2 + 30 / mapRegion.latitudeDelta),
              },
            ],
            padding: isZoomedOut ? 8 : 12,
            borderRadius: isZoomedOut ? 12 : 16,
            backgroundColor:
              trips.length > 5
                ? "#FF2D55"
                : trips.length > 2
                  ? "#007AFF"
                  : "#32C759",
          },
        ]}
      >
        <Animated.Text
          style={[
            styles.title,
            {
              fontSize: isZoomedOut
                ? Math.max(12, 16 - mapRegion.latitudeDelta / 20)
                : Math.min(18, 14 + 30 / mapRegion.latitudeDelta),
              opacity: isZoomedOut
                ? Math.max(0.9, 1 - mapRegion.latitudeDelta / 200)
                : 1,
            },
          ]}
        >
          {isZoomedOut ? getCountryCode(country) : country}
        </Animated.Text>
        <Animated.Text
          style={[
            styles.count,
            {
              fontSize: isZoomedOut
                ? Math.max(10, 12 - mapRegion.latitudeDelta / 30)
                : Math.min(14, 12 + 30 / mapRegion.latitudeDelta),
              opacity: Math.max(0.9, 1 - mapRegion.latitudeDelta / 100),
            },
          ]}
        >

          {trips.length} {trips.length === 1 ? "trip" : "trips"}
        </Animated.Text>
      </Animated.View>
    </PointAnnotation>
  );
};

const styles = StyleSheet.create({
  container: {
    // padding: 8,
    // iOS-specific fixes
  },
  title: {
    color: "#fff",
    fontWeight: "800",
    fontSize: 14,
    textAlign: "center",
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  count: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
    textAlign: "center",
    opacity: 0.95,
    marginTop: 2,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
