import React from "react";
import { StyleSheet, View } from "react-native";
import { FilterSelector } from "./FilterSelector";

interface FilterBarProps {
  selectedDay: number;
  selectedCity: string;
  completionFilter: "all" | "completed" | "pending";
  onDayPress: () => void;
  onCityPress: () => void;
  onCompletionPress: () => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  selectedDay,
  selectedCity,
  completionFilter,
  onDayPress,
  onCityPress,
  onCompletionPress,
}) => {
  return (
    <View style={styles.container}>
      <FilterSelector
        label={selectedDay === 0 ? "All Days" : `Day ${selectedDay}`}
        onPress={onDayPress}
      />

      <FilterSelector
        label={selectedCity === "all" ? "All Cities" : selectedCity}
        onPress={onCityPress}
      />

      <FilterSelector
        label={
          completionFilter === "all"
            ? "All Activities"
            : completionFilter === "completed"
              ? "Completed"
              : "Pending"
        }
        onPress={onCompletionPress}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
});
