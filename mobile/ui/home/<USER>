import React, { useRef } from "react";
import { StyleSheet } from "react-native";
import { MapView, Camera, Region } from "../map/MapUtils";
import { router } from "expo-router";
import { DbTrip } from "../../lib/types";
import { CountryMarker } from "./CountryMarker";

interface MapViewComponentProps {
  mapRegion: Region;
  setMapRegion: (region: Region) => void;
  isZoomedOut: boolean;
  setIsZoomedOut: (isZoomedOut: boolean) => void;
  tripsByCountry: Record<string, DbTrip[]>;
}

export const MapViewComponent: React.FC<MapViewComponentProps> = ({
  mapRegion,
  setMapRegion,
  isZoomedOut,
  setIsZoomedOut,
  tripsByCountry,
}) => {
  const cameraRef = useRef<any>(null);

  return (
    <MapView
      style={styles.map}
    >
      <Camera
        ref={cameraRef}
        centerCoordinate={[mapRegion.longitude, mapRegion.latitude]}
        zoomLevel={Math.max(0, Math.min(22, Math.log2(360 / Math.max(0.001, Math.min(mapRegion.latitudeDelta, 180)))))}
        onCameraChanged={(state: any) => {
          if (state.properties.center) {
            const [longitude, latitude] = state.properties.center;
            const zoom = state.properties.zoom || 10;
            const latitudeDelta = 360 / Math.pow(2, zoom);
            const longitudeDelta = latitudeDelta * Math.cos(latitude * Math.PI / 180);

            const region = {
              latitude,
              longitude,
              latitudeDelta,
              longitudeDelta,
            };

            setMapRegion(region);
            setIsZoomedOut(region.latitudeDelta > 30);
          }
        }}
      />
      {Object.entries(tripsByCountry).map(([country, trips]) => (
        <CountryMarker
          key={country}
          country={country}
          trips={trips}
          isZoomedOut={isZoomedOut}
          mapRegion={mapRegion}
          onPress={() => {
            router.push({
              pathname: "/country-trips",
              params: {
                country
              },
            });
          }}
        />
      ))}
    </MapView>
  );
};

const styles = StyleSheet.create({
  map: {
    flex: 1,
    backgroundColor: "#fff",
  },
});
