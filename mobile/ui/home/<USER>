import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface EmptyStateProps {
  hasTrips?: boolean;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ hasTrips = false }) => {
  return (
    <View style={styles.container}>
      <Ionicons name="search" size={48} color="#ccc" />
      <Text style={styles.title}>
        {hasTrips
          ? "No trips match your search or filters"
          : "No saved trips yet"}
      </Text>
      <Text style={styles.subtitle}>
        {hasTrips
          ? "Try adjusting your search or filters"
          : "Your saved trips will appear here"}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginTop: 16,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
});
