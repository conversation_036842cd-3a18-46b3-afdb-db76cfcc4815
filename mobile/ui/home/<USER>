import { Ionicons } from "@expo/vector-icons";
import React, { forwardRef } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";

export interface ViewToggleProps {
  viewMode: "map" | "list";
  toggleAnimation: any;
  onPress: () => void;
}

export const ViewToggle = forwardRef<View, ViewToggleProps>(
  ({ viewMode, toggleAnimation, onPress }, ref) => {
    // Ensure the toggle position matches the view mode
    const toggleIconStyle = useAnimatedStyle(() => ({
      transform: [
        {
          translateX: withTiming(toggleAnimation.value * 24),
        },
      ],
    }));

    // Background color changes based on the toggle position
    const toggleContainerStyle = useAnimatedStyle(() => ({
      backgroundColor: withTiming(
        `rgba(33, 150, 243, ${1 - toggleAnimation.value})`,
      ),
    }));

    return (
      <View ref={ref}>
        <TouchableOpacity
          style={[
            styles.container,
            {
              backgroundColor: viewMode === "map" ? "#2196F3" : "#f0f0f0",
            },
            toggleContainerStyle,
          ]}
          onPress={onPress}
          activeOpacity={1}
        >
          <Animated.View style={[styles.button, toggleIconStyle]}>
            <Ionicons
              name={viewMode === "map" ? "map-outline" : "list-outline"}
              size={16}
              color={viewMode === "map" ? "#2196F3" : "#666"}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: 48,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    padding: 2,
  },
  button: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
});
