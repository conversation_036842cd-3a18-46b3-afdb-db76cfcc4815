import React from "react";
import { ActivityIndicator, StyleSheet, Text, View } from "react-native";

export const LoadingState: React.FC = () => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#2196F3" />
      <Text style={styles.text}>Loading trips...</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  text: {
    fontSize: 16,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
});
