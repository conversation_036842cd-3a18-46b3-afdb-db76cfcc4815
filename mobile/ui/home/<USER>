import React, { memo } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { TripFilters } from "../../lib/hooks/useFilteredTrips";

interface FilterChipsProps {
  filters: TripFilters;
  toggleFilter: (filterName: keyof TripFilters) => void;
  clearFilters: () => void;
}

/**
 * Filter chips component for home screen
 * Allows users to filter trips by favorite, generating, and ready status
 */
export const FilterChips = memo<FilterChipsProps>(
  ({ filters, toggleFilter, clearFilters }) => {
    // Check if any filter is active
    const hasActiveFilters = Object.entries(filters).some(
      ([key, value]) =>
        key !== "searchQuery" && key !== "includeArchived" && Boolean(value),
    );

    return (
      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            Boolean(filters.favorite) && styles.activeFilterChip,
          ]}
          onPress={() => toggleFilter("favorite")}
        >
          <Ionicons
            name="heart"
            size={16}
            color={Boolean(filters.favorite) ? "white" : "#666"}
          />
          <Text
            style={[
              styles.filterChipText,
              Boolean(filters.favorite) && styles.activeFilterChipText,
            ]}
          >
            Favorites
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterChip,
            Boolean(filters.generating) && styles.activeFilterChip,
          ]}
          onPress={() => toggleFilter("generating")}
        >
          <Ionicons
            name="refresh-circle"
            size={16}
            color={Boolean(filters.generating) ? "white" : "#666"}
          />
          <Text
            style={[
              styles.filterChipText,
              Boolean(filters.generating) && styles.activeFilterChipText,
            ]}
          >
            Generating
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterChip,
            Boolean(filters.ready) && styles.activeFilterChip,
          ]}
          onPress={() => toggleFilter("ready")}
        >
          <Ionicons
            name="checkmark-circle"
            size={16}
            color={Boolean(filters.ready) ? "white" : "#666"}
          />
          <Text
            style={[
              styles.filterChipText,
              Boolean(filters.ready) && styles.activeFilterChipText,
            ]}
          >
            Ready
          </Text>
        </TouchableOpacity>

        {hasActiveFilters && (
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={clearFilters}
          >
            <Text style={styles.clearFiltersText}>Clear</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if filters change or if functions change
    return (
      prevProps.filters.favorite === nextProps.filters.favorite &&
      prevProps.filters.generating === nextProps.filters.generating &&
      prevProps.filters.ready === nextProps.filters.ready &&
      prevProps.toggleFilter === nextProps.toggleFilter &&
      prevProps.clearFilters === nextProps.clearFilters
    );
  },
);

const styles = StyleSheet.create({
  filtersContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingBottom: 12,
    flexWrap: "wrap",
    gap: 8,
    backgroundColor: "#f5f5f5",
  },
  filterChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  activeFilterChip: {
    backgroundColor: "#2196F3",
    borderColor: "#2196F3",
  },
  filterChipText: {
    marginLeft: 4,
    fontSize: 14,
    color: "#666",
  },
  activeFilterChipText: {
    color: "white",
  },
  clearFiltersButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    justifyContent: "center",
  },
  clearFiltersText: {
    color: "#F44336",
    fontSize: 14,
    fontWeight: "500",
  },
});
