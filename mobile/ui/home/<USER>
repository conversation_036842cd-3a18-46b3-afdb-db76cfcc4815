import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Animated } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useDaysBalance } from "../../lib/hooks/use-days-balance";
import { router } from "expo-router";

interface RemainingDaysTrackerProps {
  style?: any;
}

export const RemainingDaysTracker: React.FC<RemainingDaysTrackerProps> = ({ style }) => {
  const { balance, isLoading, getTotalAvailableDays } = useDaysBalance();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  const totalAvailableDays = getTotalAvailableDays();

  // Animate when days are low
  useEffect(() => {
    if (totalAvailableDays <= 3 && totalAvailableDays > 0) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [totalAvailableDays, pulseAnim]);

  const handlePress = () => {
    // Add haptic feedback
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Navigate to credits dashboard
    router.push("/credits-dashboard");
  };

  if (isLoading) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <View style={styles.loadingDot} />
          <View style={styles.loadingDot} />
          <View style={styles.loadingDot} />
        </View>
      </View>
    );
  }

  // Dynamic colors based on remaining days
  const getStatusColor = () => {
    if (totalAvailableDays === 0) return "#FF4444"; // Critical - bright red
    if (totalAvailableDays <= 3) return "#FF6B6B"; // Low - standard red
    if (totalAvailableDays <= 10) return "#FF9800"; // Warning - orange
    return "#34C759"; // Good - green
  };

  const getStatusIcon = () => {
    if (totalAvailableDays === 0) return "alert-circle";
    if (totalAvailableDays <= 3) return "warning";
    return "calendar";
  };

  const statusColor = getStatusColor();
  const statusIcon = getStatusIcon();

  return (
    <Animated.View style={[styles.container, style, { transform: [{ scale: scaleAnim }] }]}>
      <TouchableOpacity
        style={styles.tracker}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Animated.View
          style={[
            styles.iconContainer,
            { backgroundColor: `${statusColor}15`, transform: [{ scale: pulseAnim }] }
          ]}
        >
          <Ionicons
            name={statusIcon}
            size={16}
            color={statusColor}
          />
        </Animated.View>
        <Text style={[styles.daysText, { color: statusColor }]}>
          {totalAvailableDays}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#C0C0C0",
    opacity: 0.6,
  },
  tracker: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.08)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  daysText: {
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
  },
});
