import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { DbTrip } from "../../lib/types";

interface TripHeaderProps {
  selectedTrip: DbTrip;
  onBack: () => void;
}

export const TripHeader: React.FC<TripHeaderProps> = ({
  selectedTrip,
  onBack,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Ionicons name="arrow-back" size={24} color="#2196F3" />
        <Text style={styles.backButtonText}>All Trips</Text>
      </TouchableOpacity>
      <Text style={styles.title} numberOfLines={1}>
        {selectedTrip.tripDetails.destination}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  backButtonText: {
    color: "#2196F3",
    fontSize: 16,
    fontWeight: "500",
  },
  title: {
    flex: 1,
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 16,
    color: "#000",
  },
});
