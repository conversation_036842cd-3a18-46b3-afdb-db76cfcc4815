import { Ionicons } from "@expo/vector-icons";
import React, { useMemo, useState } from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { DbTrip } from "../../lib/types";
import { countryFlag } from "../../lib/utils/country-codes";
import { TripCardWithImage } from "../trips/TripCardWithImage";
import { useAuth } from "../../lib/hooks/use-auth";

interface CountrySectionProps {
  country: string;
  trips: DbTrip[];
  onTripPress: (trip: DbTrip) => void;
  onTripDelete: (tripId: string) => void;
  onToggleFavorite: (tripId: string) => void;
}

export const CountrySection: React.FC<CountrySectionProps> = ({
  country,
  trips,
  onTripPress,
  onTripDelete,
  onToggleFavorite,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { auth } = useAuth();

  const flagUri = useMemo(() => countryFlag(country), [country]);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.headerContainer}
        onPress={() => setIsCollapsed(!isCollapsed)}
      >
        <View style={styles.countryBadge}>
          <Image
            source={{
              uri: flagUri,
              cache: "reload",
              headers: {
                Authorization: `Bearer ${auth?.token}`,
              },
            }}
            style={styles.countryIcon}
          />
          <Text style={styles.countryName}>{country}</Text>
        </View>
        <View style={styles.headerRight}>
          <View style={styles.tripCountBadge}>
            <Text style={styles.tripCount}>{trips.length}</Text>
          </View>
          <Ionicons
            name={isCollapsed ? "chevron-down" : "chevron-up"}
            size={24}
            color="#666"
          />
        </View>
      </TouchableOpacity>

      {!isCollapsed && (
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
        >
          {trips.map((trip) => (
            <TripCardWithImage
              key={trip._id}
              trip={trip}
              onDelete={onTripDelete}
              handleDeleteTrip={onTripDelete}
              onPress={onTripPress}
              onToggleFavorite={onToggleFavorite}
              isFavorite={trip.isFavorite}
            />
          ))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 16,
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginHorizontal: 8,
    overflow: "hidden",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingRight: 16,
    paddingVertical: 12,
    backgroundColor: "#f8f8f8",
    borderBottomWidth: 1,
    borderBottomColor: "#eaeaea",
  },
  countryBadge: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 16,
  },
  countryIcon: {
    marginRight: 6,
    width: 36,
    height: 36,
    aspectRatio: 1,
    borderRadius: 120,
    resizeMode: "cover",
  },
  countryName: {
    fontWeight: "600",
    fontSize: 16,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  tripCountBadge: {
    backgroundColor: "#e0e0e0",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
  },
  tripCount: {
    fontSize: 14,
    fontWeight: "500",
    color: "#555",
  },
  scrollContainer: {
    paddingTop: 8,
    paddingBottom: 8,
  },
  scrollContent: {
    paddingHorizontal: 0,
  },
});
