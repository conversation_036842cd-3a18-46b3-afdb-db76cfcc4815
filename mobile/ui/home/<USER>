import React, { useEffect, memo } from "react";
import { View } from "react-native";
import { useTutorial, TutorialStep } from "../common/TutorialManager";

// Tutorial ID for the home screen
const HOME_TUTORIAL_ID = "home_screen_tutorial";

// Props for the HomeTutorial component
interface HomeTutorialProps {
  fabButtonRef: React.RefObject<View | null>;
  viewToggleRef: React.RefObject<View | null>;
  isReady: boolean; // Flag to indicate when the screen is ready to show tutorials
}

const HomeTutorial: React.FC<HomeTutorialProps> = memo(
  ({ fabButtonRef, viewToggleRef, isReady }) => {
    // Get tutorial functions from context
    const { registerTutorial, startTutorial, resetTutorial } = useTutorial();

    // // Reset the tutorial when the component mounts
    // useEffect(() => {
    //   resetTutorial(HOME_TUTORIAL_ID);
    // }, [resetTutorial]);

    // Define tutorial steps and register them only once when refs are available
    useEffect(() => {
      // Only register if both refs have current values
      if (!fabButtonRef.current || !viewToggleRef.current) return;

      // Define the tutorial steps
      const tutorialSteps: TutorialStep[] = [
        {
          id: "new_trip",
          targetRef: fabButtonRef,
          title: "Create a New Trip",
          description: "Tap this button to start planning your next adventure!",
          placement: "top-right",
          order: 0,
        },
        {
          id: "view_toggle",
          targetRef: viewToggleRef,
          title: "Switch View Mode",
          description:
            "Toggle between map and list view to see your trips in different ways.",
          placement: "bottom-right",
          order: 1,
        },
      ];

      // Register the tutorial steps
      registerTutorial(HOME_TUTORIAL_ID, tutorialSteps);

      // This effect should only run once when the refs are available
    }, [fabButtonRef, viewToggleRef, registerTutorial]);

    // Start the tutorial when the screen is ready
    useEffect(() => {
      if (!isReady) return;

      // Small delay to ensure everything is rendered
      const timer = setTimeout(() => {
        startTutorial(HOME_TUTORIAL_ID);
      }, 1000);

      return () => {
        clearTimeout(timer);
      };
    }, [isReady, startTutorial]);

    // This component doesn't render anything visible
    return null;
  },
);

export default HomeTutorial;
