import React from "react";
import { ScrollView, RefreshControl, StyleSheet } from "react-native";
import { DbTrip } from "../../lib/types";
import { CountrySection } from "./CountrySection";
import { EmptyState } from "./EmptyState";

interface TripListProps {
  tripsByCountry: { [key: string]: DbTrip[] };
  loading: boolean;
  loadTrips: () => Promise<void>;
  onTripPress: (trip: DbTrip) => void;
  onTripDelete: (tripId: string) => void;
  onToggleFavorite: (tripId: string) => void;
  allTrips: DbTrip[] | null;
}

/**
 * Trip list component for home screen
 * Shows trips grouped by country
 */
export const TripList: React.FC<TripListProps> = ({
  tripsByCountry,
  loading,
  loadTrips,
  onTripPress,
  onTripDelete,
  onToggleFavorite,
  allTrips,
}) => {
  const hasCountries = Object.keys(tripsByCountry).length > 0;

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={{
        paddingBottom: 16,
      }}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={loadTrips} />
      }
    >
      {hasCountries ? (
        Object.entries(tripsByCountry).map(([country, countryTrips]) => (
          <CountrySection
            key={country}
            country={country}
            trips={countryTrips}
            onTripPress={onTripPress}
            onTripDelete={onTripDelete}
            onToggleFavorite={onToggleFavorite}
          />
        ))
      ) : (
        <EmptyState hasTrips={Boolean(allTrips?.length)} />
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingTop: 16,
  },
});
