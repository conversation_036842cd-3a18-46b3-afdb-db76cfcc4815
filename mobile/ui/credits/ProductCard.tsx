import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Product, Subscription } from 'react-native-iap';
import { ProductConfig } from '../../lib/hooks/usePayments';

interface ProductCardProps {
  config: ProductConfig;
  storeProduct?: Product | Subscription;
  isSelected: boolean;
  isCurrentPlan?: boolean;
  isUpgrade?: boolean;
  onSelect: (id: string) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  config,
  storeProduct,
  isSelected,
  isCurrentPlan,
  isUpgrade,
  onSelect,
}) => {
  const handlePress = () => {
    if (!isCurrentPlan) {
      onSelect(config.id);
    }
  };

  const getDisplayPrice = () => {
    if (storeProduct) {
      return storeProduct.localizedPrice || `$${config.price.usd}`;
    }
    return `$${config.price.usd}`;
  };

  const getButtonText = () => {
    if (isCurrentPlan) return 'Current Plan';
    if (isUpgrade) return 'Upgrade';
    if (config.type === 'subscription') return 'Subscribe';
    return 'Purchase';
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        isSelected && styles.selectedCard,
        isCurrentPlan && styles.currentPlanCard,
      ]}
      onPress={handlePress}
      disabled={isCurrentPlan}
    >
      <View style={styles.header}>
        <Text style={styles.name}>{config.name}</Text>
        {config.popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>Popular</Text>
          </View>
        )}
      </View>
      
      <Text style={styles.description}>{config.description}</Text>
      
      <View style={styles.details}>
        {config.type === 'subscription' && config.daysPerMonth && (
          <Text style={styles.detailText}>{config.daysPerMonth} days per month</Text>
        )}
        {config.type === 'consumable' && config.days && (
          <Text style={styles.detailText}>{config.days} days</Text>
        )}
      </View>

      <View style={styles.footer}>
        <Text style={styles.price}>{getDisplayPrice()}</Text>
        <View style={[
          styles.button,
          isSelected && styles.selectedButton,
          isCurrentPlan && styles.currentPlanButton,
        ]}>
          <Text style={[
            styles.buttonText,
            isSelected && styles.selectedButtonText,
            isCurrentPlan && styles.currentPlanButtonText,
          ]}>
            {getButtonText()}
          </Text>
          {isSelected && !isCurrentPlan && (
            <Ionicons name="checkmark" size={16} color="#fff" style={styles.checkIcon} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedCard: {
    borderColor: '#2196F3',
    borderWidth: 2,
  },
  currentPlanCard: {
    backgroundColor: '#F5F5F5',
    borderColor: '#9E9E9E',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  popularBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  details: {
    marginBottom: 16,
  },
  detailText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  button: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedButton: {
    backgroundColor: '#4CAF50',
  },
  currentPlanButton: {
    backgroundColor: '#9E9E9E',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  selectedButtonText: {
    color: '#fff',
  },
  currentPlanButtonText: {
    color: '#fff',
  },
  checkIcon: {
    marginLeft: 4,
  },
});
