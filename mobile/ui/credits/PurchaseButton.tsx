import React from "react";
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface PurchaseButtonProps {
  isSelected: boolean;
  isLoading: boolean;
  isUpgrade?: boolean;
  isDowngrade?: boolean;
  isConsumable?: boolean;
  onPress: () => void;
}

/**
 * Purchase button component
 * Displays a button to complete the purchase
 */
export const PurchaseButton: React.FC<PurchaseButtonProps> = ({
  isSelected,
  isLoading,
  isUpgrade = false,
  isDowngrade = false,
  isConsumable = false,
  onPress,
}) => {
  // Determine button text based on upgrade/downgrade status
  const getButtonText = () => {
    if (isConsumable) return "Buy Days";
    if (isUpgrade) return "Upgrade Plan";
    if (isDowngrade) return "Downgrade Plan";
    return "Complete Purchase";
  };

  return (
    <TouchableOpacity
      style={[
        styles.purchaseButton,
        (!isSelected || isLoading) && styles.disabledButton,
      ]}
      onPress={onPress}
      disabled={!isSelected || isLoading}
    >
      {isLoading ? (
        <ActivityIndicator color="#fff" size="small" />
      ) : (
        <>
          <Ionicons name="cart" size={20} color="#fff" />
          <Text style={styles.purchaseButtonText}>{getButtonText()}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  purchaseButton: {
    backgroundColor: "#2196F3",
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  purchaseButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
});
