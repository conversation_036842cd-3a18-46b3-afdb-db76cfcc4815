import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

/**
 * Information card component for subscription screen
 * Displays information about how the subscription system works
 */
export const InfoCard: React.FC = () => {
  return (
    <View style={styles.infoCard}>
      <Text style={styles.infoTitle}>How Subscriptions Work</Text>

      <View style={styles.infoItem}>
        <Ionicons name="information-circle" size={18} color="#2196F3" />
        <Text style={styles.infoText}>
          Each trip generation uses 1 day from your monthly allowance
        </Text>
      </View>

      <View style={styles.infoItem}>
        <Ionicons name="information-circle" size={18} color="#2196F3" />
        <Text style={styles.infoText}>Unused days don't roll over to next month</Text>
      </View>

      <View style={styles.infoItem}>
        <Ionicons name="information-circle" size={18} color="#2196F3" />
        <Text style={styles.infoText}>Day refills expire at your plan cycle end</Text>
      </View>

      <View style={styles.infoItem}>
        <Ionicons name="information-circle" size={18} color="#2196F3" />
        <Text style={styles.infoText}>Credits never expire once purchased</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  infoCard: {
    backgroundColor: "#E3F2FD",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2196F3",
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: "#333",
    marginLeft: 8,
    flex: 1,
  },
});
