import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Product } from "react-native-iap";

// Define CreditPackage interface locally since the old service is removed
interface CreditPackage {
  id: string;
  productId: string;
  name: string;
  credits: number;
  price: number;
  type: "credit_package";
}

interface CreditPackageCardProps {
  pkg: CreditPackage;
  product: (Product & CreditPackage) | undefined;
  isSelected: boolean;
  onSelect: (id: string) => void;
}

/**
 * Credit package card component
 * Displays a single credit package with price and selection state
 */
export const CreditPackageCard: React.FC<CreditPackageCardProps> = ({
  pkg,
  product,
  isSelected,
  onSelect,
}) => {
  const price = product?.localizedPrice || `$${pkg.price}`;

  return (
    <TouchableOpacity
      style={[
        styles.packageCard,
        isSelected && styles.selectedPackage,
        pkg.popular && styles.popularPackage,
        !product && styles.unavailablePackage,
      ]}
      onPress={() => product && onSelect(pkg.id)}
      disabled={!product}
    >
      {pkg.popular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularText}>Best Value</Text>
        </View>
      )}

      <View style={styles.packageHeader}>
        <Text style={styles.packageName}>{pkg.name}</Text>
        <View style={styles.priceContainer}>
          {!product ? (
            <Text style={styles.unavailableText}>Unavailable</Text>
          ) : (
            <Text style={styles.packagePrice}>{price}</Text>
          )}
        </View>
      </View>

      <View style={styles.packageDetails}>
        <View style={styles.creditAmount}>
          <Ionicons name="flash" size={20} color="#2196F3" />
          <Text style={styles.creditText}>
            {pkg.credits.toLocaleString()} credits
          </Text>
        </View>
      </View>

      <View style={styles.selectionIndicator}>
        {isSelected ? (
          <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
        ) : (
          <View style={styles.unselectedCircle} />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  packageCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    position: "relative",
  },
  selectedPackage: {
    borderColor: "#2196F3",
    borderWidth: 2,
    shadowColor: "#2196F3",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  popularPackage: {
    borderColor: "#4CAF50",
    borderWidth: 2,
  },
  unavailablePackage: {
    opacity: 0.6,
    backgroundColor: "#f5f5f5",
  },
  popularBadge: {
    position: "absolute",
    top: -10,
    right: 16,
    backgroundColor: "#4CAF50",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  packageHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  packageName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: "700",
    color: "#333",
  },
  unavailableText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#999",
    fontStyle: "italic",
  },
  packageDetails: {
    marginBottom: 8,
  },
  creditAmount: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  creditText: {
    fontSize: 16,
    color: "#666",
    marginLeft: 8,
  },
  selectionIndicator: {
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  unselectedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#ccc",
  },
});
