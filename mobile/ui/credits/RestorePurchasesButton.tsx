import React from "react";
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface RestorePurchasesButtonProps {
  isRestoring: boolean;
  onPress: () => void;
}

/**
 * Restore purchases button component
 * Displays a button to restore previous purchases
 */
export const RestorePurchasesButton: React.FC<RestorePurchasesButtonProps> = ({
  isRestoring,
  onPress,
}) => {
  return (
    <TouchableOpacity
      style={styles.restoreButton}
      onPress={onPress}
      disabled={isRestoring}
    >
      {isRestoring ? (
        <ActivityIndicator size="small" color="#2196F3" />
      ) : (
        <>
          <Ionicons name="refresh" size={18} color="#2196F3" />
          <Text style={styles.restoreButtonText}>
            Restore Previous Purchases
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  restoreButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    padding: 12,
    marginBottom: 24,
  },
  restoreButtonText: {
    color: "#2196F3",
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
});
