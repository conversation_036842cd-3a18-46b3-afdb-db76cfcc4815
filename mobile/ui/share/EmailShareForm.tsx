import React from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface EmailShareFormProps {
  email: string;
  setEmail: (email: string) => void;
  onEmailShare: () => void;
}

/**
 * Email share form component for share screen
 */
export const EmailShareForm: React.FC<EmailShareFormProps> = ({
  email,
  setEmail,
  onEmailShare,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Share via Email</Text>

      <View style={styles.emailContainer}>
        <TextInput
          style={styles.emailInput}
          placeholder="Enter email address"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <TouchableOpacity style={styles.sendButton} onPress={onEmailShare}>
          <Ionicons name="send" size={20} color="#FFF" />
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 15,
    color: "#333",
  },
  emailContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  emailInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 10,
    fontSize: 16,
  },
  sendButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#4CAF50",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  sendButtonText: {
    color: "#FFF",
    fontWeight: "600",
    marginLeft: 8,
  },
});
