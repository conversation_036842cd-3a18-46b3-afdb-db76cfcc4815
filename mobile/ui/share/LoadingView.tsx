import React from "react";
import {
  ActivityIndicator,
  SafeAreaView,
  StyleSheet,
  View,
} from "react-native";
import ScreenHeader from "../common/ScreenHeader";

/**
 * Loading view component for share screen
 */
export const LoadingView: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Share Your Trip"
        subtitle="Share your itinerary with friends or export it for your records"
      />
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000" />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
