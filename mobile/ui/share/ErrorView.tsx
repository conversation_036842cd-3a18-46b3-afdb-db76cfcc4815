import React from "react";
import { SafeAreaView, StyleSheet } from "react-native";
import ErrorState from "../common/ErrorState";
import ScreenHeader from "../common/ScreenHeader";

interface ErrorViewProps {
  error: string;
  onRetry: () => void;
}

/**
 * Error view component for share screen
 */
export const ErrorView: React.FC<ErrorViewProps> = ({ error, onRetry }) => {
  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Share Your Trip"
        subtitle="Share your itinerary with friends or export it for your records"
      />
      <ErrorState error={error} onRetry={onRetry} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
});
