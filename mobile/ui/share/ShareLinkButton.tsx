import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ShareLinkButtonProps {
  shareUrl: string;
  onShareLink: () => void;
}

/**
 * Share link button component for share screen
 */
export const ShareLinkButton: React.FC<ShareLinkButtonProps> = ({
  shareUrl,
  onShareLink,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Share Link</Text>

      <View style={styles.linkContainer}>
        <Text style={styles.linkText} numberOfLines={1}>
          {shareUrl}
        </Text>

        <TouchableOpacity style={styles.copyButton} onPress={onShareLink}>
          <Ionicons name="copy-outline" size={20} color="#FFF" />
          <Text style={styles.copyButtonText}>Copy & Share</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 15,
    color: "#333",
  },
  linkContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  linkText: {
    flex: 1,
    fontSize: 14,
    color: "#666",
    marginRight: 10,
  },
  copyButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2196F3",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  copyButtonText: {
    color: "#FFF",
    fontWeight: "600",
    marginLeft: 6,
  },
});
