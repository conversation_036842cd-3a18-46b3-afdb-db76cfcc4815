import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { DbTrip as Trip } from "../../lib/types";
import { formatDate, getTotalActivities } from "../../lib/utils/share-utils";

interface TripPreviewCardProps {
  trip: Trip;
}

/**
 * Get icon name for activity type in highlights
 * @param type Activity type
 * @returns Icon name
 */
const getActivityIconForHighlight = (type: string) => {
  switch (type) {
    case "meal":
      return "restaurant-outline";
    default:
      return "location-outline";
  }
};

/**
 * Trip preview card component for share screen
 * Displays detailed information about the trip
 * Initially shows a condensed version with option to expand
 */
export const TripPreviewCard: React.FC<TripPreviewCardProps> = ({ trip }) => {
  const [expanded, setExpanded] = useState(false);
  const tripDetails = trip.tripDetails;
  const itinerary = trip.itinerary;
  const costBreakdown = trip.costBreakdown;

  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  return (
    <View style={styles.previewCard}>
      {/* Trip name and destination - Always visible */}
      <Text style={styles.tripName}>{trip.name}</Text>

      <View style={styles.previewRow}>
        <Ionicons name="location" size={20} color="#2196F3" />
        <Text style={styles.previewText}>{tripDetails?.destination}</Text>
      </View>

      {/* Trip dates - Always visible */}
      <View style={styles.previewRow}>
        <Ionicons name="calendar" size={20} color="#4CAF50" />
        <Text style={styles.previewText}>
          {formatDate(tripDetails?.startDate)} -{" "}
          {formatDate(tripDetails?.endDate)}
        </Text>
      </View>

      {/* Trip duration and activities - Always visible */}
      <View style={styles.previewRow}>
        <Ionicons name="time" size={20} color="#FF9800" />
        <Text style={styles.previewText}>
          {itinerary?.length} days • {getTotalActivities(itinerary)} activities
        </Text>
      </View>

      {/* Budget - Always visible if available */}
      {tripDetails?.budget && (
        <View style={styles.previewRow}>
          <Ionicons name="cash" size={20} color="#9C27B0" />
          <Text style={styles.previewText}>Budget: ${tripDetails.budget}</Text>
        </View>
      )}

      {/* Show More/Less Button */}
      <TouchableOpacity
        style={styles.showMoreButton}
        onPress={toggleExpanded}
        activeOpacity={0.7}
      >
        <Text style={styles.showMoreText}>
          {expanded ? "Show Less" : "Show More Details"}
        </Text>
        <Ionicons
          name={expanded ? "chevron-up" : "chevron-down"}
          size={20}
          color="#2196F3"
        />
      </TouchableOpacity>

      {/* Expanded Content */}
      {expanded && (
        <View style={styles.expandedContent}>
          {/* Travel type and intensity */}
          <View style={styles.previewRow}>
            <Ionicons name="compass" size={20} color="#3F51B5" />
            <Text style={styles.previewText}>
              {tripDetails?.travelType} • Intensity: {tripDetails?.intensity}/10
            </Text>
          </View>

          {/* Cities */}
          <View style={styles.previewRow}>
            <Ionicons name="business" size={20} color="#607D8B" />
            <Text style={styles.previewText}>
              From {tripDetails?.arrivalCity} to {tripDetails?.departureCity}
            </Text>
          </View>

          {/* Travelers */}
          <View style={styles.previewRow}>
            <Ionicons name="people" size={20} color="#009688" />
            <Text style={styles.previewText}>
              {tripDetails?.people?.adults}{" "}
              {tripDetails?.people?.adults === 1 ? "Adult" : "Adults"}
              {tripDetails?.people?.children > 0 &&
                ` • ${tripDetails?.people?.children} ${tripDetails?.people?.children === 1 ? "Child" : "Children"}`}
            </Text>
          </View>

          {/* Cost breakdown */}
          {costBreakdown && (
            <View style={styles.costBreakdownContainer}>
              <Text style={styles.sectionTitle}>Estimated Costs</Text>

              <View style={styles.costGrid}>
                <View style={styles.costItem}>
                  <Ionicons name="restaurant" size={16} color="#4CAF50" />
                  <Text style={styles.costLabel}>Meals</Text>
                  <Text style={styles.costValue}>${costBreakdown.meals}</Text>
                </View>

                <View style={styles.costItem}>
                  <Ionicons name="ticket" size={16} color="#FFC107" />
                  <Text style={styles.costLabel}>Activities</Text>
                  <Text style={styles.costValue}>
                    ${costBreakdown.activities}
                  </Text>
                </View>
              </View>

              <View style={styles.totalCostContainer}>
                <Text style={styles.totalCostLabel}>Total Estimated Cost</Text>
                <Text style={styles.totalCostValue}>
                  ${costBreakdown.total_estimated_cost}
                </Text>
              </View>
            </View>
          )}

          {/* Additional Tips */}
          {trip.additionalTips && trip.additionalTips.length > 0 && (
            <View style={styles.tipsContainer}>
              <Text style={styles.sectionTitle}>Travel Tips</Text>
              {trip.additionalTips.slice(0, 3).map((tip, index) => (
                <View key={index} style={styles.tipItem}>
                  <Ionicons
                    name="information-circle"
                    size={18}
                    color="#2196F3"
                  />
                  <Text style={styles.tipText}>{tip}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Must Visit Cities */}
          {tripDetails?.mustVisitCities &&
            tripDetails.mustVisitCities.length > 0 && (
              <View style={styles.citiesContainer}>
                <Text style={styles.sectionTitle}>Must Visit Places</Text>
                <View style={styles.citiesList}>
                  {tripDetails.mustVisitCities.map((city, index) => (
                    <View key={index} style={styles.cityTag}>
                      <Ionicons name="location" size={14} color="#FF5722" />
                      <Text style={styles.cityText}>{city}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

          {/* Daily Schedule */}
          <View style={styles.scheduleContainer}>
            <Text style={styles.sectionTitle}>Daily Schedule</Text>
            <View style={styles.scheduleRow}>
              <Ionicons name="sunny" size={18} color="#FF9800" />
              <Text style={styles.scheduleText}>
                Wake up: {tripDetails?.wakeUpTime || "7:00 AM"}
              </Text>
            </View>
            <View style={styles.scheduleRow}>
              <Ionicons name="moon" size={18} color="#3F51B5" />
              <Text style={styles.scheduleText}>
                Sleep: {tripDetails?.sleepTime || "10:00 PM"}
              </Text>
            </View>
          </View>

          {/* Trip Highlights */}
          {itinerary && itinerary.length > 0 && (
            <View style={styles.highlightsContainer}>
              <Text style={styles.sectionTitle}>Trip Highlights</Text>
              {itinerary
                .slice(0, 3)
                .flatMap((day) => day.activities.slice(0, 2))
                .slice(0, 4)
                .map((activity, index) => (
                  <View key={index} style={styles.highlightItem}>
                    <View style={styles.highlightIconContainer}>
                      <Ionicons
                        name={getActivityIconForHighlight(activity.type)}
                        size={18}
                        color="#FFF"
                      />
                    </View>
                    <View style={styles.highlightContent}>
                      <Text style={styles.highlightTitle}>{activity.name}</Text>
                      {activity.description && (
                        <Text
                          style={styles.highlightDescription}
                          numberOfLines={2}
                        >
                          {activity.description}
                        </Text>
                      )}
                    </View>
                  </View>
                ))}
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  previewCard: {
    backgroundColor: "#f8f9fa",
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#ddd",
    marginBottom: 20,
  },
  tripName: {
    fontSize: 20,
    fontWeight: "700",
    color: "#2196F3",
    marginBottom: 16,
    textAlign: "center",
  },
  previewRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  previewText: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginLeft: 10,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 16,
    marginBottom: 12,
  },
  // Cost breakdown styles
  costBreakdownContainer: {
    marginTop: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: "#eee",
  },
  costGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  costItem: {
    width: "48%",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    alignItems: "center",
  },
  costLabel: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
    marginBottom: 4,
  },
  costValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  totalCostContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 12,
  },
  totalCostLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  totalCostValue: {
    fontSize: 18,
    fontWeight: "700",
    color: "#2196F3",
  },
  // Tips styles
  tipsContainer: {
    marginTop: 16,
    backgroundColor: "#f5f8ff",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#e3f2fd",
  },
  tipItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
    paddingVertical: 4,
  },
  tipText: {
    fontSize: 14,
    color: "#333",
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },

  // Cities styles
  citiesContainer: {
    marginTop: 16,
  },
  citiesList: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  cityTag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff3e0",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
  },
  cityText: {
    color: "#FF5722",
    fontSize: 14,
    marginLeft: 4,
  },

  // Schedule styles
  scheduleContainer: {
    marginTop: 16,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
  },
  scheduleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  scheduleText: {
    fontSize: 16,
    color: "#333",
    marginLeft: 10,
  },

  // Highlights styles
  highlightsContainer: {
    marginTop: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#eee",
  },
  highlightItem: {
    flexDirection: "row",
    marginBottom: 12,
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
    padding: 10,
    borderLeftWidth: 3,
    borderLeftColor: "#2196F3",
  },
  highlightIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  highlightContent: {
    flex: 1,
  },
  highlightTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  highlightDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },

  // Show More/Less button styles
  showMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
    paddingVertical: 10,
    backgroundColor: "#f0f8ff",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e3f2fd",
  },
  showMoreText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2196F3",
    marginRight: 8,
  },
  expandedContent: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 16,
  },
});
