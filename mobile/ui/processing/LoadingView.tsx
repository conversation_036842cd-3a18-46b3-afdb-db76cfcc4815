import React from "react";
import { View, Text, ActivityIndicator, StyleSheet } from "react-native";
import { COLORS } from "../../lib/constants/processing-constants";
import { TravelAnimation } from "./TravelAnimation";

interface LoadingViewProps {
  message: string;
}

/**
 * Loading view component
 * Shows a loading animation with a message
 */
export const LoadingView: React.FC<LoadingViewProps> = ({ message }) => (
  <View style={styles.centeredContainer}>
    <TravelAnimation />
    <Text style={styles.status}>{message}</Text>
    <ActivityIndicator size="large" color={COLORS.PRIMARY} />
  </View>
);

const styles = StyleSheet.create({
  centeredContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  status: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 30,
    textAlign: "center",
  },
});
