import React from "react";
import { ErrorView } from "./ErrorView";
import { LoadingView } from "./LoadingView";

interface GenerationStatusProps {
  status: string | null;
  tripId: string | null;
  error: string | null;
  isLoading: boolean;
  retry: () => void;
}

/**
 * Generation status component
 * Shows the current status of trip generation
 */
export const GenerationStatus: React.FC<GenerationStatusProps> = ({
  status,
  tripId,
  error,
  isLoading,
  retry,
}) => {
  // Determine which view to show based on state
  // Prioritize loading state during active generation
  if (isLoading) {
    return <LoadingView message="Please wait..." />;
  }

  if (error) {
    return <ErrorView error={error} tripId={tripId} retry={retry} />;
  }

  if (!tripId) {
    return <LoadingView message="We're working on it..." />;
  }

  return <LoadingView message={status || "Please wait..."} />;
};
