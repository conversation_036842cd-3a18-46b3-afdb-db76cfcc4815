import React from "react";
import { View, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Animated from "react-native-reanimated";
import { useTransportAnimation } from "../../lib/hooks/useTransportAnimation";
import {
  COLORS,
  TRANSPORT_ICONS,
} from "../../lib/constants/processing-constants";

/**
 * Travel animation component
 * Shows a rotating transport icon
 */
export const TravelAnimation: React.FC = () => {
  // Use our custom animation hook
  const { animatedStyle, currentIcon } = useTransportAnimation();

  return (
    <View style={styles.animationContainer}>
      <Animated.View style={[styles.iconContainer, animatedStyle]}>
        <MaterialCommunityIcons
          name={TRANSPORT_ICONS[currentIcon].name}
          size={64}
          color={TRANSPORT_ICONS[currentIcon].color}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  animationContainer: {
    height: 120,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.BACKGROUND_LIGHT,
    borderRadius: 40,
  },
});
