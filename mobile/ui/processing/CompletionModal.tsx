import React from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Animated, {
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import {
  COLORS,
  CONFETTI_COLORS,
} from "../../lib/constants/processing-constants";
import { useModalAnimation } from "../../lib/hooks/useModalAnimation";
import { useConfettiAnimation } from "../../lib/hooks/useConfettiAnimation";
import { navigateToTripView } from "../../lib/utils/navigation";

interface CompletionModalProps {
  visible: boolean;
  tripId: string;
}

/**
 * Completion modal component
 * Shows a modal when trip generation is complete
 */
export const CompletionModal: React.FC<CompletionModalProps> = ({
  visible,
  tripId,
}) => {
  // Use our custom animation hooks
  const { containerStyle } = useModalAnimation(visible);
  const { confettiY, confettiX, confettiRotation, confettiScale } =
    useConfettiAnimation(visible);

  return (
    <Modal transparent visible={visible} animationType="fade">
      <View style={styles.modalOverlay}>
        <Animated.View style={[styles.modalContent, containerStyle]}>
          <View style={styles.confettiContainer} pointerEvents="none">
            {confettiY.map((y, index) => {
              const animatedStyle = useAnimatedStyle(() => ({
                transform: [
                  { translateY: y.value },
                  { translateX: confettiX[index].value },
                  { rotate: `${confettiRotation[index].value}deg` },
                  { scale: confettiScale[index].value },
                ],
                opacity: withTiming(y.value > 400 ? 0 : 1, { duration: 500 }),
              }));

              return (
                <Animated.View
                  key={index}
                  style={[
                    styles.confetti,
                    {
                      backgroundColor:
                        CONFETTI_COLORS[index % CONFETTI_COLORS.length],
                    },
                    animatedStyle,
                  ]}
                />
              );
            })}
          </View>

          <Text style={styles.modalTitle}>🎉 Your Trip is Ready! 🎉</Text>
          <Text style={styles.modalSubtitle}>
            Get ready for an amazing adventure!
          </Text>

          <TouchableOpacity
            style={[styles.viewTripButton, { zIndex: 30 }]}
            activeOpacity={0.7}
            onPress={() => navigateToTripView(tripId)}
          >
            <MaterialCommunityIcons
              name="map-marker-path"
              size={24}
              color="#FFF"
            />
            <Text style={styles.viewTripButtonText}>View My Trip</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 24,
    paddingBottom: 36, // Add more padding at the bottom
    alignItems: "center",
    width: "85%",
    maxWidth: 400,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    position: "relative", // Ensure proper stacking context
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.PRIMARY,
    marginBottom: 12,
    textAlign: "center",
  },
  modalSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 24,
    textAlign: "center",
  },
  viewTripButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 14, // Slightly larger for better touch target
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    zIndex: 20, // Higher z-index to ensure it's above confetti
    elevation: 6, // Add elevation for Android
    shadowColor: "#000", // Shadow for iOS
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    marginTop: 30, // Add more margin to separate from text
  },
  viewTripButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
  },
  confettiContainer: {
    position: "absolute",
    top: -100,
    left: 0,
    right: 0,
    height: 300,
    width: "100%",
    overflow: "hidden",
    zIndex: 5, // Lower z-index so it doesn't block interactions
  },
  confetti: {
    position: "absolute",
    width: 15,
    height: 15,
    borderRadius: 7,
    opacity: 0.9,
    zIndex: 20,
  },
});
