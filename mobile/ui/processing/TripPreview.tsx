import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { COLORS } from "../../lib/constants/processing-constants";
import { navigateToTripView } from "../../lib/utils/navigation";
import { DbTrip } from "../../lib/types";

interface TripPreviewProps {
  tripDetails: DbTrip["tripDetails"];
  tripId: string;
}

/**
 * Trip preview component
 * Shows a preview of the trip being generated
 */
export const TripPreview: React.FC<TripPreviewProps> = ({ tripId }) => {
  return (
    <View style={styles.previewContainer}>
      <View style={styles.previewHeader}>
        <MaterialCommunityIcons
          name="information-outline"
          size={24}
          color={COLORS.PRIMARY}
        />
        <Text style={styles.previewTitle}>Trip Generation in Progress</Text>
      </View>

      <View style={styles.previewMessage}>
        <Text style={styles.messageText}>
          Your trip itinerary is being generated day by day. You can view the
          progress in real-time!
        </Text>
      </View>

      <View style={styles.previewActions}>
        <TouchableOpacity
          style={styles.previewButton}
          onPress={() => navigateToTripView(tripId, "list")}
        >
          <MaterialCommunityIcons name="eye" size={20} color="#FFF" />
          <Text style={styles.previewButtonText}>View Generation Progress</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    backgroundColor: COLORS.BACKGROUND_LIGHT,
    borderRadius: 15,
    padding: 20,
    width: "100%",
    maxWidth: 400,
    marginTop: 20,
    marginBottom: 20,
  },
  previewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
    gap: 8,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: COLORS.PRIMARY,
  },
  previewMessage: {
    backgroundColor: COLORS.BACKGROUND_PRIMARY_LIGHT,
    borderRadius: 10,
    padding: 16,
    marginVertical: 10,
  },
  messageText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 22,
    textAlign: "center",
  },
  previewActions: {
    marginTop: 10,
    alignItems: "center",
  },
  previewButton: {
    backgroundColor: COLORS.PRIMARY,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    marginTop: 10,
    gap: 8,
  },
  previewButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "500",
  },
});
