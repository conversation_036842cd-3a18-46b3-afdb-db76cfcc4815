import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { COLORS } from "../../lib/constants/processing-constants";
import {
  navigateToHome,
  navigateToTripDetails,
  navigateToTripView,
} from "../../lib/utils/navigation";

/**
 * Maps developer-oriented error messages to user-friendly messages
 * (Duplicated from ErrorState.tsx for consistency)
 */
function getUserFriendlyErrorMessage(error: string): string {
  const errorLower = error.toLowerCase();

  // Server connection errors
  if (errorLower.includes('failed to fetch') ||
    errorLower.includes('network') ||
    errorLower.includes('connection') ||
    errorLower.includes('econnreset') ||
    errorLower.includes('econnrefused') ||
    errorLower.includes('enotfound')) {
    return "Our servers are experiencing issues. Please try again in a few moments.";
  }

  // Trip-related errors
  if (errorLower.includes('failed to fetch trips')) {
    return "We're having trouble loading your trips. Please try again.";
  }

  if (errorLower.includes('failed to fetch trip') ||
    errorLower.includes('failed to load trip')) {
    return "We couldn't load this trip. Please try again.";
  }

  if (errorLower.includes('failed to create trip') ||
    errorLower.includes('failed to generate trip')) {
    return "We couldn't create your trip right now. Please try again.";
  }

  // Authentication errors
  if (errorLower.includes('unauthorized') ||
    errorLower.includes('authentication') ||
    errorLower.includes('forbidden')) {
    return "Your session has expired. Please log in again.";
  }

  // Server errors
  if (errorLower.includes('server error') ||
    errorLower.includes('internal server error') ||
    error.includes('500') || error.includes('502') || error.includes('503')) {
    return "Our servers are experiencing issues. Please try again in a few moments.";
  }

  // Rate limiting
  if (errorLower.includes('rate limit') ||
    errorLower.includes('too many requests') ||
    error.includes('429')) {
    return "You're making requests too quickly. Please wait a moment and try again.";
  }

  // Timeout errors
  if (errorLower.includes('timeout') ||
    errorLower.includes('timed out')) {
    return "The request is taking longer than expected. Please try again.";
  }

  // Generic unknown errors
  if (errorLower.includes('unknown error') ||
    errorLower.includes('unexpected error')) {
    return "Something unexpected happened. Please try again.";
  }

  // If no specific mapping found, return a generic user-friendly message
  return "Something went wrong. Please try again.";
}

interface ErrorViewProps {
  error: string | null;
  tripId: string | null;
  retry: () => void;
}

/**
 * Error view component
 * Shows an error message with action buttons
 */
export const ErrorView: React.FC<ErrorViewProps> = ({
  error,
  tripId,
  retry,
}) => {
  const userFriendlyMessage = error ? getUserFriendlyErrorMessage(error) : "Something went wrong. Please try again.";

  return (
    <View style={styles.centeredContainerWithPadding}>
      <MaterialCommunityIcons
        name="alert-circle-outline"
        size={48}
        color={COLORS.ERROR}
      />
      <Text style={[styles.error, { marginTop: 12 }]}>{userFriendlyMessage}</Text>
      <View style={styles.errorActions}>
        {!tripId ? (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: COLORS.PRIMARY }]}
            onPress={retry}
          >
            <MaterialCommunityIcons name="refresh" size={20} color="#FFF" />
            <Text style={styles.actionButtonText}>Try Again</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: COLORS.PRIMARY }]}
            onPress={() => navigateToTripView(tripId, "list")}
          >
            <MaterialCommunityIcons name="play" size={20} color="#FFF" />
            <Text style={styles.actionButtonText}>Continue Generation</Text>
          </TouchableOpacity>
        )}

        {!tripId && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: COLORS.SUCCESS }]}
            onPress={navigateToTripDetails}
          >
            <MaterialCommunityIcons name="pencil" size={20} color="#FFF" />
            <Text style={styles.actionButtonText}>Edit Details</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: COLORS.GRAY }]}
          onPress={navigateToHome}
        >
          <MaterialCommunityIcons name="home" size={20} color="#FFF" />
          <Text style={styles.actionButtonText}>Go Home</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  centeredContainerWithPadding: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  error: {
    fontSize: 16,
    color: COLORS.ERROR,
    marginBottom: 30,
    textAlign: "center",
  },
  errorActions: {
    flexDirection: "column",
    gap: 12,
    marginTop: 24,
    width: "100%",
    maxWidth: 300,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "500",
  },
});
