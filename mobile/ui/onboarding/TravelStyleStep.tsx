import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";
import { TRAVEL_TYPES } from "../../lib/constants";
import { TripDetails } from "../../lib/types";
import { BudgetSelector } from "../common/BudgetSelector";
import { PeopleCounter } from "../common/PeopleCounter";

interface TravelStyleStepProps {
  formData: TripDetails;
  onUpdateFormData: (updates: Partial<TripDetails>) => void;
}

const TravelStyleStep: React.FC<TravelStyleStepProps> = ({
  formData,
  onUpdateFormData,
}) => {
  const getTravelTypeIcon = (type: string) => {
    switch (type) {
      case "Luxury":
        return "diamond-outline";
      case "Budget":
        return "wallet-outline";
      case "Cultural":
        return "library-outline";
      case "Adventure":
        return "compass-outline";
      case "Family":
        return "people-outline";
      case "Romantic":
        return "heart-outline";
      case "Business":
        return "briefcase-outline";
      case "Solo":
        return "person-outline";
      case "Roadtrip":
        return "car-outline";
      default:
        return "help-outline";
    }
  };

  const getTravelTypeDescription = (type: string) => {
    switch (type) {
      case "Luxury":
        return "Premium experiences, ultimate comfort";
      case "Budget":
        return "Smart spending, maximum experience";
      case "Cultural":
        return "Immerse in local traditions and heritage";
      case "Adventure":
        return "Thrilling experiences and exploration";
      case "Family":
        return "Fun activities for all ages";
      case "Romantic":
        return "Intimate moments and special experiences";
      case "Business":
        return "Efficient and professional travel";
      case "Solo":
        return "Independent exploration and discovery";
      case "Roadtrip":
        return "Explore at your own pace, with stops and starts";
      default:
        return "";
    }
  };

  return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Trip Style & Details</Text>
      <Text style={styles.stepDescription}>
        Choose your travel style and customize trip details
      </Text>

      <Text style={styles.sectionTitle}>Travel Style</Text>
      <View style={styles.travelStyleGrid}>
        {TRAVEL_TYPES.filter((type) => type !== "Select Type").map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.travelStyleCard,
              formData.travelType === type && styles.selectedTravelStyle,
            ]}
            onPress={() => onUpdateFormData({ travelType: type })}
          >
            <View style={styles.travelStyleContent}>
              <Ionicons
                name={getTravelTypeIcon(type)}
                size={24}
                color={formData.travelType === type ? "#fff" : "#666"}
              />
              <Text
                style={[
                  styles.travelStyleTitle,
                  formData.travelType === type && styles.selectedText,
                ]}
              >
                {type}
              </Text>
              <Text
                style={[
                  styles.travelStyleDescription,
                  formData.travelType === type && styles.selectedText,
                ]}
                numberOfLines={2}
              >
                {getTravelTypeDescription(type)}
              </Text>
            </View>
            <View
              style={[
                styles.arrowContainer,
                formData.travelType === type && styles.selectedArrowContainer,
              ]}
            >
              <Ionicons
                name="arrow-forward"
                size={16}
                color={formData.travelType === type ? "#fff" : "#999"}
              />
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={styles.sectionTitle}>Trip Details</Text>
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Budget Category</Text>
        <BudgetSelector
          value={formData.budget}
          onValueChange={(value) => onUpdateFormData({ budget: value })}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Trip Intensity</Text>
        <View style={styles.intensityContainer}>
          <Text style={styles.intensityValue}>
            {Math.round(formData.intensity)}
          </Text>
          <Slider
            style={styles.intensitySlider}
            minimumValue={0}
            maximumValue={10}
            step={1}
            value={formData.intensity}
            onValueChange={(value) =>
              onUpdateFormData({ intensity: Math.round(value) })
            }
            minimumTrackTintColor="#2196F3"
            maximumTrackTintColor="#000000"
            thumbTintColor="#2196F3"
          />
          <View style={styles.intensityLabels}>
            <View style={styles.intensityLabelContainer}>
              <Ionicons name="bed-outline" size={20} color="#666" />
              <Text style={styles.intensityLabelText}>Chill</Text>
            </View>
            <View style={styles.intensityLabelContainer}>
              <Ionicons name="rocket-outline" size={20} color="#666" />
              <Text style={styles.intensityLabelText}>Intense</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.inputGroup}>
        <PeopleCounter
          label="Number of Adults"
          value={formData.people.adults}
          minValue={1}
          onIncrement={() =>
            onUpdateFormData({
              people: {
                ...formData.people,
                adults: formData.people.adults + 1,
              },
            })
          }
          onDecrement={() => {
            if (formData.people.adults > 1) {
              onUpdateFormData({
                people: {
                  ...formData.people,
                  adults: formData.people.adults - 1,
                },
              });
            }
          }}
        />
      </View>

      <View style={styles.inputGroup}>
        <PeopleCounter
          label="Number of Children"
          value={formData.people.children}
          minValue={0}
          onIncrement={() =>
            onUpdateFormData({
              people: {
                ...formData.people,
                children: formData.people.children + 1,
              },
            })
          }
          onDecrement={() => {
            if (formData.people.children > 0) {
              onUpdateFormData({
                people: {
                  ...formData.people,
                  children: formData.people.children - 1,
                },
              });
            }
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 24,
    marginBottom: 16,
  },
  travelStyleGrid: {
    gap: 12,
  },
  travelStyleCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedTravelStyle: {
    backgroundColor: "#2196F3",
  },
  travelStyleContent: {
    flex: 1,
  },
  travelStyleTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 8,
  },
  travelStyleDescription: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  selectedText: {
    color: "#fff",
  },
  arrowContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
  },
  selectedArrowContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginBottom: 8,
  },
  intensityContainer: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  intensityValue: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 8,
    color: "#2196F3",
  },
  intensitySlider: {
    width: "100%",
    height: 40,
  },
  intensityLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  intensityLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  intensityLabelText: {
    fontSize: 14,
    color: "#666",
  },
});

export default TravelStyleStep;
