import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import {
  LocationSuggestion,
  useLocationSuggestions,
} from "../../lib/hooks/useLocationSuggestions";
import { TripDetails } from "../../lib/types";
import { TRANSPORT_MODES } from "../../lib/constants";
import { LocationSelectionModal } from "../common/LocationSelectionModal";

interface LocationStepProps {
  formData: TripDetails;
  onUpdateFormData: (updates: Partial<TripDetails>) => void;
}

const LocationStep: React.FC<LocationStepProps> = ({
  formData,
  onUpdateFormData,
}) => {
  const {
    isLoading: isLoadingLocations,
    suggestions: locationSuggestions,
    searchCountries,
    searchCities,
    clearSuggestions,
  } = useLocationSuggestions({
    onError: (error) => console.error("Location search error:", error),
  });

  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showCityModal, setShowCityModal] = useState(false);
  const [activeField, setActiveField] = useState<
    "arrival" | "departure" | null
  >(null);
  const [searchQuery, setSearchQuery] = useState("");

  const handleCountrySearch = async (text: string) => {
    setSearchQuery(text);
    await searchCountries(text);
  };

  const selectCountry = (suggestion: LocationSuggestion) => {
    onUpdateFormData({
      destination: suggestion.name,
      arrivalCity: "",
      departureCity: "",
    });
    setShowCountryModal(false);
    setSearchQuery("");
    clearSuggestions();
  };

  const handleCitySearch = async (text: string) => {
    setSearchQuery(text);
    if (formData.destination) {
      await searchCities(text, formData.destination);
    }
  };

  const selectCity = (suggestion: LocationSuggestion) => {
    if (activeField && suggestion.name) {
      onUpdateFormData({
        [activeField === "arrival" ? "arrivalCity" : "departureCity"]:
          suggestion.name,
      });
    }
    setShowCityModal(false);
    setSearchQuery("");
    clearSuggestions();
    setActiveField(null);
  };

  const getTransportIcon = (mode: string) => {
    switch (mode) {
      case "Air":
        return "airplane";
      case "Land":
        return "bus";
      case "Sea":
        return "boat";
      default:
        return "help";
    }
  };

  return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Where are you traveling?</Text>
      <Text style={styles.stepDescription}>
        Fill in your travel locations and transport modes
      </Text>

      <View style={styles.locationCard}>
        <View style={styles.locationHeader}>
          <Ionicons name="map-outline" size={24} color="#666" />
          <Text style={styles.locationHeaderText}>Main Destination</Text>
        </View>
        <TouchableOpacity
          style={[styles.input, formData.destination && styles.filledInput]}
          onPress={() => {
            setShowCountryModal(true);
            setSearchQuery("");
          }}
        >
          <Text
            style={
              formData.destination ? styles.inputText : styles.placeholderText
            }
          >
            {formData.destination || "Select Destination"}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      <View style={[styles.locationCard, { marginTop: 20 }]}>
        <View style={styles.locationHeader}>
          <Ionicons name="airplane-outline" size={24} color="#666" />
          <Text style={styles.locationHeaderText}>Travel Cities</Text>
        </View>

        <View style={styles.cityBlock}>
          <TouchableOpacity
            style={[styles.input, formData.arrivalCity && styles.filledInput]}
            onPress={() => {
              setShowCityModal(true);
              setActiveField("arrival");
              setSearchQuery("");
            }}
          >
            <Text
              style={
                formData.arrivalCity ? styles.inputText : styles.placeholderText
              }
            >
              {formData.arrivalCity || "Select Arrival City"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>

          <View style={styles.transportModeContainer}>
            {TRANSPORT_MODES.map((mode) => (
              <TouchableOpacity
                key={mode}
                style={[
                  styles.transportChip,
                  formData.arrivalMode === mode && styles.selectedTransportChip,
                ]}
                onPress={() => onUpdateFormData({ arrivalMode: mode })}
              >
                <Ionicons
                  name={getTransportIcon(mode)}
                  size={16}
                  color={formData.arrivalMode === mode ? "#fff" : "#666"}
                />
                <Text
                  style={[
                    styles.transportChipText,
                    formData.arrivalMode === mode &&
                      styles.selectedTransportChipText,
                  ]}
                >
                  {mode}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={[styles.cityBlock, { marginTop: 16 }]}>
          <TouchableOpacity
            style={[styles.input, formData.departureCity && styles.filledInput]}
            onPress={() => {
              setShowCityModal(true);
              setActiveField("departure");
              setSearchQuery("");
            }}
          >
            <Text
              style={
                formData.departureCity
                  ? styles.inputText
                  : styles.placeholderText
              }
            >
              {formData.departureCity || "Select Departure City"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>

          <View style={styles.transportModeContainer}>
            {TRANSPORT_MODES.map((mode) => (
              <TouchableOpacity
                key={mode}
                style={[
                  styles.transportChip,
                  formData.departureMode === mode &&
                    styles.selectedTransportChip,
                ]}
                onPress={() => onUpdateFormData({ departureMode: mode })}
              >
                <Ionicons
                  name={getTransportIcon(mode)}
                  size={16}
                  color={formData.departureMode === mode ? "#fff" : "#666"}
                />
                <Text
                  style={[
                    styles.transportChipText,
                    formData.departureMode === mode &&
                      styles.selectedTransportChipText,
                  ]}
                >
                  {mode}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      <LocationSelectionModal
        visible={showCountryModal}
        onClose={() => setShowCountryModal(false)}
        title="Select Destination"
        placeholder="Search destinations..."
        onSearch={handleCountrySearch}
        searchQuery={searchQuery}
        isLoadingLocations={isLoadingLocations}
        locationSuggestions={locationSuggestions}
        onSelect={selectCountry}
      />

      <LocationSelectionModal
        visible={showCityModal}
        onClose={() => setShowCityModal(false)}
        title={`Select ${activeField === "arrival" ? "Arrival" : "Departure"} City`}
        placeholder="Search cities..."
        onSearch={handleCitySearch}
        searchQuery={searchQuery}
        isLoadingLocations={isLoadingLocations}
        locationSuggestions={locationSuggestions}
        onSelect={selectCity}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
  },
  locationCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  locationHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  locationHeaderText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 8,
  },
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },
  cityBlock: {
    marginBottom: 16,
  },
  transportModeContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    gap: 8,
  },
  transportChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  selectedTransportChip: {
    backgroundColor: "#2196F3",
  },
  transportChipText: {
    fontSize: 14,
    color: "#666",
  },
  selectedTransportChipText: {
    color: "#fff",
  },
});

export default LocationStep;
