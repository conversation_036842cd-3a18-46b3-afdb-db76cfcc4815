import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { CUISINE_PREFERENCES } from "../../lib/constants";
import { TripDetails } from "../../lib/types";

interface CuisinePreferencesStepProps {
  formData: TripDetails;
  onUpdateFormData: (updates: Partial<TripDetails>) => void;
}

const CuisinePreferencesStep: React.FC<CuisinePreferencesStepProps> = ({
  formData,
  onUpdateFormData,
}) => {
  const handleCuisinePress = (cuisine: string) => {
    let newPreferences: string[] = [];

    if (cuisine === "No Preference") {
      // If clicking 'No Preference', clear all other selections
      newPreferences = ["No Preference"];
    } else {
      // If clicking a specific cuisine
      if (formData.cuisinePreferences?.includes(cuisine)) {
        // Remove the cuisine if it's already selected
        newPreferences = formData.cuisinePreferences.filter(
          (c) => c !== cuisine,
        );
        if (newPreferences.length === 0) {
          // If no cuisines are selected, default to 'No Preference'
          newPreferences = ["No Preference"];
        }
      } else {
        // Add the cuisine and remove 'No Preference' if it exists
        newPreferences = [
          ...(formData.cuisinePreferences?.filter(
            (c) => c !== "No Preference",
          ) || []),
          cuisine,
        ];
      }
    }

    onUpdateFormData({ cuisinePreferences: newPreferences });
  };

  const getCuisineIcon = (cuisine: string) => {
    switch (cuisine) {
      case "Local":
        return "restaurant-outline";
      case "Traditional":
        return "home-outline";
      case "International":
        return "globe-outline";
      case "Vegetarian":
        return "leaf-outline";
      case "Vegan":
        return "nutrition-outline";
      case "Halal":
        return "moon-outline";
      case "Kosher":
        return "star-of-david-outline";
      case "Gluten-Free":
        return "medical-outline";
      case "Dairy-Free":
        return "water-outline";
      case "Sugar-Free":
        return "fitness-outline";
      case "No Preference":
        return "infinite-outline";
      default:
        return "restaurant-outline";
    }
  };

  return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Cuisine Preferences</Text>
      <Text style={styles.stepDescription}>
        Select your preferred cuisines for your trip
      </Text>
      <View style={styles.cuisineGrid}>
        {CUISINE_PREFERENCES.map((cuisine) => (
          <TouchableOpacity
            key={cuisine}
            style={[
              styles.cuisineCard,
              formData.cuisinePreferences?.includes(cuisine) &&
                styles.selectedCuisine,
            ]}
            onPress={() => handleCuisinePress(cuisine)}
          >
            <Ionicons
              name={getCuisineIcon(cuisine)}
              size={24}
              color={
                formData.cuisinePreferences?.includes(cuisine) ? "#fff" : "#666"
              }
            />
            <Text
              style={[
                styles.cuisineText,
                formData.cuisinePreferences?.includes(cuisine) &&
                  styles.selectedCuisineText,
              ]}
            >
              {cuisine}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
  },
  cuisineGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  cuisineCard: {
    width: "47%",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 12,
  },
  selectedCuisine: {
    backgroundColor: "#2196F3",
  },
  cuisineText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginTop: 8,
    textAlign: "center",
  },
  selectedCuisineText: {
    color: "#fff",
  },
});

export default CuisinePreferencesStep;
