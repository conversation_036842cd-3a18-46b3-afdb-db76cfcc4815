import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { INTERESTS } from "../../lib/constants";
import { TripDetails } from "../../lib/types";
import { useTripStore } from "../../lib/store";

interface InterestsStepProps {
  formData: TripDetails;
  onUpdateFormData: (updates: Partial<TripDetails>) => void;
}

const InterestsStep: React.FC<InterestsStepProps> = ({
  formData,
  onUpdateFormData,
}) => {
  const { addInterest, removeInterest } = useTripStore();
  const [customInterest, setCustomInterest] = useState("");
  const [customInterests, setCustomInterests] = useState<
    { id: string; name: string; icon: string }[]
  >([]);

  const handleInterestPress = (interest: {
    id: string;
    name: string;
    icon: string;
  }) => {
    if (formData.interests?.includes(interest.name)) {
      // Just deselect the interest but keep it in the list
      onUpdateFormData({
        interests: formData.interests.filter((i) => i !== interest.name),
      });
      removeInterest(interest.id);
    } else {
      // Select the interest
      onUpdateFormData({
        interests: [...(formData.interests || []), interest.name],
      });
      addInterest({
        id: interest.id,
        name: interest.name,
        icon: interest.icon,
      });
    }
  };

  const handleToggleAll = () => {
    const allInterests = [...INTERESTS, ...customInterests];
    const allSelected = allInterests.every((interest) =>
      formData.interests.includes(interest.name),
    );

    if (allSelected) {
      // Just deselect all interests but keep them in the list
      onUpdateFormData({ interests: [] });
      allInterests.forEach((interest) => removeInterest(interest.id));
    } else {
      // Select all interests
      const allNames = allInterests.map((interest) => interest.name);
      onUpdateFormData({ interests: allNames });
      allInterests.forEach((interest) =>
        addInterest({
          id: interest.id,
          name: interest.name,
          icon: interest.icon,
        }),
      );
    }
  };

  const handleAddCustom = () => {
    if (customInterest.trim()) {
      const newId = `custom-${Date.now()}`;
      const newInterestObject = {
        id: newId,
        name: customInterest.trim(),
        icon: "add-outline",
      };

      // Add to custom interests list
      setCustomInterests([newInterestObject, ...customInterests]);

      // Add to form data
      onUpdateFormData({
        interests: [newInterestObject.name, ...(formData.interests || [])],
      });

      // Add to trip store
      addInterest({
        id: newId,
        name: newInterestObject.name,
        icon: newInterestObject.icon,
      });

      setCustomInterest("");
    }
  };

  return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>What interests you?</Text>
      <Text style={styles.stepDescription}>
        Select your interests to help us personalize your trip
      </Text>

      <View style={styles.addInterestContainer}>
        <TextInput
          style={styles.input}
          placeholder="Add your own interest..."
          value={customInterest}
          onChangeText={setCustomInterest}
          onSubmitEditing={handleAddCustom}
        />
        <TouchableOpacity style={styles.addButton} onPress={handleAddCustom}>
          <Ionicons name="add-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.interestsGrid}>
        <TouchableOpacity
          style={[styles.interestItem]}
          onPress={handleToggleAll}
        >
          <View
            style={[
              styles.iconContainer,
              formData.interests.length === INTERESTS.length &&
                styles.selectedIconContainer,
            ]}
          >
            <Ionicons
              name="apps-outline"
              size={32}
              color={
                formData.interests.length === INTERESTS.length ? "#fff" : "#666"
              }
            />
          </View>
          <Text
            style={[
              styles.interestName,
              formData.interests.length === INTERESTS.length &&
                styles.selectedInterestName,
            ]}
          >
            {formData.interests.length === INTERESTS.length
              ? "Deselect All"
              : "Select All"}
          </Text>
        </TouchableOpacity>

        {[...customInterests, ...INTERESTS].map((interest) => (
          <TouchableOpacity
            key={interest.id}
            style={[
              styles.interestItem,
              formData.interests.includes(interest.name) && styles.selectedItem,
            ]}
            onPress={() => handleInterestPress(interest)}
          >
            <View
              style={[
                styles.iconContainer,
                formData.interests.includes(interest.name) &&
                  styles.selectedIconContainer,
              ]}
            >
              <Ionicons
                name={interest.icon as any}
                size={32}
                color={
                  formData.interests.includes(interest.name) ? "#fff" : "#666"
                }
              />
            </View>
            <Text
              style={[
                styles.interestName,
                formData.interests.includes(interest.name) &&
                  styles.selectedInterestName,
              ]}
            >
              {interest.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
  },
  addInterestContainer: {
    flexDirection: "row",
    marginBottom: 20,
  },
  input: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    height: 50,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    marginRight: 10,
    fontSize: 16,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
  },
  interestsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 10,
  },
  interestItem: {
    width: "33.33%",
    padding: 10,
    alignItems: "center",
  },
  selectedItem: {
    transform: [{ scale: 1.05 }],
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  selectedIconContainer: {
    backgroundColor: "#2196F3",
  },
  interestName: {
    fontSize: 14,
    color: "#333",
    textAlign: "center",
  },
  selectedInterestName: {
    color: "#2196F3",
    fontWeight: "600",
  },
});

export default InterestsStep;
