import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { TripDetails } from "../../lib/types";

interface OnboardingFooterProps {
  currentStep: number;
  totalSteps: number;
  formData: TripDetails;
  isLastStep: boolean;
  onNext: () => void;
  onSubmit: () => void;
  validateStep: (step: number) => boolean;
}

const OnboardingFooter: React.FC<OnboardingFooterProps> = ({
  currentStep,
  totalSteps,
  formData,
  isLastStep,
  onNext,
  onSubmit,
  validateStep,
}) => {
  const insets = useSafeAreaInsets();
  const isStepValid = validateStep(currentStep);

  const handleAction = () => {
    if (isLastStep) {
      onSubmit();
    } else {
      onNext();
    }
  };

  return (
    <View
      style={[
        styles.footer,
        {
          paddingBottom: Math.max(16, insets.bottom),
        },
      ]}
    >
      <TouchableOpacity
        style={[styles.button, !isStepValid && styles.disabledButton]}
        onPress={handleAction}
        disabled={!isStepValid}
      >
        <Text style={styles.buttonText}>
          {isLastStep ? "Create Trip" : "Continue"}
        </Text>
        <Ionicons
          name={isLastStep ? "checkmark" : "arrow-forward"}
          size={20}
          color="#fff"
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  footer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#2196F3",
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginRight: 8,
  },
});

export default OnboardingFooter;
