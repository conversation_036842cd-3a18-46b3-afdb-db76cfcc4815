import React, { useMemo } from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { TripDetails } from "../../lib/types";
import { DateTimePicker } from "../common/DateTimePicker";
import {
  ONBOARDING_MAX_DAYS,
  calculateMinDepartureDate,
  validateTripDuration,
} from "../../lib/utils/date-constraints";

interface DatesStepProps {
  formData: TripDetails;
  onUpdateFormData: (updates: Partial<TripDetails>) => void;
}

/**
 * Dates step component for the onboarding process
 * Allows users to select arrival and departure dates and times
 */
const DatesStep: React.FC<DatesStepProps> = ({
  formData,
  onUpdateFormData,
}) => {
  // Convert string dates from store to Date objects for UI components
  const arrivalDate = useMemo(
    () => (formData.startDate ? new Date(formData.startDate) : new Date()),
    [formData.startDate],
  );

  const departureDate = useMemo(
    () => (formData.endDate ? new Date(formData.endDate) : new Date()),
    [formData.endDate],
  );

  // Convert time strings to Date objects for time pickers
  const getTimeAsDate = (timeString: string) => {
    const date = new Date();
    const [hours, minutes] = timeString.split(":").map(Number);
    if (!isNaN(hours) && !isNaN(minutes)) {
      date.setHours(hours, minutes, 0, 0);
    }
    return date;
  };

  const arrivalTime = useMemo(
    () => getTimeAsDate(formData.arrivalTime),
    [formData.arrivalTime],
  );

  const departureTime = useMemo(
    () => getTimeAsDate(formData.departureTime),
    [formData.departureTime],
  );

  const wakeUpTime = useMemo(
    () => getTimeAsDate(formData.wakeUpTime),
    [formData.wakeUpTime],
  );

  const sleepTime = useMemo(
    () => getTimeAsDate(formData.sleepTime),
    [formData.sleepTime],
  );

  // Format time to HH:MM format
  const formatTimeToHHMM = (date: Date) => {
    return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  };

  // Calculate date constraints for onboarding (5-day limit)
  const dateConstraints = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const arrivalDateObj = new Date(formData.startDate);
    const minDepartureDate = calculateMinDepartureDate(arrivalDateObj);
    const maxDepartureDate = new Date(arrivalDateObj);
    // Since we include both arrival and departure dates, we add (ONBOARDING_MAX_DAYS - 1) days
    maxDepartureDate.setDate(arrivalDateObj.getDate() + ONBOARDING_MAX_DAYS - 1);

    return {
      minArrivalDate: today,
      minDepartureDate,
      maxDepartureDate,
    };
  }, [formData.startDate]);

  // Validate trip duration for onboarding
  const tripDurationValidation = useMemo(() => {
    return validateTripDuration(formData.startDate, formData.endDate, null, 'onboarding');
  }, [formData.startDate, formData.endDate]);

  return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>When are you traveling?</Text>
      <Text style={styles.stepDescription}>
        Select your travel dates and times. For your first trip, choose start and end dates that are at least 1 day apart.
      </Text>

      <View style={styles.dateCard}>
        <View style={styles.dateHeader}>
          <Ionicons name="airplane-outline" size={24} color="#666" />
          <Text style={styles.dateHeaderText}>Arrival</Text>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={arrivalDate}
              onChange={(date) => {
                // Format date as YYYY-MM-DD to avoid timezone issues
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateString = `${year}-${month}-${day}T00:00:00.000Z`;

                onUpdateFormData({
                  startDate: dateString,
                });
              }}
              mode="date"
              label="Arrival Date"
              icon="calendar-outline"
              minimumDate={dateConstraints.minArrivalDate}
            />
          </View>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={arrivalTime}
              onChange={(date) => {
                onUpdateFormData({
                  arrivalTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Arrival Time"
              icon="time-outline"
            />
          </View>
        </View>
      </View>

      <View style={[styles.dateCard, { marginTop: 20 }]}>
        <View style={styles.dateHeader}>
          <Ionicons
            name="airplane-outline"
            size={24}
            color="#666"
            style={{ transform: [{ rotate: "180deg" }] }}
          />
          <Text style={styles.dateHeaderText}>Departure</Text>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={departureDate}
              onChange={(date) => {
                // Format date as YYYY-MM-DD to avoid timezone issues
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateString = `${year}-${month}-${day}T00:00:00.000Z`;

                onUpdateFormData({
                  endDate: dateString,
                });
              }}
              mode="date"
              label="Departure Date"
              icon="calendar-outline"
              minimumDate={dateConstraints.minDepartureDate}
              maximumDate={dateConstraints.maxDepartureDate}
              error={tripDurationValidation?.isValid === false ? tripDurationValidation.error : undefined}
            />
          </View>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={departureTime}
              onChange={(date) => {
                onUpdateFormData({
                  departureTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Departure Time"
              icon="time-outline"
            />
          </View>
        </View>
        {tripDurationValidation && !tripDurationValidation.isValid && (
          <Text style={styles.errorText}>{tripDurationValidation.error}</Text>
        )}
      </View>

      <View style={[styles.dateCard, { marginTop: 20 }]}>
        <View style={styles.dateHeader}>
          <Ionicons name="time-outline" size={24} color="#666" />
          <Text style={styles.dateHeaderText}>Daily Schedule</Text>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={wakeUpTime}
              onChange={(date) => {
                onUpdateFormData({
                  wakeUpTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Wake Up Time"
              icon="sunny-outline"
              prefix="Wake up at:"
            />
          </View>
        </View>

        <View style={styles.dateRow}>
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={sleepTime}
              onChange={(date) => {
                onUpdateFormData({
                  sleepTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Sleep Time"
              icon="moon-outline"
              prefix="Sleep at:"
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
  },
  dateCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  dateHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  dateHeaderText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 8,
  },
  dateRow: {
    marginBottom: 12,
  },
  datePickerContainer: {
    width: "100%",
  },
  dateButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    backgroundColor: "#fff",
  },
  dateButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  dateButtonText: {
    fontSize: 16,
    color: "#333",
    marginLeft: 8,
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginTop: 8,
    fontWeight: "500",
  },
});

export default DatesStep;
