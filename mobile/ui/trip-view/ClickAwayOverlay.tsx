import React from "react";
import { TouchableWithoutFeedback, View, StyleSheet } from "react-native";

interface ClickAwayOverlayProps {
  onPress: () => void;
}

/**
 * Click away overlay component
 * Used to close pickers when clicking outside
 */
export const ClickAwayOverlay: React.FC<ClickAwayOverlayProps> = ({
  onPress,
}) => {
  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <View style={styles.overlay} />
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
});
