import React from "react";
import { StyleSheet, View } from "react-native";
import { DbTrip as Trip } from "../../lib/types";
import TripListView from "../trips/TripListView";
import { ClickAwayOverlay } from "./ClickAwayOverlay";

interface ListViewProps {
  trip: Trip;
  tripId: string;
  itineraryViewMode: "day" | "city";
  setItineraryViewMode: (mode: "day" | "city") => void;
  expandedCities: { [key: string]: boolean };
  toggleCity: (cityKey: string) => void;
  completedActivities: Set<string>;
  handleActivityCompletion: (tripId: string, activityId: string) => void;
  handleActivityUncompletion: (tripId: string, activityId: string) => void;
  showDayPicker: boolean;
  showCityPicker: boolean;
  showStatusPicker: boolean;
  closeAllPickers: () => void;
  header: React.ReactNode;
  top: number;
  bottom: number;
  left: number;
  right: number;
  onInsufficientBalance?: () => void;
  isGenerating?: boolean;
  setIsGenerating?: (isGenerating: boolean) => void;
}

/**
 * List view component for trip view screen
 */
export const ListView: React.FC<ListViewProps> = ({
  trip,
  tripId,
  itineraryViewMode,
  setItineraryViewMode,
  expandedCities,
  toggleCity,
  completedActivities,
  handleActivityCompletion,
  handleActivityUncompletion,
  showDayPicker,
  showCityPicker,
  showStatusPicker,
  closeAllPickers,
  header,
  top,
  bottom,
  left,
  right,
  onInsufficientBalance,
  isGenerating,
  setIsGenerating,
}) => {
  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: top,
          paddingBottom: bottom,
          paddingLeft: left,
          paddingRight: right,
        },
      ]}
    >
      {header}
      {(showDayPicker || showCityPicker || showStatusPicker) && (
        <ClickAwayOverlay onPress={closeAllPickers} />
      )}

      <View style={styles.content}>
        <TripListView
          trip={trip}
          itineraryViewMode={itineraryViewMode}
          setItineraryViewMode={setItineraryViewMode}
          expandedCities={expandedCities}
          toggleCity={toggleCity}
          completedActivities={completedActivities}
          onToggleCompletion={(activityId, isCompleted) => {
            if (isCompleted) {
              handleActivityUncompletion(tripId, activityId);
            } else {
              handleActivityCompletion(tripId, activityId);
            }
          }}
          onInsufficientBalance={onInsufficientBalance}
          isGenerating={isGenerating}
          setIsGenerating={setIsGenerating}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
  },
});
