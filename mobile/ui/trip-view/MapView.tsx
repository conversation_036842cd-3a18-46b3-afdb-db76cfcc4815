import React, { useState, useEffect } from "react";
import { View, StyleSheet, ActivityIndicator, Text } from "react-native";
import { DbTrip as Trip, Activity } from "../../lib/types";

// Import Region type from react-native-maps for type definitions
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};
import { Animated } from "react-native";
import TripMapView from "../map/TripMapView";
import TripViewFilters from "../trips/TripViewFilters";
import { TripService } from "../../lib/services/trip.service";
import { ClickAwayOverlay } from "./ClickAwayOverlay";

interface MapViewProps {
  trip: Trip;
  tripId: string;
  mapRegion: Region;
  selectedDay: number;
  selectedCity: string;
  selectedStatus: "all" | "completed" | "pending";
  showDayPicker: boolean;
  showCityPicker: boolean;
  showStatusPicker: boolean;
  completedActivities: Set<string>;
  activityInProgress: string | null;
  progressAnimation: Animated.Value;
  completionAnimation: Animated.Value;
  handleLongPress: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => (event: any) => void;
  handlePressOut: (activityId: string, isCompleted: boolean) => () => void;
  toggleActivityCompletion: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => Promise<void>;
  handleRegionChangeComplete: (region: Region) => void;
  setShowDayPicker: (show: boolean) => void;
  setShowCityPicker: (show: boolean) => void;
  setShowStatusPicker: (show: boolean) => void;
  onDaySelected: (day: number) => void;
  onCitySelected: (city: string) => void;
  onStatusSelected: (status: "all" | "completed" | "pending") => void;
  closeAllPickers: () => void;
  header: React.ReactNode;
  top: number;
}

/**
 * Map view component for trip view screen
 */
export const MapView: React.FC<MapViewProps> = ({
  trip,
  tripId,
  mapRegion,
  selectedDay,
  selectedCity,
  selectedStatus,
  showDayPicker,
  showCityPicker,
  showStatusPicker,
  completedActivities,
  activityInProgress,
  progressAnimation,
  completionAnimation,
  handleLongPress,
  handlePressOut,
  toggleActivityCompletion,
  handleRegionChangeComplete,
  setShowDayPicker,
  setShowCityPicker,
  setShowStatusPicker,
  onDaySelected,
  onCitySelected,
  onStatusSelected,
  closeAllPickers,
  header,
  top,
}) => {
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([]);
  const [isLoadingActivities, setIsLoadingActivities] = useState(false);
  const tripService = TripService.getInstance();

  // Fetch filtered activities from backend when filters change
  useEffect(() => {
    const fetchFilteredActivities = async () => {
      if (!trip || !tripId) return;

      setIsLoadingActivities(true);
      try {
        const activities = await tripService.fetchFilteredActivities(tripId, {
          day: selectedDay,
          city: selectedCity,
          status: selectedStatus,
        });
        setFilteredActivities(activities);
      } catch (error) {
        console.error("Error fetching filtered activities:", error);
        // Fallback to empty array on error
        setFilteredActivities([]);
      } finally {
        setIsLoadingActivities(false);
      }
    };

    fetchFilteredActivities();
  }, [tripId, selectedDay, selectedCity, selectedStatus, trip]);

  return (
    <View style={[styles.container, { paddingTop: top }]}>
      {header}
      {(showDayPicker || showCityPicker || showStatusPicker) && (
        <ClickAwayOverlay onPress={closeAllPickers} />
      )}

      <TripViewFilters
        trip={trip}
        selectedDay={selectedDay}
        selectedCity={selectedCity}
        selectedStatus={selectedStatus}
        showDayPicker={showDayPicker}
        showCityPicker={showCityPicker}
        showStatusPicker={showStatusPicker}
        setShowDayPicker={setShowDayPicker}
        setShowCityPicker={setShowCityPicker}
        setShowStatusPicker={setShowStatusPicker}
        onDaySelected={onDaySelected}
        onCitySelected={onCitySelected}
        onStatusSelected={onStatusSelected}
      />

      <TripMapView
        mapRegion={mapRegion}
        onRegionChangeComplete={handleRegionChangeComplete}
        activities={filteredActivities}
        completedActivities={completedActivities}
        activityInProgress={activityInProgress}
        progressAnimation={progressAnimation}
        completionAnimation={completionAnimation}
        tripId={tripId}
        selectedDay={selectedDay}
        handleLongPress={handleLongPress}
        handlePressOut={handlePressOut}
        toggleActivityCompletion={toggleActivityCompletion}
      />

      {/* Loading overlay for activities */}
      {isLoadingActivities && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Loading activities...</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
});
