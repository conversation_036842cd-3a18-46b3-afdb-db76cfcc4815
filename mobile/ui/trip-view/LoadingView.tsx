import React from "react";
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  SafeAreaView,
} from "react-native";
import ScreenHeader from "../common/ScreenHeader";

interface LoadingViewProps {
  header: React.ReactNode;
}

/**
 * Loading view component for trip view screen
 */
export const LoadingView: React.FC<LoadingViewProps> = ({ header }) => {
  return (
    <SafeAreaView style={styles.container}>
      {header}
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading trip...</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
});
