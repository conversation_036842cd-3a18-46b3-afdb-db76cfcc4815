import { Ionicons } from "@expo/vector-icons";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Platform, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import { Product } from "react-native-iap";
import { ProductConfig } from "../../lib/hooks/usePayments";
import { AvailableSoonBadge } from "./AvailableSoonBadge";

// Define DayRefillPackage interface locally since the old service is removed
interface DayRefillPackage {
  id: string;
  productId: string;
  name: string;
  days: number;
  price: number;
  type: "day_refill";
}

interface DayRefillSelectorProps {
  packages: DayRefillPackage[];
  products: (Product & DayRefillPackage)[];
  selectedPackageId: string | null;
  onSelect: (id: string, days: number) => void;
}


interface BackendDayRefillSelectorProps {
  onSelect: (id: string, days: number, price: number) => void;
  productConfigs: ProductConfig[];
  selectedPackageId: string | null;
  availableProducts?: Product[]; // Available products from app store
}

/**
 * Day refill selector component
 * Displays an input field with plus/minus buttons for selecting days
 */
export const DayRefillSelector: React.FC<DayRefillSelectorProps> = ({
  packages,
  products,
  selectedPackageId,
  onSelect,
}) => {
  const [selectedDays, setSelectedDays] = useState(10);

  // Base package (first one - 10 days for $1.99)
  const basePackage = packages[0];
  const baseProduct = products.find(p => p.id === basePackage?.id);

  // Calculate price based on selected days (each 10 days = $1.99)
  const pricePerTenDays = 1.99;
  const calculatedPrice = (selectedDays / 10) * pricePerTenDays;

  // Find the appropriate package based on selected days or create a virtual one
  const getPackageForDays = (days: number) => {
    // Try to find exact match first
    const exactMatch = packages.find(pkg => pkg.days === days);
    if (exactMatch) return exactMatch;

    // Create virtual package for intermediate values
    return {
      id: `refill_${days}`,
      productId: `com.itrip.days.refill_${days}`,
      name: `${days} Days`,
      days: days,
      price: calculatedPrice,
      type: "day_refill" as const,
    };
  };

  const currentPackage = getPackageForDays(selectedDays);
  // For virtual packages, use the base product but with updated info
  const currentProduct = products.find(p => p.id === currentPackage?.id) ||
    (baseProduct ? {
      ...baseProduct,
      ...currentPackage,
      localizedPrice: `$${calculatedPrice.toFixed(2)}`,
      price: calculatedPrice.toString(),
    } : undefined);

  const isSelected = selectedPackageId === currentPackage?.id;

  // Update selection when days change
  useEffect(() => {
    if (currentPackage) {
      onSelect(currentPackage.id, selectedDays);
    }
  }, [selectedDays, onSelect]);

  const handleDecrease = () => {
    if (selectedDays > 10) {
      setSelectedDays(prev => Math.max(10, prev - 5));
    }
  };

  const handleIncrease = () => {
    if (selectedDays < 30) {
      setSelectedDays(prev => Math.min(30, prev + 5));
    }
  };

  const handleInputChange = (text: string) => {
    const days = parseInt(text) || 0;
    if (days >= 10 && days <= 30) {
      // Round to nearest 5, but minimum 10
      const roundedDays = Math.max(10, Math.round(days / 5) * 5);
      setSelectedDays(roundedDays);
    }
  };

  if (!basePackage) return null;

  return (
    <View
      style={[
        styles.packageCard,
        isSelected && styles.selectedPackage,
        !baseProduct && styles.unavailablePackage,
      ]}
    >
      <View style={styles.packageHeader}>
        <Text style={styles.packageName}>Day Refill</Text>
        <View style={styles.priceContainer}>
          {!baseProduct ? (
            <Text style={styles.unavailableText}>Unavailable</Text>
          ) : (
            <Text style={styles.packagePrice}>${calculatedPrice.toFixed(2)}</Text>
          )}
        </View>
      </View>

      <View style={styles.packageDetails}>
        <View style={styles.daysAmount}>
          <Ionicons name="add-circle" size={20} color="#FF9800" />
          <Text style={styles.daysText}>
            {selectedDays} additional days
          </Text>
        </View>

        {/* Days Selector */}
        <View style={styles.daysSelector}>
          <TouchableOpacity
            style={[styles.selectorButton, selectedDays <= 10 && styles.disabledButton]}
            onPress={handleDecrease}
            disabled={selectedDays <= 10}
          >
            <Ionicons name="remove" size={20} color={selectedDays <= 10 ? "#ccc" : "#FF9800"} />
          </TouchableOpacity>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.daysInput}
              value={selectedDays.toString()}
              onChangeText={handleInputChange}
              keyboardType="numeric"
              maxLength={2}
              textAlign="center"
            />
            <Text style={styles.daysLabel}>days</Text>
          </View>

          <TouchableOpacity
            style={[styles.selectorButton, selectedDays >= 30 && styles.disabledButton]}
            onPress={handleIncrease}
            disabled={selectedDays >= 30}
          >
            <Ionicons name="add" size={20} color={selectedDays >= 30 ? "#ccc" : "#FF9800"} />
          </TouchableOpacity>
        </View>

        <View style={styles.feature}>
          <Ionicons name="information-circle" size={16} color="#666" />
          <Text style={styles.featureText}>One-time purchase</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="information-circle" size={16} color="#666" />
          <Text style={styles.featureText}>Expires at plan cycle end</Text>
        </View>
      </View>

      <View style={styles.selectionIndicator}>
        {isSelected ? (
          <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
        ) : (
          <View style={styles.unselectedCircle} />
        )}
      </View>
    </View>
  );
};

/**
 * Backend-powered Day refill selector component
 * Fetches pricing from backend and enforces minimum 5 days
 */
export const BackendDayRefillSelector: React.FC<BackendDayRefillSelectorProps> = ({
  onSelect,
  productConfigs: backendProducts,
  selectedPackageId,
  availableProducts = [],
}) => {
  const [selectedDays, setSelectedDays] = useState(10);

  // Create virtual package for current selection - memoize to prevent recreation
  const currentPackage = React.useMemo(
    () => backendProducts
      .find(product => product.days === selectedDays && product.type === 'consumable'),
    [selectedDays]);

  const currentPrice = useMemo(() => {
    return currentPackage?.price.usd || 0;
  }, [currentPackage])

  // Check if the current package is available in the app store
  const isAvailable = useMemo(() => {
    if (!currentPackage) return false;
    const platform = Platform.OS as 'ios' | 'android';
    const productId = currentPackage.productId[platform];
    return productId ? availableProducts.some(product => product.productId === productId) : false;
  }, [currentPackage, availableProducts]);

  const isSelected = selectedPackageId === currentPackage?.productId.ios;

  // Only call onSelect when user explicitly changes the selection, not on every render
  const handleDaysChange = useCallback((newDays: number) => {
    if (!isAvailable) return; // Don't allow selection if not available

    const newPackage = backendProducts.find(product => product.days === newDays && product.type === 'consumable');
    if (!newPackage) return;
    const packageId = newPackage.productId.ios;
    const newPrice = newPackage?.price.usd || 0;

    setSelectedDays(newDays);
    onSelect(packageId!, newDays, newPrice);
  }, [onSelect, backendProducts, isAvailable]); // Include isAvailable to prevent selection when unavailable

  const handleDecrease = () => {
    if (!isAvailable) return; // Don't allow interaction if not available
    if (selectedDays > 5) {
      const newDays = Math.max(5, selectedDays - 5);
      handleDaysChange(newDays);
    }
  };

  const handleIncrease = () => {
    if (!isAvailable) return; // Don't allow interaction if not available
    if (selectedDays < 30) {
      const newDays = Math.min(30, selectedDays + 5);
      handleDaysChange(newDays);
    }
  };

  const handleInputChange = (text: string) => {
    if (!isAvailable) return; // Don't allow interaction if not available
    const days = parseInt(text) || 0;
    if (days >= 5 && days <= 30) {
      // Round to nearest 5, but minimum 5
      const roundedDays = Math.max(5, Math.round(days / 5) * 5);
      handleDaysChange(roundedDays);
    }
  };


  return (
    <View
      style={[
        styles.packageCard,
        isSelected && styles.selectedPackage,
        !isAvailable && styles.unavailablePackage,
      ]}
    >
      {!isAvailable && (
        <AvailableSoonBadge />
      )}

      <View style={styles.packageHeader}>
        <Text style={styles.packageName}>Day Refill</Text>
        <View style={styles.priceContainer}>
          {!isAvailable ? (
            <Text style={styles.unavailableText}>Coming Soon</Text>
          ) : (
            <Text style={styles.packagePrice}>${currentPrice.toFixed(2)}</Text>
          )}
        </View>
      </View>

      <View style={styles.packageDetails}>
        <View style={styles.daysAmount}>
          <Ionicons name="add-circle" size={20} color="#FF9800" />
          <Text style={styles.daysText}>
            {selectedDays} additional days
          </Text>
        </View>

        {/* Days Selector */}
        <View style={styles.daysSelector}>
          <TouchableOpacity
            style={[
              styles.selectorButton,
              (selectedDays <= 5 || !isAvailable) && styles.disabledButton
            ]}
            onPress={handleDecrease}
            disabled={selectedDays <= 5 || !isAvailable}
          >
            <Ionicons
              name="remove"
              size={20}
              color={(selectedDays <= 5 || !isAvailable) ? "#ccc" : "#FF9800"}
            />
          </TouchableOpacity>

          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.daysInput, !isAvailable && styles.disabledInput]}
              value={selectedDays.toString()}
              onChangeText={handleInputChange}
              keyboardType="numeric"
              maxLength={2}
              editable={isAvailable}
            />
            <Text style={styles.daysLabel}>days</Text>
          </View>

          <TouchableOpacity
            style={[
              styles.selectorButton,
              (selectedDays >= 30 || !isAvailable) && styles.disabledButton
            ]}
            onPress={handleIncrease}
            disabled={selectedDays >= 30 || !isAvailable}
          >
            <Ionicons
              name="add"
              size={20}
              color={(selectedDays >= 30 || !isAvailable) ? "#ccc" : "#FF9800"}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.feature}>
          <Ionicons name="information-circle" size={16} color="#666" />
          <Text style={styles.featureText}>One-time purchase</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="information-circle" size={16} color="#666" />
          <Text style={styles.featureText}>Expires at plan cycle end</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="information-circle" size={16} color="#666" />
          <Text style={styles.featureText}>Minimum 5 days, increments of 5</Text>
        </View>
      </View>

      <View style={styles.selectionIndicator}>
        {isSelected ? (
          <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
        ) : (
          <View style={styles.unselectedCircle} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  packageCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    position: "relative",
  },
  selectedPackage: {
    borderColor: "#FF9800",
    borderWidth: 2,
    shadowColor: "#FF9800",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  unavailablePackage: {
    opacity: 0.6,
    backgroundColor: "#f5f5f5",
  },
  packageHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  packageName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  packagePrice: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
  },
  unavailableText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#999",
    fontStyle: "italic",
  },
  packageDetails: {
    marginBottom: 8,
  },
  daysAmount: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  daysText: {
    fontSize: 16,
    color: "#666",
    marginLeft: 8,
    fontWeight: "500",
  },
  daysSelector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
    paddingVertical: 8,
  },
  selectorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f8f9fa",
    borderWidth: 1,
    borderColor: "#FF9800",
    justifyContent: "center",
    alignItems: "center",
  },
  disabledButton: {
    borderColor: "#ccc",
    backgroundColor: "#f5f5f5",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  daysInput: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    minWidth: 30,
    textAlign: "center",
  },
  disabledInput: {
    color: "#999",
    backgroundColor: "#f5f5f5",
  },
  daysLabel: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  unavailableText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#999",
    fontStyle: "italic",
  },
  feature: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  featureText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  selectionIndicator: {
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  unselectedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#ccc",
  },
  loadingCard: {
    justifyContent: "center",
    alignItems: "center",
    minHeight: 120,
  },
  loadingText: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
  },
  errorCard: {
    justifyContent: "center",
    alignItems: "center",
    minHeight: 120,
  },
  errorText: {
    fontSize: 14,
    color: "#FF5722",
    marginTop: 8,
    textAlign: "center",
  },
  fallbackPriceText: {
    fontSize: 12,
    color: "#FF9800",
    marginLeft: 4,
    fontWeight: "bold",
  },
});
