import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { useProductsConfig } from "../../lib/hooks/use-products-config";
import { DaysBalance } from "../../lib/services/days-balance.service";

interface CurrentPlanCardProps {
  balance: DaysBalance;
}

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "numeric",
  });
};

// Get plan color
const getPlanColor = (plan: string) => {
  switch (plan) {
    case "free":
      return "#666";
    case "pro":
      return "#2196F3";
    case "premium":
      return "#9C27B0";
    default:
      return "#666";
  }
};

// Get status color
const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "#4CAF50";
    case "cancelled":
      return "#FF9800";
    case "expired":
      return "#F44336";
    default:
      return "#666";
  }
};

// Get status display text
const getStatusDisplayText = (status: string) => {
  switch (status) {
    case "active":
      return "Active";
    case "cancelled":
      return "Cancelled";
    case "expired":
      return "Expired";
    case "pending":
      return "Pending";
    default:
      return "Unknown";
  }
};

/**
 * Current plan card component
 * Displays the user's current subscription plan with billing information
 */
export const CurrentPlanCard: React.FC<CurrentPlanCardProps> = ({
  balance,
}) => {
  const products = useProductsConfig();

  const planColor = getPlanColor(balance.currentPlan);
  const statusColor = getStatusColor(balance.subscriptionStatus);

  const getPlanDisplayName = (plan: string) => {
    const product = products?.find(p => p.id === plan);
    return product ? product.name : "Unknown Plan";
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.planInfo}>
          <View style={[styles.planBadge, { backgroundColor: planColor }]}>
            <Ionicons name="star" size={16} color="#FFF" />
          </View>
          <View style={styles.planDetails}>
            <Text style={styles.planName}>
              {getPlanDisplayName(balance.currentPlan)}
            </Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
              <Text style={[styles.statusText, { color: statusColor }]}>
                {getStatusDisplayText(balance.subscriptionStatus)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.content}>
        {/* Pending Plan Change Notification */}
        {balance.pendingPlanChange && (
          <View style={styles.pendingChangeSection}>
            <View style={styles.pendingChangeHeader}>
              <Ionicons name="information-circle" size={20} color="#FF9800" />
              <Text style={styles.pendingChangeTitle}>Plan Change Scheduled</Text>
            </View>
            <Text style={styles.pendingChangeText}>
              Your plan will change to {getPlanDisplayName(balance.pendingPlanChange.newPlan)} on{" "}
              {formatDate(balance.pendingPlanChange.effectiveDate)}. Your current days will be preserved.
            </Text>
          </View>
        )}

        {/* Days Information */}
        <View style={styles.daysSection}>
          <View style={styles.daysInfo}>
            <Ionicons name="calendar" size={20} color="#2196F3" />
            <Text style={styles.daysLabel}>Available Days</Text>
          </View>
          <Text style={styles.daysValue}>{balance.totalDays}</Text>
        </View>

        {/* Billing Information */}
        <View style={styles.billingSection}>
          <View style={styles.billingRow}>
            <View style={styles.billingInfo}>
              <Ionicons name="refresh" size={16} color="#666" />
              <Text style={styles.billingLabel}>Next Billing</Text>
            </View>
            <Text style={styles.billingValue}>
              {balance.nextBillingDate
                ? formatDate(balance.nextBillingDate)
                : "N/A"}
            </Text>
          </View>

          {balance.subscriptionEndDate && (
            <View style={styles.billingRow}>
              <View style={styles.billingInfo}>
                <Ionicons name="time" size={16} color="#666" />
                <Text style={styles.billingLabel}>Plan Expires</Text>
              </View>
              <Text style={styles.billingValue}>
                {formatDate(balance.subscriptionEndDate)}
              </Text>
            </View>
          )}

          {balance.subscriptionStartDate && (
            <View style={styles.billingRow}>
              <View style={styles.billingInfo}>
                <Ionicons name="play" size={16} color="#666" />
                <Text style={styles.billingLabel}>Started</Text>
              </View>
              <Text style={styles.billingValue}>
                {formatDate(balance.subscriptionStartDate)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  planInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  planBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  planDetails: {
    flex: 1,
  },
  planName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },

  content: {
    gap: 16,
  },
  pendingChangeSection: {
    padding: 12,
    backgroundColor: "#FFF8E1",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FFE0B2",
  },
  pendingChangeHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  pendingChangeTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FF9800",
    marginLeft: 8,
  },
  pendingChangeText: {
    fontSize: 14,
    color: "#E65100",
    lineHeight: 20,
  },
  daysSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
  },
  daysInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  daysLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginLeft: 8,
  },
  daysValue: {
    fontSize: 24,
    fontWeight: "700",
    color: "#2196F3",
  },
  billingSection: {
    gap: 8,
  },
  billingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 4,
  },
  billingInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  billingLabel: {
    fontSize: 14,
    color: "#666",
    marginLeft: 6,
  },
  billingValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
});
