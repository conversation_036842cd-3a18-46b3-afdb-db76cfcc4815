import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface AvailableSoonBadgeProps {
  style?: any;
}

/**
 * Available Soon badge component
 * Displays when a subscription plan is not yet available in app stores
 */
export const AvailableSoonBadge: React.FC<AvailableSoonBadgeProps> = ({
  style,
}) => {
  return (
    <View style={[styles.badge, style]}>
      <Ionicons name="time-outline" size={12} color="#666" />
      <Text style={styles.text}>Available Soon</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    position: "absolute",
    top: -10,
    right: 16,
    backgroundColor: "#f5f5f5",
    borderColor: "#ddd",
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  text: {
    color: "#666",
    fontSize: 12,
    fontWeight: "600",
  },
});
