import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Modal,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { MMKV } from "react-native-mmkv";
import * as Haptics from "expo-haptics";

// Storage for tutorial state
const storage = new MMKV();
const TUTORIAL_STORAGE_KEY = "app_tutorials";

// Tutorial step types
export type TooltipPosition =
  | "top"
  | "top-left"
  | "top-center"
  | "top-right"
  | "bottom"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right"
  | "left"
  | "left-top"
  | "left-center"
  | "left-bottom"
  | "right"
  | "right-top"
  | "right-center"
  | "right-bottom";

export type TutorialStep = {
  id: string;
  targetRef: React.RefObject<View | null>;
  title: string;
  description: string;
  placement?: TooltipPosition;
  order: number;
};

// Layout measurement type
type LayoutMeasurement = {
  x: number;
  y: number;
  width: number;
  height: number;
  pageX: number;
  pageY: number;
};

// Tutorial context type
type TutorialContextType = {
  registerTutorial: (tutorialId: string, steps: TutorialStep[]) => void;
  startTutorial: (tutorialId: string) => void;
  isTutorialActive: (tutorialId: string) => boolean;
  markTutorialComplete: (tutorialId: string) => void;
  resetTutorial: (tutorialId: string) => void;
};

// Create context
export const TutorialContext = React.createContext<TutorialContextType>({
  registerTutorial: () => {},
  startTutorial: () => {},
  isTutorialActive: () => false,
  markTutorialComplete: () => {},
  resetTutorial: () => {},
});

// Tutorial provider component
export const TutorialProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [tutorials, setTutorials] = useState<Record<string, TutorialStep[]>>(
    {},
  );
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [tooltipVisible, setTooltipVisible] = useState<boolean>(false);

  // Load completed tutorials from storage
  const getCompletedTutorials = (): Record<string, boolean> => {
    const stored = storage.getString(TUTORIAL_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  };

  // Save completed tutorial to storage
  const saveCompletedTutorial = (tutorialId: string) => {
    const completed = getCompletedTutorials();
    completed[tutorialId] = true;
    storage.set(TUTORIAL_STORAGE_KEY, JSON.stringify(completed));
  };

  // Check if tutorial has been completed
  const isTutorialCompleted = (tutorialId: string): boolean => {
    const completed = getCompletedTutorials();
    return !!completed[tutorialId];
  };

  // Register a new tutorial
  const registerTutorial = React.useCallback(
    (tutorialId: string, steps: TutorialStep[]) => {
      setTutorials((prev) => {
        // Check if tutorial already exists
        if (prev[tutorialId]) {
          // Tutorial already exists, no need to update
          return prev;
        }

        // Add new tutorial
        return {
          ...prev,
          [tutorialId]: steps.sort((a, b) => a.order - b.order),
        };
      });
    },
    [],
  );

  // Start a tutorial if it hasn't been completed
  const startTutorial = React.useCallback((tutorialId: string) => {
    if (isTutorialCompleted(tutorialId)) return;

    setActiveTutorial(tutorialId);
    setCurrentStep(0);
    setTooltipVisible(true);

    // Provide haptic feedback when tutorial starts
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }, []);

  // Check if a tutorial is currently active
  const isTutorialActive = React.useCallback(
    (tutorialId: string): boolean => {
      return activeTutorial === tutorialId;
    },
    [activeTutorial],
  );

  // Mark a tutorial as complete
  const markTutorialComplete = React.useCallback((tutorialId: string) => {
    saveCompletedTutorial(tutorialId);
    setActiveTutorial(null);
    setTooltipVisible(false);
  }, []);

  // Reset a tutorial (mark as not completed)
  const resetTutorial = React.useCallback((tutorialId: string) => {
    const completed = getCompletedTutorials();
    delete completed[tutorialId];
    storage.set(TUTORIAL_STORAGE_KEY, JSON.stringify(completed));
  }, []);

  // Handle next step in tutorial
  const handleNextStep = React.useCallback(() => {
    if (!activeTutorial) return;

    const steps = tutorials[activeTutorial];
    if (!steps) return;

    if (currentStep < steps.length - 1) {
      // Provide haptic feedback when moving to next step
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setCurrentStep((prev) => prev + 1);
    } else {
      // Tutorial complete
      markTutorialComplete(activeTutorial);
    }
  }, [activeTutorial, currentStep, markTutorialComplete, tutorials]);

  // Handle skip tutorial
  const handleSkipTutorial = React.useCallback(() => {
    if (!activeTutorial) return;

    // Provide haptic feedback when skipping
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    markTutorialComplete(activeTutorial);
  }, [activeTutorial, markTutorialComplete]);

  // Measure the position of a component
  const measureComponent = (
    ref: React.RefObject<View | null>,
  ): Promise<LayoutMeasurement | null> => {
    return new Promise((resolve) => {
      if (!ref.current) {
        resolve(null);
        return;
      }

      // Get measurements using the newer method
      ref.current.measure((x, y, width, height, pageX, pageY) => {
        resolve({ x, y, width, height, pageX, pageY });
      });
    });
  };

  // Calculate tooltip position
  const [targetMeasurements, setTargetMeasurements] =
    useState<LayoutMeasurement | null>(null);

  // Update measurements when the current step changes
  useEffect(() => {
    if (!activeTutorial || !tooltipVisible) return;

    const steps = tutorials[activeTutorial];
    if (!steps || steps.length === 0) return;

    const currentTutorialStep = steps[currentStep];
    if (!currentTutorialStep || !currentTutorialStep.targetRef.current) return;

    // Measure the target component
    const updateMeasurements = async () => {
      const measurements = await measureComponent(
        currentTutorialStep.targetRef,
      );
      setTargetMeasurements(measurements);
    };

    // Add a small delay to ensure the component is rendered
    const timer = setTimeout(() => {
      updateMeasurements();
    }, 100);

    return () => clearTimeout(timer);
  }, [activeTutorial, tooltipVisible, tutorials, currentStep]);

  // Render the current tooltip
  const renderCurrentTooltip = React.useCallback(() => {
    if (!activeTutorial || !tooltipVisible || !targetMeasurements) return null;

    const steps = tutorials[activeTutorial];
    if (!steps || steps.length === 0) return null;

    const currentTutorialStep = steps[currentStep];
    if (!currentTutorialStep) return null;

    // Calculate tooltip position based on placement
    const placement = currentTutorialStep.placement || "bottom";
    const { width: screenWidth, height: screenHeight } =
      Dimensions.get("window");
    const tooltipWidth = Math.min(300, screenWidth * 0.8);
    const tooltipHeight = 180; // Approximate height
    const arrowSize = 10;

    // Default position (centered on screen)
    let tooltipX = (screenWidth - tooltipWidth) / 2;
    let tooltipY = (screenHeight - tooltipHeight) / 2;
    let arrowX = tooltipWidth / 2 - arrowSize;
    let arrowY = 0;
    let arrowPosition: "top" | "bottom" | "left" | "right" = "top";

    // Position based on target measurements
    if (targetMeasurements) {
      const { pageX, pageY, width, height } = targetMeasurements;

      // Extract the base position and alignment from the placement
      const [basePosition, alignment = "center"] = placement.includes("-")
        ? (placement.split("-") as [any, any])
        : [placement, "center"];

      // Calculate positions based on base position (top, bottom, left, right)
      switch (basePosition) {
        case "top":
          // Base positioning for top
          tooltipY = pageY - tooltipHeight - arrowSize;
          // Fix: Position the arrow to connect perfectly with the tooltip bubble
          arrowY = tooltipHeight - arrowSize - 2; // Adjusted to ensure connection with the tooltip
          arrowPosition = "bottom";

          // Horizontal alignment for top position
          switch (alignment) {
            case "left":
              tooltipX = pageX;
              arrowX = Math.min(width / 2, tooltipWidth / 4) - arrowSize;
              break;
            case "right":
              tooltipX = pageX + width - tooltipWidth;
              arrowX =
                tooltipWidth -
                Math.min(width / 2, tooltipWidth / 4) -
                arrowSize;
              break;
            case "center":
            default:
              tooltipX = pageX + width / 2 - tooltipWidth / 2;
              arrowX = tooltipWidth / 2 - arrowSize;
              break;
          }
          break;

        case "bottom":
          // Base positioning for bottom
          tooltipY = pageY + height + arrowSize;
          arrowY = -arrowSize * 2;
          arrowPosition = "top";

          // Horizontal alignment for bottom position
          switch (alignment) {
            case "left":
              tooltipX = pageX;
              arrowX = Math.min(width / 2, tooltipWidth / 4) - arrowSize;
              break;
            case "right":
              tooltipX = pageX + width - tooltipWidth;
              arrowX =
                tooltipWidth -
                Math.min(width / 2, tooltipWidth / 4) -
                arrowSize;
              break;
            case "center":
            default:
              tooltipX = pageX + width / 2 - tooltipWidth / 2;
              arrowX = tooltipWidth / 2 - arrowSize;
              break;
          }
          break;

        case "left":
          // Base positioning for left
          tooltipX = pageX - tooltipWidth - arrowSize;
          arrowX = tooltipWidth;
          arrowPosition = "right";

          // Vertical alignment for left position
          switch (alignment) {
            case "top":
              tooltipY = pageY;
              arrowY = Math.min(height / 2, tooltipHeight / 4) - arrowSize;
              break;
            case "bottom":
              tooltipY = pageY + height - tooltipHeight;
              arrowY =
                tooltipHeight -
                Math.min(height / 2, tooltipHeight / 4) -
                arrowSize;
              break;
            case "center":
            default:
              tooltipY = pageY + height / 2 - tooltipHeight / 2;
              arrowY = tooltipHeight / 2 - arrowSize;
              break;
          }
          break;

        case "right":
          // Base positioning for right
          tooltipX = pageX + width + arrowSize;
          arrowX = -arrowSize * 2;
          arrowPosition = "left";

          // Vertical alignment for right position
          switch (alignment) {
            case "top":
              tooltipY = pageY;
              arrowY = Math.min(height / 2, tooltipHeight / 4) - arrowSize;
              break;
            case "bottom":
              tooltipY = pageY + height - tooltipHeight;
              arrowY =
                tooltipHeight -
                Math.min(height / 2, tooltipHeight / 4) -
                arrowSize;
              break;
            case "center":
            default:
              tooltipY = pageY + height / 2 - tooltipHeight / 2;
              arrowY = tooltipHeight / 2 - arrowSize;
              break;
          }
          break;
      }

      // Ensure tooltip stays within screen bounds
      tooltipX = Math.max(
        10,
        Math.min(screenWidth - tooltipWidth - 10, tooltipX),
      );
      tooltipY = Math.max(
        10,
        Math.min(screenHeight - tooltipHeight - 10, tooltipY),
      );
    }

    // Highlight effect for the target
    const highlightPosition = targetMeasurements
      ? {
          left: targetMeasurements.pageX - 5,
          top: targetMeasurements.pageY - 5,
          width: targetMeasurements.width + 10,
          height: targetMeasurements.height + 10,
        }
      : null;

    return (
      <Modal transparent visible={tooltipVisible} animationType="fade">
        <View style={styles.modalOverlay}>
          {/* Highlight around the target element */}
          {highlightPosition && (
            <View style={[styles.highlight, highlightPosition]} />
          )}

          {/* Tooltip */}
          <View
            style={[
              styles.tooltip,
              { left: tooltipX, top: tooltipY, width: tooltipWidth },
            ]}
          >
            {/* Arrow */}
            <View
              style={[
                styles.arrow,
                arrowPosition === "top" && styles.arrowTop,
                arrowPosition === "bottom" && styles.arrowBottom,
                arrowPosition === "left" && styles.arrowLeft,
                arrowPosition === "right" && styles.arrowRight,
                {
                  left: arrowX,
                  top: arrowY,
                  // Ensure the arrow is properly connected to the tooltip for all positions
                  ...(arrowPosition === "top" && {
                    transform: [{ translateY: 0 }],
                  }),
                  ...(arrowPosition === "bottom" && {
                    transform: [{ translateY: 0 }],
                  }),
                  ...(arrowPosition === "left" && {
                    transform: [{ translateX: 0 }],
                  }),
                  ...(arrowPosition === "right" && {
                    transform: [{ translateX: 0 }],
                  }),
                },
              ]}
            />

            {/* Content */}
            <View style={styles.tooltipContent}>
              <Text style={styles.tooltipTitle}>
                {currentTutorialStep.title}
              </Text>
              <Text style={styles.tooltipDescription}>
                {currentTutorialStep.description}
              </Text>
              <View style={styles.tooltipFooter}>
                <TouchableOpacity
                  onPress={handleSkipTutorial}
                  style={styles.skipButton}
                >
                  <Text style={styles.skipButtonText}>Skip</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleNextStep}
                  style={styles.nextButton}
                >
                  <Text style={styles.nextButtonText}>
                    {currentStep < steps.length - 1 ? "Next" : "Got it"}
                  </Text>
                  {currentStep < steps.length - 1 && (
                    <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  }, [
    activeTutorial,
    tooltipVisible,
    tutorials,
    currentStep,
    targetMeasurements,
    handleSkipTutorial,
    handleNextStep,
  ]);

  return (
    <TutorialContext.Provider
      value={{
        registerTutorial,
        startTutorial,
        isTutorialActive,
        markTutorialComplete,
        resetTutorial,
      }}
    >
      {children}
      {renderCurrentTooltip()}
    </TutorialContext.Provider>
  );
};

// Custom hook to use the tutorial context
export const useTutorial = () => {
  const context = React.useContext(TutorialContext);
  if (!context) {
    throw new Error("useTutorial must be used within a TutorialProvider");
  }
  return context;
};

// Styles
const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  highlight: {
    position: "absolute",
    borderRadius: 4,
    borderWidth: 2,
    borderColor: "#007AFF",
    backgroundColor: "rgba(0, 122, 255, 0.1)",
    zIndex: 1,
  },
  tooltip: {
    position: "absolute",
    borderRadius: 8,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 2,
    // Ensure no gap between tooltip and arrow
    overflow: "visible",
  },
  arrow: {
    position: "absolute",
    width: 0,
    height: 0,
    borderStyle: "solid",
    borderWidth: 10,
    zIndex: 3, // Ensure arrow is above tooltip content
  },
  arrowTop: {
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: "#FFFFFF",
    borderTopColor: "transparent",
    borderBottomWidth: 10,
    // Add a slight overlap to ensure connection with the tooltip
    marginBottom: -2,
  },
  arrowBottom: {
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderTopColor: "#FFFFFF",
    borderBottomColor: "transparent",
    borderTopWidth: 10,
    // Add a more significant overlap to ensure connection with the tooltip
    marginTop: -10,
  },
  arrowLeft: {
    borderTopColor: "transparent",
    borderBottomColor: "transparent",
    borderRightColor: "#FFFFFF",
    borderLeftColor: "transparent",
    borderRightWidth: 10,
    // Add a slight overlap to ensure connection with the tooltip
    marginRight: -2,
  },
  arrowRight: {
    borderTopColor: "transparent",
    borderBottomColor: "transparent",
    borderLeftColor: "#FFFFFF",
    borderRightColor: "transparent",
    borderLeftWidth: 10,
    // Add a slight overlap to ensure connection with the tooltip
    marginLeft: -2,
  },
  tooltipContent: {
    padding: 16,
  },
  tooltipTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  tooltipDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
    lineHeight: 20,
  },
  tooltipFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
  },
  skipButton: {
    padding: 8,
  },
  skipButtonText: {
    color: "#999",
    fontSize: 14,
  },
  nextButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  nextButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
    marginRight: 4,
  },
});

export default TutorialProvider;
