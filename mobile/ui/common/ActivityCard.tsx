import { Text, View } from "react-native";
import {
  DayActivityCard,
  CityActivityCard,
  TimelineActivityCard,
  styles,
  ActivityCardProps,
  ActivityCardContext
} from "./activity-card";

// Re-export types for external use
export type { ActivityCardContext, ActivityCardProps } from "./activity-card";





export default function ActivityCard({
  activity,
  context,
  isCompleted = false,
  onToggleCompletion,
  onPress,
  dayNumber,
  date,
  isLast = false,
  index: _index = 0,
  onEdit: _onEdit,
  onRemove: _onRemove,
  drag: _drag,
  isActive: _isActive = false,
}: ActivityCardProps) {
  // Validate activity object
  if (!activity || typeof activity !== 'object') {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Invalid activity data</Text>
      </View>
    );
  }

  // Render based on context
  switch (context) {
    case "day":
      return (
        <DayActivityCard
          activity={activity}
          isCompleted={isCompleted}
          onToggleCompletion={onToggleCompletion}
          onPress={onPress}
        />
      );
    case "city":
      return (
        <CityActivityCard
          activity={activity}
          isCompleted={isCompleted}
          onToggleCompletion={onToggleCompletion}
          onPress={onPress}
          dayNumber={dayNumber}
          date={date}
        />
      );
    case "timeline":
      return (
        <TimelineActivityCard
          activity={activity}
          isCompleted={isCompleted}
          onToggleCompletion={onToggleCompletion}
          onPress={onPress}
          isLast={isLast}
          index={_index}
        />
      );
    case "draggable":
      // For now, return day context - will be enhanced later if needed
      return (
        <DayActivityCard
          activity={activity}
          isCompleted={isCompleted}
          onToggleCompletion={onToggleCompletion}
          onPress={onPress}
        />
      );
    default:
      return (
        <DayActivityCard
          activity={activity}
          isCompleted={isCompleted}
          onToggleCompletion={onToggleCompletion}
          onPress={onPress}
        />
      );
  }
}


