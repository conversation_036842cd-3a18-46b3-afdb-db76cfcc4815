import { Ionicons } from "@expo/vector-icons";
import React, { useState, useCallback } from "react";
import {
  ActivityIndicator,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import {
  useLocationSuggestions,
  LocationSuggestion,
} from "../../lib/hooks/useLocationSuggestions";
import { LocationSelectionModal } from "./LocationSelectionModal";

interface LocationPickerProps {
  label?: string;
  value?: string;
  placeholder?: string;
  isRequired?: boolean;
  type: "country" | "city";
  visible: boolean;
  onClose: () => void;
  onSelect: (suggestion: LocationSuggestion) => void;
  country?: string; // Required for city selection
  style?: any;
}

export default function LocationPicker({
  label,
  value,
  placeholder = "Select...",
  isRequired = false,
  type,
  visible,
  onClose,
  onSelect,
  country,
  style,
}: LocationPickerProps) {
  const {
    isLoading: isLoadingLocations,
    suggestions: locationSuggestions,
    searchCountries,
    searchCities,
    clearSuggestions,
  } = useLocationSuggestions({
    onError: (error) => console.error("Location search error:", error),
  });

  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = useCallback(
    async (text: string) => {
      setSearchQuery(text);
      if (type === "country") {
        await searchCountries(text);
      } else if (country) {
        await searchCities(text, country);
      }
    },
    [type, country, searchCountries, searchCities],
  );

  const handleClose = useCallback(() => {
    setSearchQuery("");
    clearSuggestions();
    onClose();
  }, [clearSuggestions, onClose]);

  const handleSelectItem = useCallback(
    (suggestion: LocationSuggestion) => {
      onSelect(suggestion);
      handleClose();
    },
    [onSelect, handleClose],
  );

  const getTitle = useCallback(() => {
    if (type === "country") {
      return "Select Country";
    } else if (type === "city") {
      return "Select City";
    }
    return "Select Location";
  }, [type]);

  // If used as a field with label
  if (label) {
    return (
      <View style={[styles.fieldContainer, style]}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {isRequired && <Text style={styles.requiredStar}>*</Text>}
        </View>

        <TouchableOpacity
          style={[styles.input, value ? styles.filledInput : {}]}
          onPress={onClose}
        >
          <Text style={value ? styles.inputText : styles.placeholderText}>
            {value || placeholder}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>

        <LocationSelectionModal
          visible={visible}
          onClose={handleClose}
          title={getTitle()}
          placeholder={`Search ${type === "country" ? "countries" : "cities"}...`}
          onSearch={handleSearch}
          searchQuery={searchQuery}
          isLoadingLocations={isLoadingLocations}
          locationSuggestions={locationSuggestions}
          onSelect={handleSelectItem}
        />
      </View>
    );
  }

  // Original modal implementation
  return (
    <Modal visible={visible} animationType="slide" onRequestClose={handleClose}>
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{getTitle()}</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        <TextInput
          style={styles.searchInput}
          placeholder={`Search for a ${type}...`}
          value={searchQuery}
          onChangeText={handleSearch}
        />
        <ScrollView style={styles.suggestionsList}>
          {isLoadingLocations ? (
            <ActivityIndicator style={styles.loader} />
          ) : (
            locationSuggestions.map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionItem}
                onPress={() => handleSelectItem(suggestion)}
              >
                <Text style={styles.suggestionText}>{suggestion.name}</Text>
                {suggestion.country && (
                  <Text style={styles.suggestionSubtext}>
                    {suggestion.country}
                  </Text>
                )}
              </TouchableOpacity>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  fieldContainer: {
    marginBottom: 16,
  },
  labelContainer: {
    flexDirection: "row",
    marginBottom: 4,
  },
  label: {
    fontSize: 14,
    color: "#666",
  },
  requiredStar: {
    fontSize: 14,
    color: "#FF3B30",
    marginLeft: 4,
  },
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    flex: 1,
    textAlign: "center",
  },
  closeButton: {
    padding: 8,
  },
  searchInput: {
    margin: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
    fontSize: 16,
  },
  suggestionsList: {
    flex: 1,
  },
  suggestionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  suggestionText: {
    fontSize: 16,
    color: "#333",
  },
  suggestionSubtext: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  loader: {
    marginTop: 20,
  },
});
