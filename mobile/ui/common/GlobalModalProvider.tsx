import React from "react";
import { useGlobalModalStore } from "../../lib/store";
import { DaysBalanceService } from "../../lib/services/days-balance.service";
import DaysQuotaModal from "./DaysQuotaModal";

const daysBalanceService = DaysBalanceService.getInstance();

/**
 * Global Modal Provider component
 * Handles global modals that should be accessible from anywhere in the app
 */
export const GlobalModalProvider: React.FC = () => {
  const {
    showDaysQuotaModal,
    requiredDays,
    currentBalance,
    hideDaysQuotaModal
  } = useGlobalModalStore();

  const handleCloseModal = () => {
    hideDaysQuotaModal();
  };

  const handleRefreshBalance = async () => {
    try {
      // Directly call the service to refresh balance
      // This avoids potential conflicts with multiple useDaysBalance hooks
      await daysBalanceService.getBalance();
    } catch (error) {
      console.error("Error refreshing balance from global modal:", error);
    }
  };

  return (
    <>
      <DaysQuotaModal
        visible={showDaysQuotaModal}
        onClose={handleCloseModal}
        currentBalance={currentBalance}
        requiredAmount={requiredDays}
        onRefreshBalance={handleRefreshBalance}
      />
    </>
  );
};
