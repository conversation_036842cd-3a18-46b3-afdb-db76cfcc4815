import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { Text, TouchableOpacity, View } from "react-native";
import { formatTime } from "../../../lib/utils/date";
import { getActivityIcon } from "../../../lib/utils/activity-utils";
import { DayActivityProps } from "./types";
import { safeString } from "./utils";
import { styles } from "./styles";
import { ActivityDetails } from "./ActivityDetails";
import { MapsButton } from "../MapsButton";

export const DayActivityCard: React.FC<DayActivityProps> = ({
  activity,
  isCompleted = false,
  onToggleCompletion,
  onPress
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (onToggleCompletion) {
      onToggleCompletion(safeString(activity.id, 'unknown'), isCompleted);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.activityItem, isCompleted && styles.completedActivity]}
      onPress={handlePress}
      disabled={!onToggleCompletion && !onPress}
    >
      <View style={styles.timeContainer}>
        <Ionicons
          name={getActivityIcon(activity)}
          size={24}
          color={isCompleted ? "#4CAF50" : "#4A90E2"}
        />
        <Text style={styles.time}>{formatTime(safeString(activity.startTime))}</Text>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.activityContent}>
          <Text style={styles.activityTitle}>{safeString(activity.name, "Untitled Activity")}</Text>
          {activity.location && (
            <Text style={styles.location}>{safeString(activity.location)}</Text>
          )}
          <View>
            <ActivityDetails activity={activity} context="day" />
          </View>
        </View>
        <View style={styles.activityActions}>
          {onToggleCompletion && (
            <TouchableOpacity
              style={[
                styles.completionButton,
                isCompleted && styles.completedButton,
              ]}
              onPress={() => onToggleCompletion(safeString(activity.id, 'unknown'), isCompleted)}
            >
              <Ionicons
                name={isCompleted ? "checkmark-circle" : "ellipse-outline"}
                size={24}
                color={isCompleted ? "#4CAF50" : "#CCCCCC"}
              />
            </TouchableOpacity>
          )}
          <MapsButton
            activity={activity}
            variant="icon-only"
            size="small"
            color="#666"
            backgroundColor="transparent"
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};
