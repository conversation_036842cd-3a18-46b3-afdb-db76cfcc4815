import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  // Common styles
  completedActivity: {
    backgroundColor: "#f8fff8",
    borderLeftWidth: 3,
    borderLeftColor: "#4CAF50",
    paddingLeft: 8,
  },
  completedText: {
    color: "#666",
  },

  // Day context styles
  activityItem: {
    flexDirection: "row",
    marginBottom: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  timeContainer: {
    alignItems: "center",
    marginRight: 16,
    width: 50,
  },
  time: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
    lineHeight: 14,
  },
  contentContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-start",
    position: "relative",
  },
  activityContent: {
    flex: 1,
    paddingRight: 8,
  },
  activityActions: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 8,
  },
  completionButton: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  completedButton: {
    transform: [{ scale: 1.1 }],
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: "#666",
  },
  cost: {
    marginTop: 4,
    fontSize: 14,
    color: "#2E7D32",
    fontWeight: "500",
  },

  // City context styles
  cityActivityItem: {
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    paddingBottom: 16,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  activityHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  activityTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 8,
  },
  activityIcon: {
    marginRight: 8,
  },
  activityName: {
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
  },
  checkmark: {
    marginLeft: 4,
  },
  dayTimeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 8,
  },
  dayContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  dayText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    marginLeft: 4,
  },
  dateText: {
    fontSize: 12,
    color: "#666",
    marginLeft: 4,
  },
  timeInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  activityDescription: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    marginBottom: 8,
  },
  activityDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  detailText: {
    fontSize: 13,
    color: "#666",
    marginLeft: 6,
  },

  // Timeline context styles
  timelineItem: {
    flexDirection: "row",
    marginBottom: 20,
  },
  timeColumn: {
    width: 80,
    alignItems: "center",
  },
  timelineTime: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#2196F3",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1,
  },
  timelineLine: {
    position: "absolute",
    top: 40,
    width: 2,
    height: "100%",
    backgroundColor: "#EEEEEE",
  },
  detailsColumn: {
    flex: 1,
    marginLeft: 16,
  },
  timelineHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 4,
  },
  locationText: {
    fontSize: 14,
    color: "#666",
  },
  costRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 4,
  },
  costText: {
    fontSize: 14,
    color: "#666",
  },

  // Error styles
  errorContainer: {
    padding: 16,
    backgroundColor: "#ffebee",
    borderRadius: 8,
    margin: 8,
  },
  errorText: {
    color: "#d32f2f",
    fontSize: 14,
    textAlign: "center",
  },
});
