// Helper function to safely get string values
export const safeString = (value: string | undefined | null, fallback: string = ""): string => {
  return value || fallback;
};

// Helper function to safely get number values
export const safeNumber = (value: number | undefined | null, fallback: number = 0): number => {
  return value ?? fallback;
};

// Helper function to safely get array values
export const safeArray = <T,>(value: T[] | undefined | null): T[] => {
  return Array.isArray(value) ? value : [];
};
