import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { Text, TouchableOpacity, View } from "react-native";
import { formatTime, formatDate } from "../../../lib/utils/date";
import { getActivityIcon } from "../../../lib/utils/activity-utils";
import { CityActivityProps } from "./types";
import { safeString, safeNumber } from "./utils";
import { styles } from "./styles";
import { MapsButton } from "../MapsButton";

export const CityActivityCard: React.FC<CityActivityProps> = ({
  activity,
  isCompleted = false,
  onToggleCompletion,
  onPress,
  dayNumber,
  date
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (onToggleCompletion) {
      onToggleCompletion(safeString(activity.id, 'unknown'), isCompleted);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.cityActivityItem,
        isCompleted && styles.completedActivity,
      ]}
      onPress={handlePress}
      disabled={!onToggleCompletion && !onPress}
    >
      {/* Activity Header with Icon, Title and Completion */}
      <View style={styles.activityHeader}>
        <View style={styles.activityTitleContainer}>
          <Ionicons
            name={getActivityIcon(activity)}
            size={20}
            color={isCompleted ? "#4CAF50" : "#4A90E2"}
            style={styles.activityIcon}
          />
          <Text
            style={[
              styles.activityName,
              isCompleted && styles.completedText,
            ]}
          >
            {safeString(activity.name, "Untitled Activity")}
          </Text>
          {isCompleted ? (
            <Ionicons
              name="checkmark-circle"
              size={20}
              color="#4CAF50"
              style={styles.checkmark}
            />
          ) : null}
        </View>
        <View style={styles.activityActions}>
          <MapsButton
            activity={activity}
            variant="icon-only"
            size="small"
            color="#666"
            backgroundColor="transparent"
          />
        </View>
      </View>

      {/* Day and Time Information */}
      <View style={styles.dayTimeContainer}>
        <View style={styles.dayContainer}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.dayText}>
            Day {dayNumber || 0}
          </Text>
          {date && (
            <Text style={styles.dateText}>
              • {formatDate(date)}
            </Text>
          )}
        </View>
        {activity.startTime && (
          <View style={styles.timeInfoContainer}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.timeText}>
              {formatTime(safeString(activity.startTime))}
            </Text>
          </View>
        )}
      </View>

      {/* Activity Description */}
      {activity.description && (
        <Text
          style={[
            styles.activityDescription,
            isCompleted && styles.completedText,
          ]}
        >
          {safeString(activity.description)}
        </Text>
      )}

      {/* Activity Details */}
      <View style={styles.activityDetails}>
        {activity.location ? (
          <View style={styles.detailRow}>
            <Ionicons name="location-outline" size={14} color="#666" />
            <Text style={styles.detailText}>{safeString(activity.location)}</Text>
          </View>
        ) : null}
        {activity.duration ? (
          <View style={styles.detailRow}>
            <Ionicons name="hourglass-outline" size={14} color="#666" />
            <Text style={styles.detailText}>{safeString(activity.duration)}</Text>
          </View>
        ) : null}
        {activity.cost && activity.cost > 0 ? (
          <View style={styles.detailRow}>
            <Ionicons name="cash-outline" size={14} color="#666" />
            <Text style={styles.detailText}>${safeNumber(activity.cost)}</Text>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};
