import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { Text, TouchableOpacity, View } from "react-native";
import { getActivityIcon, getActivityColor } from "../../../lib/utils/activity-utils";
import { TimelineActivityProps } from "./types";
import { safeString, safeNumber } from "./utils";
import { styles } from "./styles";
import { MapsButton } from "../MapsButton";

export const TimelineActivityCard: React.FC<TimelineActivityProps> = ({
  activity,
  isCompleted = false,
  onToggleCompletion,
  onPress,
  isLast = false
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (onToggleCompletion) {
      onToggleCompletion(safeString(activity.id, 'unknown'), isCompleted);
    }
  };

  return (
    <TouchableOpacity
      style={styles.timelineItem}
      onPress={handlePress}
    >
      <View style={styles.timeColumn}>
        <Text style={styles.timelineTime}>{safeString(activity.startTime, "N/A")}</Text>
        <View
          style={[
            styles.timelineIcon,
            {
              backgroundColor: getActivityColor(activity.type),
            },
          ]}
        >
          <Ionicons name={getActivityIcon(activity)} size={16} color="#FFF" />
        </View>
        {!isLast && <View style={styles.timelineLine} />}
      </View>

      <View style={styles.detailsColumn}>
        <View style={styles.timelineHeader}>
          <Text style={styles.itemTitle}>{safeString(activity.name, "Untitled Activity")}</Text>
          <MapsButton
            activity={activity}
            variant="icon-only"
            size="small"
            color="#666"
            backgroundColor="transparent"
          />
        </View>
        {activity.location && (
          <View style={styles.locationRow}>
            <Ionicons name="location-outline" size={14} color="#666" />
            <Text style={styles.locationText}>{safeString(activity.location)}</Text>
          </View>
        )}
        {activity.description && (
          <Text style={styles.description}>{safeString(activity.description)}</Text>
        )}
        {Boolean(activity.cost) && (
          <View style={styles.costRow}>
            <Ionicons name="cash-outline" size={14} color="#666" />
            <Text style={styles.costText}>${safeNumber(activity.cost)}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};
