import { Activity } from "../../../lib/types";

export type ActivityCardContext = "day" | "city" | "timeline" | "draggable";

// Component interfaces
export interface BaseActivityProps {
  activity: Activity;
  isCompleted?: boolean;
  onToggleCompletion?: (activityId: string, isCompleted: boolean) => void;
  onPress?: () => void;
}

export interface DayActivityProps extends BaseActivityProps { }

export interface CityActivityProps extends BaseActivityProps {
  dayNumber?: number;
  date?: string;
}

export interface TimelineActivityProps extends BaseActivityProps {
  isLast?: boolean;
  index?: number;
}

export interface ActivityCardProps {
  activity: Activity;
  context: ActivityCardContext;
  isCompleted?: boolean;
  onToggleCompletion?: (activityId: string, isCompleted: boolean) => void;
  onPress?: () => void;
  // Context-specific props
  dayNumber?: number;
  date?: string;
  isLast?: boolean; // For timeline context
  index?: number; // For timeline context
  // Draggable context props
  onEdit?: () => void;
  onRemove?: () => void;
  drag?: () => void;
  isActive?: boolean;
}
