import React from "react";
import { Text } from "react-native";
import { Activity } from "../../../lib/types";
import { ActivityCardContext } from "./types";
import { safeString, safeNumber, safeArray } from "./utils";
import { styles } from "./styles";

interface ActivityDetailsProps {
  activity: Activity;
  context: ActivityCardContext;
}

export const ActivityDetails: React.FC<ActivityDetailsProps> = ({
  activity,
  context
}) => {
  if (context === "city") return null; // Details are handled separately in city context

  const details: React.ReactElement[] = [];

  if (activity.type === "activity") {
    if (activity.duration) {
      details.push(
        <Text key="duration" style={styles.description}>
          Duration: {safeString(activity.duration)}
        </Text>
      );
    }
    if (activity.rating) {
      details.push(
        <Text key="rating" style={styles.description}>
          Rating: {safeNumber(activity.rating)}/5
        </Text>
      );
    }
  }

  if (activity.type === "meal") {
    if (activity.mealType) {
      details.push(
        <Text key="mealType" style={styles.description}>
          Meal type: {safeString(activity.mealType)}
        </Text>
      );
    }
  }


  const cost = safeNumber(activity.cost);
  if (cost > 0) {
    const costValue = cost;
    details.push(
      <Text key="cost" style={styles.cost}>
        Estimated cost: ${costValue}
      </Text>
    );
  }

  return <>{details}</>;
};
