import React from "react";
import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import { useGlobalInsufficientDays } from "../../lib/hooks/useGlobalInsufficientDays";

/**
 * Test component to demonstrate the global insufficient balance modal
 * This can be used for testing purposes
 */
export const TestGlobalModal: React.FC = () => {
  const { handleInsufficientBalanceError, checkAndHandleInsufficientBalance } = useGlobalInsufficientDays();

  const testWithContext = () => {
    const errorContext = {
      isInsufficientBalance: true,
      notificationType: "insufficient_balance",
      payload: {
        type: "insufficient_balance",
        content: "Test insufficient balance error",
        available: 2,
        needed: 5,
      }
    };

    handleInsufficientBalanceError("Test insufficient balance error", errorContext);
  };

  const testWithError = () => {
    const mockError = {
      message: "Insufficient days. Required: 3, Available: 1",
      status: 402,
      response: { status: 402 }
    };

    checkAndHandleInsufficientBalance(mockError);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.button} onPress={testWithContext}>
        <Text style={styles.buttonText}>Test Global Modal (With Context)</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testWithError}>
        <Text style={styles.buttonText}>Test Global Modal (With Error)</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 10,
  },
  button: {
    backgroundColor: "#FF6B6B",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "bold",
  },
});
