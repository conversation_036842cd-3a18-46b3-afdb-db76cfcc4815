import { Ionicons } from "@expo/vector-icons";
import React, { forwardRef, useEffect, useRef } from "react";
import { StyleSheet, TouchableOpacity, View, Animated } from "react-native";

export interface FabButtonProps {
  onPress: (type?: "full" | "activities" | "restaurants" | "hotels") => void;
}

const FabButton = forwardRef<View, FabButtonProps>(({ onPress }, ref) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Initial entrance animation
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 50,
      friction: 3,
      useNativeDriver: true,
    }).start();

    // Continuous subtle pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => {
      pulseAnimation.stop();
    };
  }, []);

  const handlePress = () => {
    // Rotation animation on press
    Animated.sequence([
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    onPress("full");
  };

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  return (
    <View ref={ref} style={styles.fabContainer}>
      <Animated.View
        style={[
          styles.fabButton,
          {
            transform: [
              { scale: Animated.multiply(scaleAnim, pulseAnim) },
              { rotate: rotateInterpolate },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.touchableArea}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="add" size={28} color="white" />
          </View>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
});

export default FabButton;

const styles = StyleSheet.create({
  fabContainer: {
    position: "absolute",
    bottom: 32,
    right: 24,
    zIndex: 1000,
    // Ensure consistent positioning across views
    elevation: 10,
  },
  fabButton: {
    backgroundColor: "#2196F3",
    width: 64,
    height: 64,
    borderRadius: 32,
    elevation: 8,
    shadowColor: "#2196F3",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    // White border for better definition
    borderWidth: 3,
    borderColor: "#FFFFFF",
  },
  touchableArea: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 32,
  },
  iconContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 20,
    padding: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
    alignItems: "flex-end",
    padding: 20,
  },
  menuContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    marginBottom: 80,
    marginRight: 10,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    minWidth: 200,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
    color: "#333",
  },
});
