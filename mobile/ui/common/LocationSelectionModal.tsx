import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LocationSuggestion } from "../../lib/hooks/useLocationSuggestions";

interface LocationSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  placeholder: string;
  onSearch: (text: string) => void;
  searchQuery: string;
  isLoadingLocations: boolean;
  locationSuggestions: LocationSuggestion[];
  onSelect: (item: LocationSuggestion) => void;
  selectedItems?: string[];
  onRemove?: (item: string) => void;
}

export const LocationSelectionModal: React.FC<LocationSelectionModalProps> = ({
  visible,
  onClose,
  title,
  placeholder,
  onSearch,
  searchQuery,
  isLoadingLocations,
  locationSuggestions,
  onSelect,
  selectedItems = [],
  onRemove,
}) => {
  const [searchText, setSearchText] = useState("");

  const handleSearch = (text: string) => {
    setSearchText(text);
    onSearch(text);
  };

  const handleSelect = (item: LocationSuggestion) => () => {
    onSelect(item);
    setSearchText("");
  };

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>{title}</Text>
          <View style={{ width: 24 }} />
        </View>
        {selectedItems.length > 0 && (
          <View style={styles.selectedItemsContainer}>
            <Text style={styles.selectedItemsTitle}>Selected:</Text>
            <View style={styles.selectedItemsList}>
              {selectedItems.map((item, index) => (
                <View key={index} style={styles.selectedItem}>
                  <Text style={styles.selectedItemText}>{item}</Text>
                  {onRemove && (
                    <TouchableOpacity
                      onPress={() => onRemove(item)}
                      style={styles.removeButton}
                    >
                      <Ionicons name="close-circle" size={20} color="#FF3B30" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder={placeholder}
            value={searchText}
            onChangeText={handleSearch}
            autoFocus
          />
        </View>
        {isLoadingLocations ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2196F3" />
          </View>
        ) : locationSuggestions.length === 0 ? (
          <View style={styles.noResultsContainer}>
            <Text style={styles.noResultsText}>No results found</Text>
          </View>
        ) : (
          <ScrollView>
            {locationSuggestions.map((item, index) => (
              <TouchableOpacity
                style={styles.suggestionItem}
                onPress={handleSelect(item)}
                key={index}
              >
                <Text style={styles.suggestionText}>{item.name}</Text>
                {item.country && (
                  <Text
                    style={[
                      styles.suggestionText,
                      { fontSize: 14, color: "#666" },
                    ]}
                  >
                    {item.country}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#f5f5f5",
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  suggestionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  suggestionText: {
    fontSize: 16,
    color: "#333",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  noResultsText: {
    fontSize: 16,
    color: "#666",
  },
  selectedItemsContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedItemsTitle: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  selectedItemsList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  selectedItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectedItemText: {
    fontSize: 14,
    color: "#333",
    marginRight: 4,
  },
  removeButton: {
    padding: 2,
  },
});
