import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LocationSuggestion } from "../../lib/hooks/useLocationSuggestions";
import { FormField } from "./FormField";
import LocationPicker from "./LocationPicker";

interface LocationFieldProps {
  label: string;
  value: string;
  placeholder?: string;
  isRequired?: boolean;
  type: "country" | "city";
  country?: string; // Required for city selection
  onSelect: (suggestion: LocationSuggestion) => void;
  style?: any;
}

const LocationField: React.FC<LocationFieldProps> = ({
  label,
  value,
  placeholder = "Select...",
  isRequired = false,
  type,
  country,
  onSelect,
  style,
}) => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleSelect = (suggestion: LocationSuggestion) => {
    onSelect(suggestion);
    setModalVisible(false);
  };

  return (
    <FormField label={label} isRequired={isRequired} style={style}>
      <TouchableOpacity
        style={[styles.input, value ? styles.filledInput : {}]}
        onPress={() => setModalVisible(true)}
      >
        <Text style={value ? styles.inputText : styles.placeholderText}>
          {value || placeholder}
        </Text>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>

      <LocationPicker
        type={type}
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onSelect={handleSelect}
        country={country}
      />
    </FormField>
  );
};

const styles = StyleSheet.create({
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },
});

export default LocationField;
