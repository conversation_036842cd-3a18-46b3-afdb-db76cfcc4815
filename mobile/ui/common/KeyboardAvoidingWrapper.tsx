import React from "react";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  ViewStyle,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

interface KeyboardAvoidingWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  scrollEnabled?: boolean;
  showsVerticalScrollIndicator?: boolean;
  keyboardVerticalOffset?: number;
  behavior?: "height" | "position" | "padding";
  enableOnAndroid?: boolean;
}

/**
 * Reusable KeyboardAvoidingView wrapper component that provides consistent
 * keyboard avoidance behavior across all screens with input fields.
 * 
 * Features:
 * - Automatic platform-specific behavior (iOS: padding, Android: height)
 * - Safe area insets integration
 * - Optional ScrollView integration
 * - Customizable keyboard offset
 * - Consistent styling and behavior
 */
export const KeyboardAvoidingWrapper: React.FC<KeyboardAvoidingWrapperProps> = ({
  children,
  style,
  contentContainerStyle,
  scrollEnabled = true,
  showsVerticalScrollIndicator = false,
  keyboardVerticalOffset,
  behavior,
  enableOnAndroid = true,
}) => {
  const { bottom } = useSafeAreaInsets();

  // Determine behavior based on platform
  const keyboardBehavior = behavior || (Platform.OS === "ios" ? "padding" : "height");
  
  // Calculate keyboard offset
  const defaultOffset = Platform.OS === "ios" ? 64 : 0;
  const offset = keyboardVerticalOffset !== undefined ? keyboardVerticalOffset : defaultOffset;

  // Determine if keyboard avoiding should be enabled
  const shouldAvoidKeyboard = Platform.OS === "ios" || enableOnAndroid;

  const keyboardAvoidingProps = {
    style: [styles.keyboardAvoidingView, style],
    behavior: keyboardBehavior,
    keyboardVerticalOffset: offset,
  };

  const scrollViewProps = {
    style: styles.scrollView,
    contentContainerStyle: [
      styles.contentContainer,
      { paddingBottom: bottom + 20 },
      contentContainerStyle,
    ],
    showsVerticalScrollIndicator,
    keyboardShouldPersistTaps: "handled" as const,
  };

  if (scrollEnabled) {
    return shouldAvoidKeyboard ? (
      <KeyboardAvoidingView {...keyboardAvoidingProps}>
        <ScrollView {...scrollViewProps}>
          {children}
        </ScrollView>
      </KeyboardAvoidingView>
    ) : (
      <ScrollView {...scrollViewProps}>
        {children}
      </ScrollView>
    );
  }

  return shouldAvoidKeyboard ? (
    <KeyboardAvoidingView {...keyboardAvoidingProps}>
      {children}
    </KeyboardAvoidingView>
  ) : (
    <>{children}</>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
});
