import React from "react";
import { TouchableOpacity, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Activity } from "../../lib/types";
import { MapsService } from "../../lib/services/maps.service";

interface MapsButtonProps {
  activity: Activity;
  variant?: "default" | "compact" | "icon-only";
  color?: string;
  backgroundColor?: string;
  borderColor?: string;
  size?: "small" | "medium" | "large";
  onPress?: () => void;
}

/**
 * Reusable button component for opening activities in maps applications
 * Can be used across different contexts with various styling options
 */
export const MapsButton: React.FC<MapsButtonProps> = ({
  activity,
  variant = "default",
  color = "#007AFF",
  backgroundColor = "#F0F8FF",
  borderColor = "#007AFF",
  size = "medium",
  onPress,
}) => {
  // Don't render if activity has no location data
  if (!activity.coordinates && !activity.location) {
    return null;
  }

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      MapsService.showMapsAppSelector(activity);
    }
  };

  const getStyles = () => {
    const baseStyle: any[] = [styles.button];
    const textStyle: any[] = [styles.buttonText];
    const iconSize = size === "small" ? 14 : size === "large" ? 20 : 16;

    // Apply size-specific styles
    if (size === "small") {
      baseStyle.push(styles.smallButton);
      textStyle.push(styles.smallText);
    } else if (size === "large") {
      baseStyle.push(styles.largeButton);
      textStyle.push(styles.largeText);
    }

    // Apply variant-specific styles
    if (variant === "compact") {
      baseStyle.push(styles.compactButton);
    } else if (variant === "icon-only") {
      baseStyle.push(styles.iconOnlyButton);
    }

    // Apply custom colors
    baseStyle.push({
      backgroundColor,
      borderColor,
    });

    textStyle.push({
      color,
    });

    return { baseStyle, textStyle, iconSize };
  };

  const { baseStyle, textStyle, iconSize } = getStyles();

  return (
    <TouchableOpacity
      style={baseStyle}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Ionicons name="map" size={iconSize} color={color} />
      {variant !== "icon-only" && (
        <Text style={textStyle}>
          {variant === "compact" ? "Maps" : "Open in Maps"}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  smallButton: {
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
  },
  largeButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 10,
  },
  compactButton: {
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  iconOnlyButton: {
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 20,
  },
  buttonText: {
    fontSize: 13,
    fontWeight: "600",
    marginLeft: 4,
  },
  smallText: {
    fontSize: 12,
    marginLeft: 3,
  },
  largeText: {
    fontSize: 14,
    marginLeft: 5,
  },
});

export default MapsButton;
