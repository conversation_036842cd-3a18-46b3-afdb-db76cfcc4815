import { Ionicons } from "@expo/vector-icons";
import { router, Stack, useRouter, useSegments } from "expo-router";
import React, { useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from "react-native";

interface ScreenHeaderProps {
  title: string;
  subtitle: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  transparent?: boolean;
  onBackPress?: () => void;
}

export default function ScreenHeader({
  title,
  subtitle,
  rightComponent,
  showBackButton = true,
  transparent = false,
  onBackPress,
}: ScreenHeaderProps) {
  const segments = useSegments();

  const handleBack = () => {
    // Use custom back handler if provided
    if (onBackPress) {
      onBackPress();
      return;
    }

    // Default back navigation
    if (segments[segments.length - 1] === "trip-view") {
      router.replace("/home");
    } else {
      router.back();
    }
  };

  const canSeeBackButton = useMemo(() => {
    // Don't show back button on home screen to prevent navigation errors
    if (segments[segments.length - 1] === "home") {
      return false;
    }

    if (showBackButton) {
      return true;
    }
    return false;
  }, [showBackButton, segments]);

  return (
    <View
      style={[
        styles.container,
        transparent && { backgroundColor: "transparent" },
      ]}
    >
      <View style={styles.header}>
        {canSeeBackButton && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="chevron-back" size={28} color="#2196F3" />
          </TouchableOpacity>
        )}
        <View
          style={[
            styles.titleContainer,
            !showBackButton && styles.titleContainerNoBack,
          ]}
        >
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>
          <Text style={styles.subtitle} numberOfLines={2}>
            {subtitle}
          </Text>
        </View>
        {rightComponent && (
          <View style={styles.rightComponent}>{rightComponent}</View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingTop: 0,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
    elevation: 1,
    zIndex: 10000,
  },
  header: {
    paddingVertical: Platform.OS === "android" ? 4 : 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(33, 150, 243, 0.08)",
    justifyContent: "center",
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    marginLeft: 4,
  },
  titleContainerNoBack: {
    marginLeft: 0,
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
    opacity: 0.9,
  },
  rightComponent: {
    marginLeft: "auto",
  },
});
