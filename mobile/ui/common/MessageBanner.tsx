import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, Animated } from "react-native";
import { Ionicons } from "@expo/vector-icons";

export type MessageBannerType = "error" | "warning" | "info" | "success";

interface MessageBannerProps {
  visible: boolean;
  message: string;
  type?: MessageBannerType;
  onClose: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

/**
 * Message banner component with close button
 * Displays messages at the top of screens as an alternative to Alert dialogs
 */
export const MessageBanner: React.FC<MessageBannerProps> = ({
  visible,
  message,
  type = "error",
  onClose,
  autoHide = false,
  autoHideDelay = 5000,
}) => {
  const slideAnim = React.useRef(new Animated.Value(-100)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      if (autoHide) {
        const timer = setTimeout(() => {
          onClose();
        }, autoHideDelay);
        return () => clearTimeout(timer);
      }
    } else {
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim, autoHide, autoHideDelay, onClose]);

  if (!visible) return null;

  const getBannerStyle = () => {
    switch (type) {
      case "error":
        return styles.errorBanner;
      case "warning":
        return styles.warningBanner;
      case "info":
        return styles.infoBanner;
      case "success":
        return styles.successBanner;
      default:
        return styles.errorBanner;
    }
  };

  const getIconName = () => {
    switch (type) {
      case "error":
        return "alert-circle";
      case "warning":
        return "warning";
      case "info":
        return "information-circle";
      case "success":
        return "checkmark-circle";
      default:
        return "alert-circle";
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        getBannerStyle(),
        { transform: [{ translateY: slideAnim }] },
      ]}
    >
      <View style={styles.content}>
        <Ionicons
          name={getIconName()}
          size={20}
          color="#fff"
          style={styles.icon}
        />
        <Text style={styles.message} numberOfLines={3}>
          {message}
        </Text>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: 50, // Account for status bar
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  icon: {
    marginRight: 12,
  },
  message: {
    flex: 1,
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
    lineHeight: 18,
  },
  closeButton: {
    marginLeft: 12,
    padding: 4,
  },
  errorBanner: {
    backgroundColor: "#FF4444",
  },
  warningBanner: {
    backgroundColor: "#FF9800",
  },
  infoBanner: {
    backgroundColor: "#2196F3",
  },
  successBanner: {
    backgroundColor: "#4CAF50",
  },
});
