import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ErrorStateProps {
  error: string;
  onRetry: () => void;
}

/**
 * Maps developer-oriented error messages to user-friendly messages
 */
function getUserFriendlyErrorMessage(error: string): string {
  const errorLower = error.toLowerCase();

  // Server connection errors
  if (errorLower.includes('failed to fetch') ||
    errorLower.includes('network') ||
    errorLower.includes('connection') ||
    errorLower.includes('econnreset') ||
    errorLower.includes('econnrefused') ||
    errorLower.includes('enotfound')) {
    return "Our servers are experiencing issues. Please try again in a few moments.";
  }

  // Trip-related errors
  if (errorLower.includes('failed to fetch trips')) {
    return "We're having trouble loading your trips. Please try again.";
  }

  if (errorLower.includes('failed to fetch trip') ||
    errorLower.includes('failed to load trip')) {
    return "We couldn't load this trip. Please try again.";
  }

  if (errorLower.includes('failed to fetch filtered trips')) {
    return "We're having trouble finding your trips. Please try again.";
  }

  if (errorLower.includes('failed to fetch trip cities')) {
    return "We couldn't load the cities for this trip. Please try again.";
  }

  if (errorLower.includes('failed to create trip') ||
    errorLower.includes('failed to generate trip')) {
    return "We couldn't create your trip right now. Please try again.";
  }

  if (errorLower.includes('failed to update trip')) {
    return "We couldn't save your changes. Please try again.";
  }

  if (errorLower.includes('failed to delete trip')) {
    return "We couldn't delete this trip. Please try again.";
  }

  // Activity-related errors
  if (errorLower.includes('failed to load activities') ||
    errorLower.includes('error loading activities')) {
    return "We're having trouble loading the activities. Please try again.";
  }

  // Authentication errors
  if (errorLower.includes('unauthorized') ||
    errorLower.includes('authentication') ||
    errorLower.includes('forbidden')) {
    return "Your session has expired. Please log in again.";
  }

  // Server errors
  if (errorLower.includes('server error') ||
    errorLower.includes('internal server error') ||
    error.includes('500') || error.includes('502') || error.includes('503')) {
    return "Our servers are experiencing issues. Please try again in a few moments.";
  }

  // Rate limiting
  if (errorLower.includes('rate limit') ||
    errorLower.includes('too many requests') ||
    error.includes('429')) {
    return "You're making requests too quickly. Please wait a moment and try again.";
  }

  // Timeout errors
  if (errorLower.includes('timeout') ||
    errorLower.includes('timed out')) {
    return "The request is taking longer than expected. Please try again.";
  }

  // Parsing and validation errors
  if (errorLower.includes('failed to parse') ||
    errorLower.includes('validation') ||
    errorLower.includes('invalid format') ||
    errorLower.includes('syntax error')) {
    return "We encountered an issue processing your request. Please try again.";
  }

  // Generic unknown errors
  if (errorLower.includes('unknown error') ||
    errorLower.includes('unexpected error')) {
    return "Something unexpected happened. Please try again.";
  }

  // If no specific mapping found, return a generic user-friendly message
  return "Something went wrong. Please try again.";
}

export default function ErrorState({ error, onRetry }: ErrorStateProps) {
  if (!error) return null;

  const userFriendlyMessage = getUserFriendlyErrorMessage(error);

  return (
    <View style={styles.container}>
      <Ionicons name="alert-circle-outline" size={48} color="#FF4444" />
      <Text style={styles.errorText}>{userFriendlyMessage}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Ionicons name="refresh-outline" size={20} color="#FFFFFF" />
        <Text style={styles.retryText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginVertical: 16,
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2196F3",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 8,
  },
  retryText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
});
