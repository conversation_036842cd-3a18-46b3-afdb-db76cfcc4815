import React from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Platform,
  View,
  Modal,
  TouchableWithoutFeedback,
} from "react-native";
import DTPicker from "@react-native-community/datetimepicker";
import { Ionicons } from "@expo/vector-icons";

interface DateTimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  mode: "date" | "time";
  label?: string;
  error?: string;
  disabled?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  prefix?: string;
  minimumDate?: Date;
  maximumDate?: Date;
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const formatTime = (date: Date) => {
  return date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });
};

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  value,
  onChange,
  mode,
  label,
  error,
  disabled = false,
  icon = mode === "date" ? "calendar-outline" : "time-outline",
  prefix,
  minimumDate,
  maximumDate,
}) => {
  const [show, setShow] = React.useState(false);
  const [tempDate, setTempDate] = React.useState(value);

  const handleChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === "android") {
      setShow(false);
      if (selectedDate) {
        onChange(selectedDate);
      }
    } else {
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    }
  };

  const handleConfirm = () => {
    onChange(tempDate);
    setShow(false);
  };

  const handleCancel = () => {
    setTempDate(value);
    setShow(false);
  };

  const getFormattedValue = () => {
    const timeStr = formatTime(value);
    const dateStr = formatDate(value);

    if (prefix) {
      return `${prefix} ${mode === "date" ? dateStr : timeStr}`;
    }
    return mode === "date" ? dateStr : timeStr;
  };

  return (
    <>
      <TouchableOpacity
        style={[
          styles.dateButton,
          disabled && styles.disabled,
          error && styles.error,
        ]}
        onPress={() => !disabled && setShow(true)}
        disabled={disabled}
      >
        <View style={styles.dateButtonContent}>
          <Ionicons name={icon} size={24} color="#666" style={styles.icon} />
          <Text
            style={[styles.dateButtonText, disabled && styles.disabledText]}
            numberOfLines={1}
          >
            {getFormattedValue()}
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>

      {Platform.OS === "ios" ? (
        <Modal visible={show} transparent animationType="fade">
          <TouchableWithoutFeedback onPress={handleCancel}>
            <View style={styles.modalOverlay}>
              <TouchableWithoutFeedback>
                <View style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>
                      {label ||
                        (mode === "date" ? "Select Date" : "Select Time")}
                    </Text>
                    <TouchableOpacity onPress={handleConfirm}>
                      <Text style={styles.closeButton}>Done</Text>
                    </TouchableOpacity>
                  </View>
                  <DTPicker
                    value={tempDate}
                    mode={mode}
                    display="spinner"
                    onChange={handleChange}
                    themeVariant="light"
                    minimumDate={mode === "date" ? minimumDate : undefined}
                    maximumDate={mode === "date" ? maximumDate : undefined}
                  />
                </View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      ) : (
        show && (
          <DTPicker
            value={value}
            mode={mode}
            display="default"
            onChange={handleChange}
            minimumDate={mode === "date" ? minimumDate : undefined}
            maximumDate={mode === "date" ? maximumDate : undefined}
          />
        )
      )}
    </>
  );
};

const styles = StyleSheet.create({
  dateButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    minHeight: 48,
  },
  dateButtonContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  icon: {
    marginRight: 8,
    flexShrink: 0,
  },
  dateButtonText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  disabled: {
    backgroundColor: "#f5f5f5",
    opacity: 0.7,
  },
  disabledText: {
    color: "#999",
  },
  error: {
    borderColor: "#FF3B30",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    width: "80%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  closeButton: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "500",
  },
});
