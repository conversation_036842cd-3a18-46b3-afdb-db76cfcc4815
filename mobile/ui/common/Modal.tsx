import React, { useCallback, useEffect, useState } from "react";
import { View, StyleSheet, Dimensions, Modal as RNModal } from "react-native";
import { PanGestureHandler } from "react-native-gesture-handler";
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from "react-native-reanimated";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");
const MAX_TRANSLATE_Y = -SCREEN_HEIGHT + 50;

type ModalProps = {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  initialSnap?: "half" | "full";
};

const Modal = ({
  visible,
  onClose,
  children,
  initialSnap = "half",
}: ModalProps) => {
  const translateY = useSharedValue(0);
  const initialHeight =
    initialSnap === "half" ? SCREEN_HEIGHT / 2 : SCREEN_HEIGHT;
  const [modalHeight, setModalHeight] = useState(initialHeight);

  useEffect(() => {
    if (visible) {
      translateY.value = withSpring(
        initialSnap === "half" ? MAX_TRANSLATE_Y / 2 : MAX_TRANSLATE_Y,
        { damping: 50 },
      );
    } else {
      translateY.value = withSpring(0, { damping: 50 });
    }
  }, [visible, initialSnap]);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx: any) => {
      ctx.startY = translateY.value;
    },
    onActive: (event, ctx) => {
      const newTranslateY = ctx.startY + event.translationY;
      translateY.value = Math.max(MAX_TRANSLATE_Y, Math.min(0, newTranslateY));
    },
    onEnd: (event) => {
      const velocity = event.velocityY;

      if (velocity > 1000 && translateY.value > MAX_TRANSLATE_Y / 2) {
        translateY.value = withSpring(0, { velocity });
        runOnJS(setModalHeight)(initialHeight);
        runOnJS(onClose)();
      } else if (velocity < -1000 || translateY.value < MAX_TRANSLATE_Y / 2) {
        translateY.value = withSpring(MAX_TRANSLATE_Y, { velocity });
        runOnJS(setModalHeight)(SCREEN_HEIGHT);
      } else {
        translateY.value = withSpring(MAX_TRANSLATE_Y / 2);
        runOnJS(setModalHeight)(SCREEN_HEIGHT / 2);
      }
    },
  });

  const rStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const handleBackdropPress = useCallback(() => {
    onClose();
  }, [onClose]);

  if (!visible) return null;

  return (
    <RNModal transparent visible={visible}>
      <View style={styles.overlay}>
        <View style={styles.backdrop} onTouchEnd={handleBackdropPress} />
        <PanGestureHandler onGestureEvent={gestureHandler}>
          <Animated.View
            style={[
              styles.container,
              rStyle,
              {
                height: modalHeight,
              },
            ]}
          >
            <View style={styles.dragIndicator} />
            {children}
          </Animated.View>
        </PanGestureHandler>
      </View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "flex-end",
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  container: {
    height: "100%",
    width: "100%",
    backgroundColor: "white",
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    position: "absolute",
    top: SCREEN_HEIGHT,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: "#DFDFDF",
    borderRadius: 2,
    alignSelf: "center",
    marginTop: 10,
  },
});

export default Modal;
