import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect } from "react";
import {
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
} from "react-native";
import { mediumHapticFeedback } from "../../lib/utils/haptics";

interface DaysQuotaModalProps {
  visible: boolean;
  onClose: () => void;
  currentBalance?: number;
  requiredAmount?: number;
  onRefreshBalance?: () => void;
}

const DaysQuotaModal: React.FC<DaysQuotaModalProps> = ({
  visible,
  onClose,
  currentBalance = 0,
  requiredAmount = 0,
  onRefreshBalance,
}) => {
  // Animation values
  const opacity = React.useRef(new Animated.Value(0)).current;
  const scale = React.useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      // Trigger haptic feedback when modal appears
      mediumHapticFeedback();

      // Animate in
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Refresh balance when modal opens
      if (onRefreshBalance) {
        onRefreshBalance();
      }
    } else {
      // Animate out
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, opacity, scale, onRefreshBalance]);

  const handleGetDays = () => {
    onClose();
    router.push("/buy-credits");
  };

  const handleViewUsage = () => {
    onClose();
    router.push("/credits-dashboard");
  };

  // Calculate the amount needed
  const amountNeeded = Math.max(requiredAmount - currentBalance, 0);

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              opacity,
              transform: [{ scale }],
            },
          ]}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="calendar-outline" size={40} color="#FF9800" />
          </View>

          <Text style={styles.title}>Insufficient Days</Text>

          <Text style={styles.message}>
            You don't have enough days to complete this operation.
            {requiredAmount > 0 && currentBalance >= 0 && (
              <>
                {"\n\n"}
                <Text style={styles.balanceText}>
                  Current balance:{" "}
                  <Text style={styles.highlight}>
                    {currentBalance.toLocaleString()}
                  </Text>{" "}
                  days
                  {"\n"}
                  Required:{" "}
                  <Text style={styles.highlight}>
                    {requiredAmount.toLocaleString()}
                  </Text>{" "}
                  days
                  {"\n"}
                  Needed:{" "}
                  <Text style={styles.highlightNeeded}>
                    {amountNeeded.toLocaleString()}
                  </Text>{" "}
                  more days
                </Text>
              </>
            )}
          </Text>

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleGetDays}
            activeOpacity={0.7}
          >
            <Ionicons name="add-circle" size={20} color="#FFF" />
            <Text style={styles.primaryButtonText}>Get More Days</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleViewUsage}
            activeOpacity={0.7}
          >
            <Ionicons name="bar-chart" size={20} color="#2196F3" />
            <Text style={styles.secondaryButtonText}>View Usage History</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get("window");
const modalWidth = Math.min(width - 40, 400);

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: modalWidth,
    backgroundColor: "#FFF",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#FFF8E1",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: "700",
    color: "#333",
    marginBottom: 12,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  balanceText: {
    fontSize: 15,
    lineHeight: 24,
    textAlign: "center",
  },
  highlight: {
    fontWeight: "700",
    color: "#2196F3",
  },
  highlightNeeded: {
    fontWeight: "700",
    color: "#FF5722",
  },
  primaryButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#4CAF50",
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    width: "100%",
    marginBottom: 12,
  },
  primaryButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  secondaryButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#E3F2FD",
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    width: "100%",
    marginBottom: 16,
  },
  secondaryButtonText: {
    color: "#2196F3",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  closeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  closeButtonText: {
    color: "#666",
    fontSize: 14,
  },
});

export default DaysQuotaModal;
