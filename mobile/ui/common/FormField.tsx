import React from "react";
import { View, Text, StyleSheet } from "react-native";

interface FormFieldProps {
  label: string;
  isRequired?: boolean;
  hint?: string;
  error?: string;
  children: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  isRequired = false,
  hint,
  error,
  children,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {label}
          {isRequired && <Text style={styles.requiredStar}>*</Text>}
        </Text>
      </View>
      {hint && <Text style={styles.hintText}>{hint}</Text>}
      {children}
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  requiredStar: {
    color: "#FF3B30",
    marginLeft: 4,
  },

  hintText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    color: "#FF3B30",
    marginTop: 4,
  },
});
