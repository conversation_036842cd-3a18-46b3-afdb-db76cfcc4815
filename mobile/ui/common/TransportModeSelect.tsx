import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { TRANSPORT_MODES } from "../../lib/constants";

type TransportMode = typeof TRANSPORT_MODES[number];

interface TransportModeSelectProps {
  value?: TransportMode;
  onChange: (mode: TransportMode) => void;
  placeholder?: string;
}

/**
 * Transport Mode Select component
 * Displays a dropdown-style selector for transport modes
 */
export const TransportModeSelect: React.FC<TransportModeSelectProps> = ({
  value,
  onChange,
  placeholder = "Mode",
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const getTransportIcon = (mode: string) => {
    switch (mode) {
      case "Air":
        return "airplane";
      case "Land":
        return "bus";
      case "Sea":
        return "boat";
      default:
        return "help";
    }
  };

  const handleSelect = (mode: TransportMode) => {
    onChange(mode);
    setIsOpen(false);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          value && styles.selectedSelector,
        ]}
        onPress={() => setIsOpen(true)}
      >
        <View style={styles.selectorContent}>
          {value ? (
            <>
              <Ionicons
                name={getTransportIcon(value)}
                size={16}
                color="#007AFF"
                style={styles.icon}
              />
              <Text style={styles.selectedText}>{value}</Text>
            </>
          ) : (
            <Text style={styles.placeholderText}>{placeholder}</Text>
          )}
        </View>
        <Ionicons name="chevron-down" size={16} color="#999" />
      </TouchableOpacity>

      <Modal
        visible={isOpen}
        transparent
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Transport Mode</Text>
              <TouchableOpacity onPress={() => setIsOpen(false)}>
                <Ionicons name="close" size={20} color="#666" />
              </TouchableOpacity>
            </View>
            <FlatList
              data={TRANSPORT_MODES}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.option,
                    item === value && styles.selectedOption,
                  ]}
                  onPress={() => handleSelect(item)}
                >
                  <View style={styles.optionContent}>
                    <Ionicons
                      name={getTransportIcon(item)}
                      size={20}
                      color={item === value ? "#007AFF" : "#666"}
                      style={styles.optionIcon}
                    />
                    <Text
                      style={[
                        styles.optionText,
                        item === value && styles.selectedOptionText,
                      ]}
                    >
                      {item}
                    </Text>
                  </View>
                  {item === value && (
                    <Ionicons name="checkmark" size={20} color="#007AFF" />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  selector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
    minHeight: 48,
  },
  selectedSelector: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  selectorContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  icon: {
    marginRight: 8,
  },
  selectedText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "500",
  },
  placeholderText: {
    fontSize: 14,
    color: "#999",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 12,
    width: "80%",
    maxWidth: 300,
    maxHeight: "60%",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f8f9fa",
  },
  selectedOption: {
    backgroundColor: "#f0f8ff",
  },
  optionContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  optionIcon: {
    marginRight: 12,
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
  selectedOptionText: {
    color: "#007AFF",
    fontWeight: "500",
  },
});
