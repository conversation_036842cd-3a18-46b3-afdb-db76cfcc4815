import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { BUDGET_OPTIONS } from "../../lib/constants";
import { BudgetType } from "../../lib/types";

interface BudgetSelectorProps {
  value: BudgetType;
  onValueChange: (value: BudgetType) => void;
}

export function BudgetSelector({ value, onValueChange }: BudgetSelectorProps) {
  return (
    <View style={styles.container}>
      {BUDGET_OPTIONS.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.optionContainer,
            value === option.value && styles.selectedOption,
          ]}
          onPress={() => onValueChange(option.value)}
        >
          <View style={styles.optionContent}>
            <Text
              style={[
                styles.optionLabel,
                value === option.value && styles.selectedLabel,
              ]}
            >
              {option.label}
            </Text>
            <Text
              style={[
                styles.optionDescription,
                value === option.value && styles.selectedDescription,
              ]}
            >
              {option.description}
            </Text>
          </View>
          <View
            style={[
              styles.radioButton,
              value === option.value && styles.selectedRadio,
            ]}
          >
            {value === option.value && <View style={styles.radioInner} />}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedOption: {
    borderColor: "#2196F3",
    backgroundColor: "#f3f8ff",
  },
  optionContent: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  selectedLabel: {
    color: "#2196F3",
  },
  optionDescription: {
    fontSize: 14,
    color: "#666",
  },
  selectedDescription: {
    color: "#2196F3",
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#e0e0e0",
    alignItems: "center",
    justifyContent: "center",
  },
  selectedRadio: {
    borderColor: "#2196F3",
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#2196F3",
  },
});
