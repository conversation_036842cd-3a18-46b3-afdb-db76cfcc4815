import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface PeopleCounterProps {
  label: string;
  value: number;
  minValue?: number;
  onIncrement: () => void;
  onDecrement: () => void;
}

export function PeopleCounter({
  label,
  value,
  minValue = 0,
  onIncrement,
  onDecrement,
}: PeopleCounterProps) {
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.counterContainer}>
        <TouchableOpacity
          style={[
            styles.counterButton,
            value <= minValue && styles.counterButtonDisabled,
          ]}
          onPress={onDecrement}
          disabled={value <= minValue}
        >
          <Ionicons
            name="remove"
            size={24}
            color={value <= minValue ? "#ccc" : "#2196F3"}
          />
        </TouchableOpacity>

        <Text style={styles.counterValue}>{value}</Text>

        <TouchableOpacity style={styles.counterButton} onPress={onIncrement}>
          <Ionicons name="add" size={24} color="#2196F3" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  counterContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  counterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
  },
  counterButtonDisabled: {
    opacity: 0.5,
  },
  counterValue: {
    fontSize: 20,
    fontWeight: "bold",
    marginHorizontal: 20,
    color: "#333",
    minWidth: 30,
    textAlign: "center",
  },
});
