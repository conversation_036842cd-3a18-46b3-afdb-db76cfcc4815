import React, { useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import {
  useTutorial,
  TutorialStep,
  TooltipPosition,
} from "../common/TutorialManager";

/**
 * Example component demonstrating the enhanced tutorial system with various tooltip positions
 */
const TutorialExample: React.FC = () => {
  // Get tutorial context
  const { registerTutorial, startTutorial, resetTutorial } = useTutorial();

  // Create refs for tutorial targets
  const topLeftRef = useRef<View>(null);
  const topCenterRef = useRef<View>(null);
  const topRightRef = useRef<View>(null);
  const bottomLeftRef = useRef<View>(null);
  const bottomCenterRef = useRef<View>(null);
  const bottomRightRef = useRef<View>(null);
  const leftTopRef = useRef<View>(null);
  const leftCenterRef = useRef<View>(null);
  const leftBottomRef = useRef<View>(null);
  const rightTopRef = useRef<View>(null);
  const rightCenterRef = useRef<View>(null);
  const rightBottomRef = useRef<View>(null);

  // Define tutorial ID
  const TUTORIAL_ID = "tooltip-positions-tutorial";

  // Register tutorial steps
  useEffect(() => {
    const tutorialSteps: TutorialStep[] = [
      {
        id: "top-left",
        targetRef: topLeftRef,
        title: "Top Left Tooltip",
        description:
          "This tooltip is positioned at the top-left of the target element.",
        placement: "top-left",
        order: 1,
      },
      {
        id: "top-center",
        targetRef: topCenterRef,
        title: "Top Center Tooltip",
        description:
          "This tooltip is positioned at the top-center of the target element.",
        placement: "top-center",
        order: 2,
      },
      {
        id: "top-right",
        targetRef: topRightRef,
        title: "Top Right Tooltip",
        description:
          "This tooltip is positioned at the top-right of the target element.",
        placement: "top-right",
        order: 3,
      },
      {
        id: "bottom-left",
        targetRef: bottomLeftRef,
        title: "Bottom Left Tooltip",
        description:
          "This tooltip is positioned at the bottom-left of the target element.",
        placement: "bottom-left",
        order: 4,
      },
      {
        id: "bottom-center",
        targetRef: bottomCenterRef,
        title: "Bottom Center Tooltip",
        description:
          "This tooltip is positioned at the bottom-center of the target element.",
        placement: "bottom-center",
        order: 5,
      },
      {
        id: "bottom-right",
        targetRef: bottomRightRef,
        title: "Bottom Right Tooltip",
        description:
          "This tooltip is positioned at the bottom-right of the target element.",
        placement: "bottom-right",
        order: 6,
      },
      {
        id: "left-top",
        targetRef: leftTopRef,
        title: "Left Top Tooltip",
        description:
          "This tooltip is positioned at the left-top of the target element.",
        placement: "left-top",
        order: 7,
      },
      {
        id: "left-center",
        targetRef: leftCenterRef,
        title: "Left Center Tooltip",
        description:
          "This tooltip is positioned at the left-center of the target element.",
        placement: "left-center",
        order: 8,
      },
      {
        id: "left-bottom",
        targetRef: leftBottomRef,
        title: "Left Bottom Tooltip",
        description:
          "This tooltip is positioned at the left-bottom of the target element.",
        placement: "left-bottom",
        order: 9,
      },
      {
        id: "right-top",
        targetRef: rightTopRef,
        title: "Right Top Tooltip",
        description:
          "This tooltip is positioned at the right-top of the target element.",
        placement: "right-top",
        order: 10,
      },
      {
        id: "right-center",
        targetRef: rightCenterRef,
        title: "Right Center Tooltip",
        description:
          "This tooltip is positioned at the right-center of the target element.",
        placement: "right-center",
        order: 11,
      },
      {
        id: "right-bottom",
        targetRef: rightBottomRef,
        title: "Right Bottom Tooltip",
        description:
          "This tooltip is positioned at the right-bottom of the target element.",
        placement: "right-bottom",
        order: 12,
      },
    ];

    registerTutorial(TUTORIAL_ID, tutorialSteps);
  }, [registerTutorial]);

  // Create a button component for each position
  const PositionButton = ({
    position,
    reference,
  }: {
    position: TooltipPosition;
    reference: React.RefObject<View>;
  }) => (
    <TouchableOpacity
      style={styles.button}
      ref={reference}
      onPress={() => {
        // Reset and start the tutorial to show this specific position
        resetTutorial(TUTORIAL_ID);
        startTutorial(TUTORIAL_ID);
      }}
    >
      <Text style={styles.buttonText}>{position}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={styles.title}>Tooltip Position Examples</Text>
      <Text style={styles.description}>
        Tap any button to see a tooltip with the specified position.
      </Text>

      <View style={styles.grid}>
        <View style={styles.row}>
          <PositionButton position="top-left" reference={topLeftRef} />
          <PositionButton position="top-center" reference={topCenterRef} />
          <PositionButton position="top-right" reference={topRightRef} />
        </View>

        <View style={styles.row}>
          <PositionButton position="bottom-left" reference={bottomLeftRef} />
          <PositionButton
            position="bottom-center"
            reference={bottomCenterRef}
          />
          <PositionButton position="bottom-right" reference={bottomRightRef} />
        </View>

        <View style={styles.row}>
          <PositionButton position="left-top" reference={leftTopRef} />
          <PositionButton position="left-center" reference={leftCenterRef} />
          <PositionButton position="left-bottom" reference={leftBottomRef} />
        </View>

        <View style={styles.row}>
          <PositionButton position="right-top" reference={rightTopRef} />
          <PositionButton position="right-center" reference={rightCenterRef} />
          <PositionButton position="right-bottom" reference={rightBottomRef} />
        </View>
      </View>

      <TouchableOpacity
        style={styles.resetButton}
        onPress={() => {
          resetTutorial(TUTORIAL_ID);
          startTutorial(TUTORIAL_ID);
        }}
      >
        <Text style={styles.resetButtonText}>Start Full Tutorial</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  contentContainer: {
    padding: 20,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: "center",
    color: "#666",
  },
  grid: {
    width: "100%",
    marginBottom: 30,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
  },
  button: {
    backgroundColor: "#007AFF",
    padding: 15,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 12,
  },
  resetButton: {
    backgroundColor: "#4CAF50",
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginTop: 20,
  },
  resetButtonText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 16,
  },
});

export default TutorialExample;
