import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { FormField } from "../common/FormField";
import { LocationSelectionModal } from "../common/LocationSelectionModal";
import { TransportModeSelect } from "../common/TransportModeSelect";
import { TripDetails } from "../../lib/types";
import { useLocationSelection } from "../../lib/hooks/useLocationSelection";

interface LocationSectionProps {
  tripDetails: TripDetails;
  updateTripDetails: (updates: Partial<TripDetails>) => void;
}

/**
 * Location section component for trip details screen
 * Handles country and city selection
 */
export const LocationSection: React.FC<LocationSectionProps> = ({
  tripDetails,
  updateTripDetails,
}) => {

  const {
    isLoadingLocations,
    locationSuggestions,
    showCountryModal,
    showCityModal,
    activeField,
    searchQuery,
    setShowCountryModal,
    setShowCityModal,
    handleCountrySearch,
    handleCitySearch,
    selectCountry,
    selectCity,
    openCountryModal,
    openCityModal,
  } = useLocationSelection();

  return (
    <>
      <FormField label="Country" isRequired>
        <TouchableOpacity
          style={[styles.input, tripDetails.destination && styles.filledInput]}
          onPress={openCountryModal}
        >
          <Text
            style={
              tripDetails.destination
                ? styles.inputText
                : styles.placeholderText
            }
          >
            {tripDetails.destination || "Select Destination"}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </FormField>

      <FormField label="Arrival" isRequired>
        <View style={styles.row}>
          <View style={styles.cityContainer}>
            <TouchableOpacity
              style={[styles.input, tripDetails.arrivalCity && styles.filledInput]}
              onPress={() => openCityModal("arrival")}
            >
              <Text
                style={
                  tripDetails.arrivalCity
                    ? styles.inputText
                    : styles.placeholderText
                }
              >
                {tripDetails.arrivalCity || "City"}
              </Text>
              <Ionicons name="chevron-forward" size={20} color="#999" />
            </TouchableOpacity>
          </View>
          <View style={styles.modeContainer}>
            <TransportModeSelect
              value={tripDetails.arrivalMode}
              onChange={(mode) => updateTripDetails({ arrivalMode: mode })}
              placeholder="Mode"
            />
          </View>
        </View>
      </FormField>

      <FormField label="Departure" isRequired>
        <View style={styles.row}>
          <View style={styles.cityContainer}>
            <TouchableOpacity
              style={[
                styles.input,
                tripDetails.departureCity && styles.filledInput,
              ]}
              onPress={() => openCityModal("departure")}
            >
              <Text
                style={
                  tripDetails.departureCity
                    ? styles.inputText
                    : styles.placeholderText
                }
              >
                {tripDetails.departureCity || "City"}
              </Text>
              <Ionicons name="chevron-forward" size={20} color="#999" />
            </TouchableOpacity>
          </View>
          <View style={styles.modeContainer}>
            <TransportModeSelect
              value={tripDetails.departureMode}
              onChange={(mode) => updateTripDetails({ departureMode: mode })}
              placeholder="Mode"
            />
          </View>
        </View>
      </FormField>

      {/* Country Selection Modal */}
      <LocationSelectionModal
        visible={showCountryModal}
        onClose={() => setShowCountryModal(false)}
        title="Select Destination"
        placeholder="Search destinations..."
        onSearch={handleCountrySearch}
        searchQuery={searchQuery}
        isLoadingLocations={isLoadingLocations}
        locationSuggestions={locationSuggestions}
        onSelect={selectCountry}
      />

      {/* City Selection Modal */}
      <LocationSelectionModal
        visible={showCityModal}
        onClose={() => setShowCityModal(false)}
        title={`Select ${activeField === "arrival" ? "Arrival" : "Departure"} City`}
        placeholder="Search cities..."
        onSearch={handleCitySearch}
        searchQuery={searchQuery}
        isLoadingLocations={isLoadingLocations}
        locationSuggestions={locationSuggestions}
        onSelect={selectCity}
      />
    </>
  );
};

const styles = StyleSheet.create({
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },

  row: {
    flexDirection: "row",
    gap: 16,
  },
  flex1: {
    flex: 1,
  },
  cityContainer: {
    flex: 2,
  },
  modeContainer: {
    flex: 1,
  },
});
