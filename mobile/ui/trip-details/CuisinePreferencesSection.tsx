import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { CUISINE_PREFERENCES } from "../../lib/constants";
import { TripDetails } from "../../lib/types";
import { FormField } from "../common/FormField";
import SharedModal from "../common/Modal";

type CuisinePreference = (typeof CUISINE_PREFERENCES)[number];

interface CuisinePreferencesSectionProps {
  tripDetails: TripDetails;
  updateTripDetails: (updates: Partial<TripDetails>) => void;
}

/**
 * Cuisine preferences section component for trip details screen
 * Handles cuisine preferences selection
 */
export const CuisinePreferencesSection: React.FC<
  CuisinePreferencesSectionProps
> = ({ tripDetails, updateTripDetails }) => {
  const [showCuisinePreferencesModal, setShowCuisinePreferencesModal] =
    useState(false);

  return (
    <>
      <FormField label="Cuisine Preferences">
        <TouchableOpacity
          style={[
            styles.input,
            tripDetails.cuisinePreferences &&
              tripDetails.cuisinePreferences.length > 0 &&
              styles.filledInput,
          ]}
          onPress={() => {
            setShowCuisinePreferencesModal(true);
          }}
        >
          <Text
            style={
              tripDetails.cuisinePreferences &&
              tripDetails.cuisinePreferences.length > 0
                ? styles.inputText
                : styles.placeholderText
            }
          >
            {tripDetails.cuisinePreferences &&
            tripDetails.cuisinePreferences.length > 0
              ? `${tripDetails.cuisinePreferences.length} preferences selected`
              : "Select cuisine preferences"}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        {tripDetails.cuisinePreferences &&
          tripDetails.cuisinePreferences.length > 0 && (
            <View style={styles.selectedItemsContainer}>
              {tripDetails.cuisinePreferences.map((pref, index) => (
                <View key={index} style={styles.selectedItem}>
                  <Text style={styles.selectedItemText}>{pref}</Text>
                  <TouchableOpacity
                    onPress={() => {
                      const newPreferences = tripDetails.cuisinePreferences
                        ? tripDetails.cuisinePreferences.filter(
                            (_, i) => i !== index,
                          )
                        : [];
                      // If removing all preferences, add back "No Preference"
                      if (newPreferences.length === 0) {
                        updateTripDetails({
                          cuisinePreferences: ["No Preference"],
                        });
                      } else {
                        updateTripDetails({
                          cuisinePreferences: newPreferences,
                        });
                      }
                    }}
                    style={styles.removeItemButton}
                  >
                    <Ionicons name="close-circle" size={20} color="#FF3B30" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
      </FormField>

      <SharedModal
        visible={showCuisinePreferencesModal}
        onClose={() => setShowCuisinePreferencesModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Cuisine Preferences</Text>
              <TouchableOpacity
                onPress={() => setShowCuisinePreferencesModal(false)}
              >
                <Text style={styles.closeButton}>Done</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={CUISINE_PREFERENCES}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    tripDetails.cuisinePreferences &&
                      tripDetails.cuisinePreferences.includes(item) &&
                      styles.selectedOption,
                  ]}
                  onPress={() => {
                    if (!tripDetails.cuisinePreferences) {
                      updateTripDetails({
                        cuisinePreferences: [item],
                      });
                      return;
                    }

                    const isCurrentlySelected =
                      tripDetails.cuisinePreferences.includes(item);

                    if (isCurrentlySelected) {
                      // If removing the item
                      const newPreferences =
                        tripDetails.cuisinePreferences.filter(
                          (p: string) => p !== item,
                        ) as CuisinePreference[];
                      // If removing all preferences, add back "No Preference"
                      if (newPreferences.length === 0) {
                        updateTripDetails({
                          cuisinePreferences: ["No Preference"],
                        });
                        return;
                      }

                      updateTripDetails({
                        cuisinePreferences: newPreferences,
                      });
                    } else {
                      // If adding a new item
                      if (item === "No Preference") {
                        // If selecting "No Preference", remove all other preferences
                        updateTripDetails({
                          cuisinePreferences: ["No Preference"],
                        });
                      } else {
                        // If selecting any other option, remove "No Preference" and add the new option
                        const newPreferences = tripDetails.cuisinePreferences
                          .filter((p: string) => p !== "No Preference")
                          .concat(item) as CuisinePreference[];

                        updateTripDetails({
                          cuisinePreferences: newPreferences,
                        });
                      }
                      return;
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.optionText,
                      tripDetails.cuisinePreferences &&
                        tripDetails.cuisinePreferences.includes(item) &&
                        styles.selectedOptionText,
                    ]}
                  >
                    {item}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </SharedModal>
    </>
  );
};

const styles = StyleSheet.create({
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },
  selectedItemsContainer: {
    marginTop: 8,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  selectedItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectedItemText: {
    fontSize: 14,
    color: "#333",
    marginRight: 4,
  },
  removeItemButton: {
    padding: 2,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  closeButton: {
    fontSize: 16,
    color: "#007AFF",
  },
  optionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedOption: {
    backgroundColor: "#f0f0f0",
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
  selectedOptionText: {
    color: "#007AFF",
    fontWeight: "500",
  },
});
