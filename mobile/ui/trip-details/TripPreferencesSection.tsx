import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";
import { TRAVEL_TYPES } from "../../lib/constants";
import { TripDetails } from "../../lib/types";
import { FormField } from "../common/FormField";
import { Select } from "../common/Select";
import { PeopleCounter } from "../common/PeopleCounter";
import { BudgetSelector } from "../common/BudgetSelector";

type TravelType = (typeof TRAVEL_TYPES)[number];

interface TripPreferencesSectionProps {
  tripDetails: TripDetails;
  updateTripDetails: (updates: Partial<TripDetails>) => void;
}

/**
 * Trip preferences section component for trip details screen
 * Handles travel type, people, budget, and intensity
 */
export const TripPreferencesSection: React.FC<TripPreferencesSectionProps> = ({
  tripDetails,
  updateTripDetails,
}) => {
  return (
    <>
      <FormField label="Type of Travel" isRequired>
        <Select
          value={tripDetails.travelType}
          options={TRAVEL_TYPES.map((type) => ({ label: type, value: type }))}
          onChange={(value) => {
            updateTripDetails({
              travelType: value as TravelType,
            });
          }}
          placeholder="Select travel type"
        />
      </FormField>

      <FormField label="Number of People" isRequired>
        <View style={styles.row}>
          <View style={styles.flex1}>
            <PeopleCounter
              label="Adults"
              value={tripDetails.people.adults}
              minValue={1}
              onIncrement={() =>
                updateTripDetails({
                  people: {
                    ...tripDetails.people,
                    adults: tripDetails.people.adults + 1,
                  },
                })
              }
              onDecrement={() =>
                updateTripDetails({
                  people: {
                    ...tripDetails.people,
                    adults: Math.max(1, tripDetails.people.adults - 1),
                  },
                })
              }
            />
          </View>
          <View style={styles.flex1}>
            <PeopleCounter
              label="Children"
              value={tripDetails.people.children}
              minValue={0}
              onIncrement={() =>
                updateTripDetails({
                  people: {
                    ...tripDetails.people,
                    children: tripDetails.people.children + 1,
                  },
                })
              }
              onDecrement={() =>
                updateTripDetails({
                  people: {
                    ...tripDetails.people,
                    children: Math.max(0, tripDetails.people.children - 1),
                  },
                })
              }
            />
          </View>
        </View>
      </FormField>

      <FormField label="Budget Category" isRequired>
        <BudgetSelector
          value={tripDetails.budget}
          onValueChange={(value) => updateTripDetails({ budget: value })}
        />
      </FormField>

      <FormField label="Trip Intensity" isRequired>
        <View style={styles.intensityContainer}>
          <Text style={styles.intensityValue}>{tripDetails.intensity}</Text>
          <Slider
            style={styles.intensitySlider}
            minimumValue={1}
            maximumValue={10}
            step={1}
            value={tripDetails.intensity}
            onValueChange={(value) => updateTripDetails({ intensity: value })}
            minimumTrackTintColor="#2196F3"
            maximumTrackTintColor="#ddd"
          />
          <View style={styles.intensityLabels}>
            <View style={styles.intensityLabelContainer}>
              <Ionicons name="bed-outline" size={20} color="#666" />
              <Text style={styles.intensityLabelText}>Chill</Text>
            </View>
            <View style={styles.intensityLabelContainer}>
              <Ionicons name="rocket-outline" size={20} color="#666" />
              <Text style={styles.intensityLabelText}>Intense</Text>
            </View>
          </View>
        </View>
      </FormField>
    </>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    gap: 16,
  },
  flex1: {
    flex: 1,
  },
  intensityContainer: {
    padding: 16,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
  },
  intensityValue: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  intensitySlider: {
    width: "100%",
    height: 40,
  },
  intensityLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  intensityLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  intensityLabelText: {
    fontSize: 14,
    color: "#666",
  },
});
