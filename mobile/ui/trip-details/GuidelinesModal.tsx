import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { mediumHapticFeedback } from "../../lib/utils/haptics";

interface GuidelinesModalProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * Guidelines Modal component for trip details screen
 * Displays informative guidelines to help users fill the form with real data
 */
export const GuidelinesModal: React.FC<GuidelinesModalProps> = ({
  visible,
  onClose,
}) => {
  const handleClose = () => {
    mediumHapticFeedback();
    onClose();
  };

  const guidelines = [
    {
      icon: "location" as const,
      title: "Must-Visit Cities",
      description: "Add specific cities you want to explore.",
    },
    {
      icon: "star" as const,
      title: "Your Interests",
      description: "Select activities you actually enjoy.",
    },
    {
      icon: "people" as const,
      title: "Travel Type",
      description: "Choose your travel style and group size.",
    },
    {
      icon: "restaurant" as const,
      title: "Food Preferences",
      description: "Share your cuisine and dietary needs.",
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.title}>Quick Tips</Text>
            <Text style={styles.subtitle}>
              Fill these key fields for better results
            </Text>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Guidelines List */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {guidelines.map((guideline, index) => (
            <View key={index} style={styles.guidelineItem}>
              <View style={styles.iconContainer}>
                <Ionicons
                  name={guideline.icon}
                  size={20}
                  color="#2196F3"
                />
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.guidelineTitle}>{guideline.title}</Text>
                <Text style={styles.guidelineDescription}>
                  {guideline.description}
                </Text>
              </View>
            </View>
          ))}

          {/* Bottom Message */}
          <View style={styles.bottomMessage}>
            <View style={styles.tipContainer}>
              <Ionicons name="bulb" size={20} color="#FF9500" />
              <Text style={styles.tipText}>
                More details = better recommendations!
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Footer Button */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.continueButton} onPress={handleClose}>
            <Text style={styles.continueButtonText}>Got it, let's start!</Text>
            <Ionicons name="arrow-forward" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerContent: {
    flex: 1,
    paddingRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#333",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    lineHeight: 22,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  guidelineItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    paddingVertical: 4,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#E3F2FD",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  guidelineTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  guidelineDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  bottomMessage: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  tipContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: "#FFF8E1",
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#FF9500",
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: "#E65100",
    fontWeight: "500",
    marginLeft: 12,
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  continueButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#2196F3",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  continueButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
