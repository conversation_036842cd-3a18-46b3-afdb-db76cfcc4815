import React from "react";
import { View, StyleSheet, Text } from "react-native";
import { TripDetails } from "../../lib/types";
import { FormField } from "../common/FormField";
import { DateTimePicker } from "../common/DateTimePicker";

interface DateTimeSectionProps {
  tripDetails: TripDetails;
  updateTripDetails: (updates: Partial<TripDetails>) => void;
  arrivalDate: Date;
  departureDate: Date;
  wakeUpTime: Date;
  sleepTime: Date;
  formatTimeToHHMM: (date: Date) => string;
  dateConstraints?: {
    minArrivalDate?: Date;
    maxArrivalDate?: Date;
    minDepartureDate?: Date;
    maxDepartureDate?: Date;
  };
  tripDurationValidation?: {
    isValid: boolean;
    error?: string;
    maxDays: number;
  };
}

/**
 * DateTime section component for trip details screen
 * Handles arrival, departure, and sleep schedule date/time selection
 */
export const DateTimeSection: React.FC<DateTimeSectionProps> = ({
  tripDetails,
  updateTripDetails,
  arrivalDate,
  departureDate,
  wakeUpTime,
  sleepTime,
  formatTimeToHHMM,
  dateConstraints,
  tripDurationValidation,
}) => {
  return (
    <>
      <FormField label="Arrival" isRequired>
        <View style={styles.row}>
          <View style={styles.flex1}>
            <DateTimePicker
              value={arrivalDate}
              onChange={(date) => {
                // Format date as YYYY-MM-DD to avoid timezone issues
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateString = `${year}-${month}-${day}T00:00:00.000Z`;

                console.log('Arrival date selected:', date.toString());
                console.log('Formatted arrival date:', dateString);

                updateTripDetails({
                  startDate: dateString,
                });
              }}
              mode="date"
              label="Date"
              minimumDate={dateConstraints?.minArrivalDate}
              maximumDate={dateConstraints?.maxArrivalDate}
            />
          </View>
          <View style={styles.flex1}>
            <DateTimePicker
              value={new Date(`2000-01-01T${tripDetails.arrivalTime || '12:00'}:00`)}
              onChange={(date) => {
                updateTripDetails({
                  arrivalTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Time"
            />
          </View>
        </View>
      </FormField>

      <FormField label="Departure" isRequired>
        <View style={styles.row}>
          <View style={styles.flex1}>
            <DateTimePicker
              value={departureDate}
              onChange={(date) => {
                // Format date as YYYY-MM-DD to avoid timezone issues
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateString = `${year}-${month}-${day}T00:00:00.000Z`;

                console.log('Departure date selected:', date.toString());
                console.log('Formatted departure date:', dateString);

                updateTripDetails({
                  endDate: dateString,
                });
              }}
              mode="date"
              label="Date"
              minimumDate={dateConstraints?.minDepartureDate}
              maximumDate={dateConstraints?.maxDepartureDate}
              error={tripDurationValidation?.isValid === false ? tripDurationValidation.error : undefined}
            />
          </View>
          <View style={styles.flex1}>
            <DateTimePicker
              value={new Date(`2000-01-01T${tripDetails.departureTime || '12:00'}:00`)}
              onChange={(date) => {
                updateTripDetails({
                  departureTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Time"
            />
          </View>
        </View>
        {tripDurationValidation && !tripDurationValidation.isValid && (
          <Text style={styles.errorText}>{tripDurationValidation.error}</Text>
        )}
      </FormField>



      <FormField label="Sleep Schedule" isRequired>
        <View style={styles.row}>
          <View style={styles.flex1}>
            <DateTimePicker
              value={wakeUpTime}
              onChange={(date) => {
                updateTripDetails({
                  wakeUpTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Wake Up Time"
            />
          </View>
          <View style={styles.flex1}>
            <DateTimePicker
              value={sleepTime}
              onChange={(date) => {
                updateTripDetails({
                  sleepTime: formatTimeToHHMM(date),
                });
              }}
              mode="time"
              label="Sleep Time"
            />
          </View>
        </View>
      </FormField>
    </>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    gap: 16,
  },
  flex1: {
    flex: 1,
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginTop: 8,
    fontWeight: "500",
  },
});
