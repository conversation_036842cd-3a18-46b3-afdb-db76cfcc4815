import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { TripDetails } from "../../lib/types";
import { FormField } from "../common/FormField";
import { TextInput } from "../common/TextInput";
import { LocationSelectionModal } from "../common/LocationSelectionModal";
import { useLocationSelection } from "../../lib/hooks/useLocationSelection";

interface AdditionalOptionsSectionProps {
  tripDetails: TripDetails;
  updateTripDetails: (updates: Partial<TripDetails>) => void;
}

/**
 * Additional options section component for trip details screen
 * <PERSON>les must visit cities and additional requirements
 */
export const AdditionalOptionsSection: React.FC<
  AdditionalOptionsSectionProps
> = ({ tripDetails, updateTripDetails }) => {
  const {
    isLoadingLocations,
    locationSuggestions,
    showMustVisitSheet,
    searchQuery,
    setShowMustVisitSheet,
    handleCitySearch,
    openMustVisitModal,
    addMustVisitCity,
    removeMustVisitCity,
  } = useLocationSelection();

  return (
    <>
      <FormField label="Must Visit Cities">
        <TouchableOpacity
          style={[
            styles.input,
            tripDetails.mustVisitCities &&
              tripDetails.mustVisitCities.length > 0 &&
              styles.filledInput,
          ]}
          onPress={openMustVisitModal}
        >
          <Text
            style={
              tripDetails.mustVisitCities &&
              tripDetails.mustVisitCities.length > 0
                ? styles.inputText
                : styles.placeholderText
            }
          >
            {tripDetails.mustVisitCities &&
            tripDetails.mustVisitCities.length > 0
              ? `${tripDetails.mustVisitCities.length} cities selected`
              : "Add cities to visit"}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        {tripDetails.mustVisitCities &&
          tripDetails.mustVisitCities.length > 0 && (
            <View style={styles.selectedItemsContainer}>
              {tripDetails.mustVisitCities.map((city, index) => (
                <View key={index} style={styles.selectedItem}>
                  <Text style={styles.selectedItemText}>{city}</Text>
                  <TouchableOpacity
                    onPress={() => removeMustVisitCity(city)}
                    style={styles.removeItemButton}
                  >
                    <Ionicons name="close-circle" size={20} color="#FF3B30" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
      </FormField>

      <FormField label="Additional Requirements">
        <TextInput
          value={tripDetails.additionalRequirements || ""}
          onChangeText={(text) =>
            updateTripDetails({ additionalRequirements: text })
          }
          placeholder="Enter any additional requirements"
          multiline
          numberOfLines={4}
        />
      </FormField>

      <LocationSelectionModal
        visible={showMustVisitSheet}
        onClose={() => setShowMustVisitSheet(false)}
        title="Add Must Visit Cities"
        placeholder="Search cities..."
        onSearch={handleCitySearch}
        searchQuery={searchQuery}
        isLoadingLocations={isLoadingLocations}
        locationSuggestions={locationSuggestions}
        selectedItems={tripDetails.mustVisitCities || []}
        onSelect={addMustVisitCity}
        onRemove={removeMustVisitCity}
      />
    </>
  );
};

const styles = StyleSheet.create({
  input: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#fff",
  },
  filledInput: {
    backgroundColor: "#f8f9fa",
    borderColor: "#007AFF",
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  placeholderText: {
    flex: 1,
    fontSize: 16,
    color: "#999",
  },
  selectedItemsContainer: {
    marginTop: 8,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  selectedItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectedItemText: {
    fontSize: 14,
    color: "#333",
    marginRight: 4,
  },
  removeItemButton: {
    padding: 2,
  },
});
