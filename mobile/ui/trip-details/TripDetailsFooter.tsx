import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface TripDetailsFooterProps {
  isFormValid: boolean;
  onNext: () => void;
}

/**
 * Footer component for trip details screen
 * Contains the next button
 */
export const TripDetailsFooter: React.FC<TripDetailsFooterProps> = ({
  isFormValid,
  onNext,
}) => {
  return (
    <View style={styles.bottomButtonContainer}>
      <TouchableOpacity
        style={[styles.nextButton, !isFormValid && styles.nextButtonDisabled]}
        onPress={onNext}
        disabled={!isFormValid}
      >
        <Text
          style={[
            styles.nextButtonText,
            !isFormValid && styles.nextButtonTextDisabled,
          ]}
        >
          Next
        </Text>
        <Ionicons
          name="arrow-forward"
          size={20}
          color={!isFormValid ? "#999" : "#fff"}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    paddingTop: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  nextButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#2196F3",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
    width: "100%",
  },
  nextButtonDisabled: {
    backgroundColor: "#E0E0E0",
  },
  nextButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  nextButtonTextDisabled: {
    color: "#999",
  },
});
