import { Ionicons } from "@expo/vector-icons";
import React, { forwardRef, useMemo, useRef, useCallback, useState } from "react";
import {
  StyleSheet,
  View,
  Animated,
  TouchableOpacity,
  GestureResponderEvent,
} from "react-native";
import {
  MarkerView,
  MapMarkerProps as RNMapMarkerProps,
} from "./MapUtils";
import { Activity } from "../../lib/types";
import { getActivityIcon } from "../trips/TripViewUtils";
import {
  lightHapticFeedback,
  selectionHapticFeedback,
  successHapticFeedback,
} from "../../lib/utils/haptics";

interface MapMarkerProps extends Pick<RNMapMarkerProps, "onPress"> {
  children?: React.ReactNode;
  activity: Activity;
  onPress: () => void;
}

const pathColor = "#007AFF";

const MapMarker = React.memo(
  forwardRef<any, MapMarkerProps>(
    (
      {
        activity,
        onPress,
        children,
      },
      ref,
    ) => {
      // Memoize the coordinate to prevent re-renders (Mapbox uses [lng, lat] format)
      const coordinate = useMemo(
        () => [
          activity.coordinates?.lng || 0,
          activity.coordinates?.lat || 0,
        ],
        [activity.coordinates?.lat, activity.coordinates?.lng],
      );

      const isCompleted = useMemo(() => {
        return activity.completed;
      }, [activity.completed]);

      // Memoize the icon name to prevent re-renders
      const iconName = useMemo(() => {
        return getActivityIcon(activity.type || "activity");
      }, [activity.type]);

      // Ensure activity has minimum required data to prevent rendering issues
      const isValidActivity = activity.id && typeof activity.id === 'string';


      // Handle marker press to show callout
      const handleMarkerPress = useCallback(
        (e: any) => {
          // Stop event propagation
          e?.stopPropagation?.();

          // Trigger selection haptic feedback when pressing a marker
          selectionHapticFeedback();

          onPress?.();
        },
        [onPress],
      );

      const handleLongPress = useCallback(
        (e: any) => {
          // Stop event propagation
          console.log("Long press");
        },
        [],
      );

      if (!isValidActivity) return null;


      // Return null if no coordinates are available or if activity data is invalid
      if (!activity.coordinates || !activity.id) return null;

      return (
        <MarkerView
          id={`${activity.id}-${activity.name}-${isCompleted ? 'completed' : 'incomplete'}`}
          coordinate={coordinate}
          anchor={{ x: 0.5, y: 0.5 }}
        >
          <TouchableOpacity
            style={[
              styles.markerContainer,
              { borderColor: pathColor },
              isCompleted && styles.completedMarkerContainer,
            ]}
            onLongPress={handleLongPress}
            onPress={handleMarkerPress}
            activeOpacity={0.8}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // Increase touch area
          >
            {/* Circular progress animation for long press */}
            <View
              style={[
                styles.circleProgress,
                {
                  borderColor: pathColor,
                },
              ]}
            />


            {/* Completed overlay */}
            {isCompleted && (
              <View
                style={[
                  styles.completedOverlay,
                  { backgroundColor: pathColor },
                ]}
              />
            )}


            {/* Activity icon */}
            <Ionicons
              name={iconName as any}
              size={16}
              color={isCompleted ? "#fff" : pathColor}
              style={{ position: "relative", zIndex: 2 }}
            />

            {/* Checkmark for completed activities */}
            {isCompleted && (
              <View style={styles.checkmarkContainer}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color="#4CAF50"
                  style={styles.checkmark}
                />
              </View>
            )}
          </TouchableOpacity>
          {children}
        </MarkerView>
      );
    },
  ),
  (prevProps, nextProps) => {
    // Custom comparison function for React.memo
    // Only re-render if these specific props change
    return (
      prevProps.activity.completed === nextProps.activity.completed &&
      prevProps.activity.id === nextProps.activity.id &&
      prevProps.activity.coordinates?.lat ===
      nextProps.activity.coordinates?.lat &&
      prevProps.activity.coordinates?.lng ===
      nextProps.activity.coordinates?.lng
    );
  },
);

const styles = StyleSheet.create({
  longPressOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 10,
    backgroundColor: "rgba(76, 175, 80, 0.3)", // Greenish color
  },
  markerContainer: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 8,
    borderWidth: 2,
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    // iOS-specific fixes
    // overflow: 'visible',
    // zIndex: 1,
    width: 40,
    height: 40,
  },
  completedMarkerContainer: {
    backgroundColor: "#4CAF50", // Green background for completed activities
    borderColor: "#388E3C", // Darker green border
  },
  progressOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 10,
  },
  completedOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 10,
    opacity: 0.5,
  },
  completionAnimation: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 10,
    backgroundColor: "rgba(76, 175, 80, 0.3)", // Light green
  },
  // New circular progress style for the border animation
  circleProgress: {
    position: "absolute",
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 14, // Slightly larger than the container
    borderWidth: 2,
    borderColor: "#4CAF50",
  },
  checkmarkContainer: {
    position: "absolute",
    top: -6,
    right: -6,
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 0,
  },
  checkmark: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  callout: {
    backgroundColor: "transparent",
    borderWidth: 0,
    width: 300, // Set a fixed width for the callout
    height: "auto", // Allow height to adjust based on content
    // Add defensive text styling to prevent fontSize issues
    fontSize: 14, // Ensure a default fontSize is set
  },
});

MapMarker.displayName = "MapMarker";

export default MapMarker;
