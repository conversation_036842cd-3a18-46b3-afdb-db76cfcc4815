import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Platform,
  LayoutChangeEvent,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Activity } from "../../lib/types";
import { MapsService } from "../../lib/services/maps.service";

// Simple error boundary wrapper for ActivityCallout
const SafeActivityCallout: React.FC<ActivityCalloutProps> = ({
  activity,
  onLayout,
  onToggleCompletion
}) => {
  try {
    return (
      <ActivityCalloutContent
        activity={activity}
        onLayout={onLayout}
        onToggleCompletion={onToggleCompletion}
      />
    );
  } catch (error) {
    console.error("Error rendering ActivityCallout:", error);
    return (
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>Activity</Text>
          <Text style={styles.location}>Unable to display activity details</Text>
        </View>
      </View>
    );
  }
};

interface ActivityCalloutProps {
  activity: Activity;
  onLayout?: (height: number) => void;
  onToggleCompletion?: (activityId: string, isCompleted: boolean) => void;
}

const ActivityCalloutContent: React.FC<ActivityCalloutProps> = ({
  activity,
  onLayout,
  onToggleCompletion,
}) => {
  // Ensure all text values are properly defined to prevent fontSize errors
  const safeActivity = {
    ...activity,
    name: activity.name || "Untitled Activity",
    description: activity.description || "No description available",
    location: activity.location || "Location not specified",
    startTime: activity.startTime || "",
    endTime: activity.endTime || "",
    type: activity.type || "activity",
  };

  const formatCost = (cost?: number) => {
    if (!cost) return "Free";
    return `$${cost.toFixed(2)}`;
  };

  const formatDuration = (duration?: string) => {
    if (!duration) return null;
    return duration;
  };

  const getActivityTypeIcon = (type?: string, mealType?: string) => {
    if (type === "meal") {
      switch (mealType) {
        case "breakfast": return "🍳";
        case "lunch": return "🥗";
        case "dinner": return "🍽️";
        default: return "🍴";
      }
    }
    return "📍";
  };

  const formatTags = (tags?: string[]) => {
    if (!tags || tags.length === 0) return null;
    return tags.slice(0, 3).join(" • "); // Show max 3 tags
  };

  // Check if activity has location data for maps
  const hasLocationData = !!(activity.coordinates || activity.location);
  const isCompleted = activity.completed || false;

  const handleLayout = (event: LayoutChangeEvent) => {
    const height = event.nativeEvent.layout.height;
    onLayout?.(height); // No extra height needed for bottom positioning
  };

  const handleOpenMaps = () => {
    if (hasLocationData) {
      MapsService.showMapsAppSelector(activity);
    }
  };

  const handleToggleCompletion = () => {
    if (onToggleCompletion) {
      onToggleCompletion(activity.id, isCompleted);
    }
  };

  return (
    <View style={styles.container} onLayout={handleLayout}>
      <View style={styles.contentContainer}>
        {/* Header with title, type icon and completion status */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.typeIcon}>
              {getActivityTypeIcon(activity.type, activity.mealType)}
            </Text>
            <Text style={[styles.title, isCompleted && styles.completedText]} numberOfLines={2}>
              {safeActivity.name}
            </Text>
          </View>
          {isCompleted && (
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
          )}
        </View>

        {/* Activity type and meal info */}
        {(activity.type === "meal" && activity.restaurant) && (
          <Text style={styles.restaurant} numberOfLines={1}>
            🏪 {activity.restaurant}
          </Text>
        )}

        {/* Location */}
        <Text style={styles.location} numberOfLines={1}>
          📍 {safeActivity.location}
        </Text>

        {/* Description */}
        {safeActivity.description && safeActivity.description !== "No description available" && (
          <Text style={styles.description} numberOfLines={3}>
            {safeActivity.description}
          </Text>
        )}

        {/* Time, Duration, Cost, Rating row */}
        <View style={styles.infoRow}>
          <View style={styles.timeContainer}>
            <Text style={styles.time}>
              {safeActivity.startTime || safeActivity.endTime
                ? `${safeActivity.startTime}${safeActivity.endTime ? ` - ${safeActivity.endTime}` : ""}`
                : "Time not specified"}
            </Text>
            {formatDuration(activity.duration) && (
              <Text style={styles.duration}>⏱️ {formatDuration(activity.duration)}</Text>
            )}
          </View>
          <View style={styles.rightInfo}>
            <Text style={styles.cost}>{formatCost(activity.cost)}</Text>
            {activity.rating && (
              <Text style={styles.rating}>★ {activity.rating.toFixed(1)}</Text>
            )}
          </View>
        </View>

        {/* Tags */}
        {formatTags(activity.tags) && (
          <Text style={styles.tags} numberOfLines={1}>
            🏷️ {formatTags(activity.tags)}
          </Text>
        )}

        {/* Action buttons */}
        <View style={styles.buttonContainer}>
          {hasLocationData && (
            <TouchableOpacity style={styles.mapsButton} onPress={handleOpenMaps}>
              <Ionicons name="map" size={16} color="#007AFF" />
              <Text style={styles.buttonText}>Open in Maps</Text>
            </TouchableOpacity>
          )}

          {onToggleCompletion && (
            <TouchableOpacity
              style={[styles.completionButton, isCompleted && styles.completedButton]}
              onPress={handleToggleCompletion}
            >
              <Ionicons
                name={isCompleted ? "checkmark-circle" : "ellipse-outline"}
                size={16}
                color={isCompleted ? "#fff" : "#4CAF50"}
              />
              <Text style={[
                styles.buttonText,
                !isCompleted && { color: "#4CAF50" },
                isCompleted && styles.completedButtonText
              ]}>
                {isCompleted ? "Completed" : "Mark Done"}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#ddd",
    borderBottomWidth: 0,
    width: "100%",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: -2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32, // Extra padding for bottom positioning and safe area
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: 8,
  },
  typeIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  title: {
    fontWeight: "bold",
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  completedText: {
    textDecorationLine: "line-through",
    color: "#888",
  },
  restaurant: {
    fontSize: 14,
    color: "#8B4513",
    marginBottom: 6,
    fontWeight: "500",
  },
  location: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
    fontWeight: "500",
  },
  description: {
    fontSize: 13,
    color: "#555",
    marginBottom: 8,
    lineHeight: 18,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  timeContainer: {
    flex: 1,
  },
  time: {
    fontSize: 13,
    color: "#666",
    fontWeight: "500",
    marginBottom: 2,
  },
  duration: {
    fontSize: 12,
    color: "#888",
    fontWeight: "400",
  },
  rightInfo: {
    alignItems: "flex-end",
    gap: 2,
  },
  cost: {
    fontSize: 13,
    color: "#2E8B57",
    fontWeight: "600",
  },
  rating: {
    fontSize: 13,
    color: "#FFD700",
    fontWeight: "600",
  },
  tags: {
    fontSize: 12,
    color: "#666",
    marginBottom: 12,
    fontStyle: "italic",
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 8,
  },
  mapsButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F0F8FF",
    borderWidth: 1,
    borderColor: "#007AFF",
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 6,
  },
  completionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F0FFF0",
    borderWidth: 1,
    borderColor: "#4CAF50",
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 6,
  },
  completedButton: {
    backgroundColor: "#4CAF50",
    borderColor: "#4CAF50",
  },
  buttonText: {
    fontSize: 13,
    fontWeight: "600",
    color: "#007AFF",
  },
  completedButtonText: {
    color: "#fff",
  },
});

// Export the safe wrapper as the main component
const ActivityCallout = SafeActivityCallout;

export default ActivityCallout;
