import React, {
  useRef,
  useCallback,
  useState,
  useEffect,
} from "react";
import { StyleSheet } from "react-native";
import type { Region } from "./MapUtils";
import { Activity } from "../../lib/types";
import { Animated as RNAnimated } from "react-native";
import SharedMapView, { SharedMapViewRef } from "./SharedMapView";

interface TripMapViewProps {
  mapRegion: Region;
  onRegionChangeComplete: (region: Region) => void;
  activities: Activity[];
  completedActivities: Set<string>;
  activityInProgress: string | null;
  progressAnimation: RNAnimated.Value;
  completionAnimation?: RNAnimated.Value;
  tripId: string;
  selectedDay: number;
  handleLongPress: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => (event: any) => void;
  handlePressOut: (activityId: string, isCompleted: boolean) => () => void;
  toggleActivityCompletion: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => void;
}

const TripMapView: React.FC<TripMapViewProps> = ({
  mapRegion,
  onRegionChangeComplete,
  activities,
  completedActivities,
  activityInProgress,
  progressAnimation,
  completionAnimation,
  tripId,
  selectedDay,
  handleLongPress,
  handlePressOut,
  toggleActivityCompletion,
}) => {
  const mapRef = useRef<SharedMapViewRef>(null);
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationTimeoutRef = useRef<number | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // Track if we've already auto-fitted on initial load
  const [hasAutoFitted, setHasAutoFitted] = useState(false);

  // Auto-fit only on first load when activities are available
  useEffect(() => {
    if (activities.length > 0 && mapRef.current && !hasAutoFitted) {
      // Small delay to ensure map is ready
      const timer = setTimeout(() => {
        mapRef.current?.fitToElements();
        setHasAutoFitted(true);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [activities, hasAutoFitted]);

  // Reset auto-fit flag when mapRegion changes (new screen/filter)
  useEffect(() => {
    setHasAutoFitted(false);
  }, [mapRegion]);

  // Handle activity selection - center on activity and show callout
  const handleActivitySelect = useCallback((activityId: string) => {
    // Prevent multiple calls for the same activity or while animating
    if (selectedActivityId === activityId || isAnimating) {
      return;
    }

    const activity = activities.find(a => a.id === activityId);
    if (!activity?.coordinates || !mapRef.current || !mapRegion) {
      return;
    }

    // Clear any existing timeout
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
      animationTimeoutRef.current = null;
    }

    // Set animation state
    setIsAnimating(true);
    setSelectedActivityId(activityId);

    const region: Region = {
      latitude: activity.coordinates.lat,
      longitude: activity.coordinates.lng,
      latitudeDelta: mapRegion.latitudeDelta, // Keep current zoom level
      longitudeDelta: mapRegion.longitudeDelta, // Keep current zoom level
    };

    // First animate to the region
    mapRef.current.animateToRegion(region, 1000); // 1 second animation

    // Show the callout after animation completes
    animationTimeoutRef.current = setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.showCalloutForActivity(activityId);
      }
      setIsAnimating(false);
      animationTimeoutRef.current = null;
    }, 1200) as unknown as number; // Slightly longer than animation duration
  }, [activities, mapRegion, selectedActivityId, isAnimating]);

  // Handle region change to reset selection state
  const handleRegionChangeComplete = useCallback((region: Region) => {
    onRegionChangeComplete(region);
    // Reset selection state when user manually changes region
    if (selectedActivityId && !isAnimating) {
      setSelectedActivityId(null);
    }
  }, [onRegionChangeComplete, selectedActivityId, isAnimating]);

  return (
    <SharedMapView
      ref={mapRef}
      mapRegion={mapRegion}
      onRegionChangeComplete={handleRegionChangeComplete}
      activities={activities}
      completedActivities={completedActivities}
      activityInProgress={activityInProgress}
      progressAnimation={progressAnimation}
      completionAnimation={completionAnimation}
      tripId={tripId}
      selectedDay={selectedDay}
      handleLongPress={handleLongPress}
      handlePressOut={handlePressOut}
      toggleActivityCompletion={toggleActivityCompletion}
      onMarkerPress={handleActivitySelect}
      showPolylines={true} // Show connecting lines for trip view
    />
  );
};



const styles = StyleSheet.create({
  map: {
    flex: 1,
  },
});

export default TripMapView;
