import { Platform } from 'react-native';

// Conditional imports for @rnmapbox/maps to avoid web platform issues
let MapView: any = null;
let Camera: any = null;
let PointAnnotation: any = null;
let MarkerView: any = null;
let Callout: any = null;
let ShapeSource: any = null;
let LineLayer: any = null;
let Mapbox: any = null;

if (Platform.OS === 'android' || Platform.OS === 'ios') {
  Mapbox = require('@rnmapbox/maps').default;
  MapView = Mapbox.MapView;
  Camera = Mapbox.Camera;
  PointAnnotation = Mapbox.PointAnnotation;
  MarkerView = Mapbox.MarkerView;
  Callout = Mapbox.Callout;
  ShapeSource = Mapbox.ShapeSource;
  LineLayer = Mapbox.LineLayer;
  Mapbox.setAccessToken('pk.eyJ1IjoidmlndWl6ZSIsImEiOiJjbWI4M2ptZXUwMXNsMnFzOHdtZnN0c2h6In0.-M7ojVSIReGXaC3sbWIz8Q');
}

// Export all components
export {
  MapView,
  Camera,
  PointAnnotation,
  MarkerView,
  Callout,
  ShapeSource,
  LineLayer,
  Mapbox,
};

// Define types for compatibility
export interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface MapMarkerProps {
  coordinate: {
    latitude: number;
    longitude: number;
  };
  onPress?: () => void;
}
