import React from "react";
import { StyleSheet, Text, TouchableOpacity } from "react-native";

interface DayButtonProps {
  day: number;
  itineraryColor: string;
  isSelected: boolean;
  onSelect: () => void;
}

const DayButton: React.FC<DayButtonProps> = ({
  day,
  itineraryColor,
  isSelected,
  onSelect,
}) => {
  const isSelectAll = day === -1;

  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: isSelected ? itineraryColor : "white" },
      ]}
      onPress={onSelect}
    >
      <Text
        style={[styles.text, { color: isSelected ? "white" : itineraryColor }]}
      >
        {isSelectAll ? "All" : `Day ${day}`}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flex: 1,
    padding: 10,
    borderRadius: 10,
  },
  text: {
    fontSize: 14,
    fontWeight: "500",
  },
});

export default DayButton;
