import React, { useState, useEffect, useCallback } from "react";
import { View, StyleSheet, ActivityIndicator } from "react-native";
import { Region } from "./MapUtils";
import { Activity } from "../../lib/types";
import SharedMapView from "./SharedMapView";
import { MapRegionService } from "../../lib/services/map-region.service";
import { useActivityCompletion } from "../trips/ActivityManager";
import Animated from "react-native-reanimated";
import { ErrorState } from "../common/ErrorState";

interface MapContainerProps {
  tripId: string;
  selectedDay?: number;
  activities: Activity[];
  initialRegion?: Region;
  showPolylines?: boolean;
  onRegionChange?: (region: Region) => void;
  onMarkerPress?: (activityId: string) => void;
  onMapPress?: () => void;
  style?: any;
  children?: React.ReactNode;
}

const MapContainer: React.FC<MapContainerProps> = ({
  tripId,
  selectedDay = 0,
  activities,
  initialRegion,
  showPolylines = true,
  onRegionChange,
  onMarkerPress,
  onMapPress,
  style,
  children,
}) => {
  const [mapRegion, setMapRegion] = useState<Region | null>(
    initialRegion || null,
  );
  const [loading, setLoading] = useState(!initialRegion);
  const [error, setError] = useState<string | null>(null);

  const mapRegionService = MapRegionService.getInstance();

  const {
    completedActivities,
    activityInProgress,
    progressAnimation,
    completionAnimation,
    handleLongPress,
    handlePressOut,
    toggleActivityCompletion,
  } = useActivityCompletion({
    tripId,
    onTripUpdate: () => { }, // No need to reload the trip here
  });

  // Fetch map region from backend if not provided
  useEffect(() => {
    const fetchMapRegion = async () => {
      if (initialRegion) {
        setMapRegion(initialRegion);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let region;
        if (selectedDay > 0) {
          region = await mapRegionService.getDailyMapRegion(
            tripId,
            selectedDay,
          );
        } else {
          region = await mapRegionService.getTripMapRegion(tripId);
        }

        if (region) {
          setMapRegion(region);
        } else {
          // Default region if none is returned
          setMapRegion({
            latitude: 0,
            longitude: 0,
            latitudeDelta: 50,
            longitudeDelta: 50,
          });
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load map region",
        );
        console.error("Error fetching map region:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchMapRegion();
  }, [tripId, selectedDay, initialRegion, mapRegionService]);

  const handleRegionChangeComplete = useCallback(
    (region: Region) => {
      setMapRegion(region);
      if (onRegionChange) {
        onRegionChange(region);
      }
    },
    [onRegionChange],
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer, style]}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, style]}>
        <ErrorState error={error} onRetry={() => setError(null)} />
      </View>
    );
  }

  if (!mapRegion) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <SharedMapView
        mapRegion={mapRegion}
        onRegionChangeComplete={handleRegionChangeComplete}
        activities={activities}
        completedActivities={completedActivities}
        activityInProgress={activityInProgress}
        progressAnimation={progressAnimation as any}
        completionAnimation={completionAnimation as any}
        tripId={tripId}
        selectedDay={selectedDay}
        handleLongPress={handleLongPress}
        handlePressOut={handlePressOut}
        toggleActivityCompletion={toggleActivityCompletion}
        onMapPress={onMapPress}
        onMarkerPress={onMarkerPress}
        showPolylines={showPolylines}
      >
        {children}
      </SharedMapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
});

export default MapContainer;
