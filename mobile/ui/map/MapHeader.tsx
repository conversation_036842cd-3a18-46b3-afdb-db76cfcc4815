import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ScreenHeader from "../common/ScreenHeader";
import DayButton from "./DayButton";

interface MapHeaderProps {
  selectedDay: number;
  setSelectedDay: (day: number) => void;
  itineraryColors: string[];
  itineraryLength: number;
}

const MapHeader: React.FC<MapHeaderProps> = ({
  selectedDay,
  setSelectedDay,
  itineraryColors,
  itineraryLength,
}) => {
  const { top } = useSafeAreaInsets();

  return (
    <View style={[styles.container, { top: 0, paddingTop: top }]}>
      <ScreenHeader
        title="Your Trip Map"
        subtitle="This is your trip map, you can see the itinerary for each day here."
        showBackButton
        transparent
      />
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {Array.from({ length: itineraryLength }).map((_, index) => {
          const day = index + 1;
          return (
            <DayButton
              key={day}
              day={day}
              itineraryColor={itineraryColors[index]}
              isSelected={day === selectedDay}
              onSelect={() => setSelectedDay(day)}
            />
          );
        })}
      </ScrollView>
      <Text style={styles.helperText}>
        Currently seeing itinerary for{" "}
        {selectedDay === -1 ? "all days" : `day ${selectedDay}`}, choose a
        different day to see the itinerary for that day.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: 0,
    right: 0,
    width: "100%",
    backgroundColor: "rgba(220, 255, 220, 0.65)",
    padding: 10,
    borderRadius: 10,
    zIndex: 1,
  },
  scrollContent: {
    flexDirection: "row",
    gap: 10,
  },
  helperText: {
    fontSize: 12,
    color: "gray",
    marginTop: 10,
  },
});

export default MapHeader;
