import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Activity } from "../../lib/types";
import MapMarker from "./MapMarker";
import { Camera, MapView, Region } from "./MapUtils";
import TripPolyline from "./TripPolyline";

import ActivityCallout from "./ActivityCallout";

export interface SharedMapViewRef {
  animateToRegion: (region: Region, duration?: number) => void;
  fitToElements: (options?: { animated?: boolean; edgePadding?: { top: number; right: number; bottom: number; left: number } }) => void;
  showCalloutForActivity: (activityId: string) => void;
}

interface SharedMapViewProps {
  mapRegion: Region;
  onRegionChangeComplete?: (region: Region) => void;
  activities?: Activity[];
  completedActivities?: Set<string>;
  activityInProgress?: string | null;
  progressAnimation?: any;
  completionAnimation?: any;
  tripId?: string;
  selectedDay?: number;
  handleLongPress?: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => (event: any) => void;
  handlePressOut?: (activityId: string, isCompleted: boolean) => () => void;
  toggleActivityCompletion?: (
    tripId: string,
    activityId: string,
    isCompleted: boolean,
  ) => void;
  onMapPress?: () => void;
  onMarkerPress?: (activityId: string) => void;
  showPolylines?: boolean;
  children?: React.ReactNode;
}

const DELTA = 0.3;

const SharedMapView = forwardRef<SharedMapViewRef, SharedMapViewProps>(({
  mapRegion,
  onRegionChangeComplete,
  activities = [],
  tripId = "",
  selectedDay = 0,
  toggleActivityCompletion,
  onMapPress,
  onMarkerPress,
  showPolylines = true,
  children,
}, ref) => {
  const mapRef = useRef<any>(null);
  const cameraRef = useRef<any>(null);

  // State for managing callout visibility
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);

  // State to control marker rendering - helps avoid race conditions
  const [markersReady, setMarkersReady] = useState(false);

  // Track current zoom level to preserve it when centering on activities
  const [currentZoomLevel, setCurrentZoomLevel] = useState<number | null>(null);

  // Delay marker rendering slightly to ensure map is fully initialized
  useEffect(() => {
    const timer = setTimeout(() => {
      setMarkersReady(true);
    }, 100); // Small delay to ensure map is ready

    return () => clearTimeout(timer);
  }, []);



  // Memoize regular activities to prevent re-renders
  const regularActivities = useMemo(
    () => {
      return activities.filter(
        (activity) => activity.coordinates && activity.id,
      );
    },
    [activities],
  );

  const handleMapPress = useCallback(() => {
    // Close any open callouts when map is pressed
    setSelectedActivity(null);
    onMapPress?.();
  }, [onMapPress]);


  const handleMarkerPress = useCallback(
    (activity: Activity) => () => {
      // Set the selected activity to show the callout
      setSelectedActivity(activity);

      // Animate camera to center on the selected activity
      if (activity.coordinates && cameraRef.current) {
        cameraRef.current.setCamera({
          centerCoordinate: [activity.coordinates.lng, activity.coordinates.lat],
          animationDuration: 1000,
        });
      }

      // Call the onMarkerPress callback if provided
      if (onMarkerPress) {
        onMarkerPress(activity.id);
      }
    },
    [onMarkerPress, cameraRef],
  );

  const handleCameraChanged = useCallback((state: any) => {
    if (onRegionChangeComplete && state.properties.center) {
      const [longitude, latitude] = state.properties.center;
      const zoom = state.properties.zoom || 10;

      // Track current zoom level for preserving it during marker press
      setCurrentZoomLevel(zoom);

      const latitudeDelta = 360 / Math.pow(2, zoom);
      const longitudeDelta = latitudeDelta * Math.cos(latitude * Math.PI / 180);

      onRegionChangeComplete({
        latitude,
        longitude,
        latitudeDelta,
        longitudeDelta,
      });
    }
  }, [onRegionChangeComplete])

  const handleToggleCompletion = useCallback((activityId: string, isCompleted: boolean) => {
    if (toggleActivityCompletion) {
      toggleActivityCompletion(tripId, activityId, isCompleted);
      setSelectedActivity(null);
    }
  }, [toggleActivityCompletion, tripId]);



  // Calculate proper zoom level for Mapbox
  const calculateZoomLevel = useCallback((latitudeDelta: number): number => {
    // Clamp latitude delta to reasonable bounds
    const clampedDelta = Math.max(0.001, Math.min(latitudeDelta, 180));

    // Convert latitude delta to zoom level using Mapbox formula
    // Zoom level 0 shows the entire world (360 degrees)
    // Each zoom level doubles the magnification
    const zoomLevel = Math.log2(360 / clampedDelta);

    // Clamp zoom level to Mapbox's supported range (0-22)
    return Math.max(0, Math.min(22, zoomLevel));
  }, []);

  // Track if we've already auto-fitted on initial load
  const [hasAutoFitted, setHasAutoFitted] = useState(false);

  // Function to fit camera to show all activities perfectly
  const fitToActivities = useCallback(() => {
    if (!cameraRef.current || regularActivities.length === 0) return;

    const validCoordinates = regularActivities
      .map(a => a.coordinates)
      .filter(coord => coord && typeof coord.lat === 'number' && typeof coord.lng === 'number');

    if (validCoordinates.length === 0) return;

    if (validCoordinates.length === 1) {
      // For single activity, center and use a reasonable zoom
      const coord = validCoordinates[0];
      cameraRef.current.setCamera({
        centerCoordinate: [coord!.lng, coord!.lat],
        zoomLevel: 15, // Good zoom level for single activity
        animationDuration: 1000,
      });
    } else {
      // For multiple activities, use fitBounds for perfect fit
      const lats = validCoordinates.map(c => c!.lat);
      const lngs = validCoordinates.map(c => c!.lng);
      const minLat = Math.min(...lats);
      const maxLat = Math.max(...lats);
      const minLng = Math.min(...lngs);
      const maxLng = Math.max(...lngs);

      // Use Mapbox's fitBounds method for perfect fitting
      if (cameraRef.current.fitBounds) {
        cameraRef.current.fitBounds(
          [minLng - DELTA, minLat - DELTA], // southwest
          [maxLng + DELTA, maxLat + DELTA], // northeast
          { top: 80, right: 80, bottom: 80, left: 80 }, // padding
          1000 // animation duration
        );
      } else {
        // Fallback if fitBounds is not available
        const centerLat = (minLat + maxLat) / 2;
        const centerLng = (minLng + maxLng) / 2;
        const latSpan = (maxLat - minLat) * 1.4; // Less aggressive padding
        const lngSpan = (maxLng - minLng) * 1.4;

        cameraRef.current.setCamera({
          centerCoordinate: [centerLng, centerLat],
          zoomLevel: calculateZoomLevel(Math.max(latSpan, lngSpan)),
          animationDuration: 1000,
        });
      }
    }
  }, [regularActivities, calculateZoomLevel]);

  // Auto-fit only on first load when activities and markers are ready
  useEffect(() => {
    if (markersReady && regularActivities.length > 0 && cameraRef.current && !hasAutoFitted) {
      // Small delay to ensure markers are rendered
      const timer = setTimeout(() => {
        fitToActivities();
        setHasAutoFitted(true);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [markersReady, regularActivities, hasAutoFitted, fitToActivities]);

  // Reset auto-fit flag when mapRegion changes (new screen/filter)
  useEffect(() => {
    setHasAutoFitted(false);
  }, [mapRegion]);

  // Expose methods to parent components via ref
  React.useImperativeHandle(ref, () => ({
    animateToRegion: (region: Region, duration = 1000) => {
      if (cameraRef.current) {
        cameraRef.current.moveTo(
          [region.longitude, region.latitude],
          duration,
        )
      }
    },
    fitToElements: () => {
      fitToActivities();
    },
    showCalloutForActivity: (activityId: string) => {
      const activity = activities.find(a => a.id === activityId);
      if (activity) {
        setSelectedActivity(activity);
      }
    },
  }), [currentZoomLevel, calculateZoomLevel, fitToActivities, activities]);

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        onPress={handleMapPress}
      >
        <Camera
          ref={cameraRef}
          centerCoordinate={[mapRegion?.longitude ?? 0, mapRegion?.latitude ?? 0]}
          zoomLevel={calculateZoomLevel(mapRegion?.latitudeDelta ?? 180)}
          onCameraChanged={handleCameraChanged}
        />
        {/* Draw connecting lines between regular activities */}
        {showPolylines && selectedDay > 0 && regularActivities.length > 1 && (
          <TripPolyline
            key={`polyline-day-${selectedDay}`}
            activities={regularActivities}
          />
        )}

        {/* Render activity markers for regular activities */}
        {markersReady && regularActivities.map((activity, idx) => {
          // Skip rendering if activity data is invalid
          if (!activity.id || !activity.coordinates) {
            console.warn('Skipping invalid activity:', activity);
            return null;
          }

          return (
            <MapMarker
              key={`marker-${activity.id}-${activity.name}-${idx}`}
              activity={activity}
              onPress={handleMarkerPress(activity)}
            />
          );
        })}

        {/* Additional children components */}
        {children}
      </MapView>

      {/* Custom callout overlay */}
      {selectedActivity && (
        <View style={styles.calloutOverlay}>
          <ActivityCallout activity={selectedActivity} onToggleCompletion={handleToggleCompletion} />
        </View>
      )}
    </View>
  );
});



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  calloutOverlay: {
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
    zIndex: 1000,
    pointerEvents: 'box-none',
  },
  calloutContainer: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    padding: 16,
    maxWidth: '100%',
    pointerEvents: 'auto',
  },
  closeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
});

export default SharedMapView;
