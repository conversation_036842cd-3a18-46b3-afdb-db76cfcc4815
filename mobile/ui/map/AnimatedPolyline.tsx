import React, { useEffect, useState, useRef } from "react";
import { Animated, Easing } from "react-native";
import { Polyline } from "./MapUtils";

interface AnimatedPolylineProps {
  coordinates: { latitude: number; longitude: number }[];
  strokeColor: string;
  strokeWidth: number;
  duration?: number;
  onCoordinateReached?: (index: number) => void;
  currentIndex?: number;
  isPaused?: boolean;
}

const AnimatedPolyline: React.FC<AnimatedPolylineProps> = ({
  coordinates,
  strokeColor,
  strokeWidth,
  duration = 5000,
  onCoordinateReached,
  isPaused = false,
}) => {
  const [progress] = useState(new Animated.Value(0));
  const [currentCoords, setCurrentCoords] = useState(coordinates.slice(0, 2));
  const lastIndex = useRef(-1);
  const animation = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    progress.setValue(0);
    setCurrentCoords(coordinates.slice(0, 2));
    lastIndex.current = -1;

    progress.addListener(({ value }) => {
      const rawIndex = value * (coordinates.length - 1);
      const nextIndex = Math.max(
        1,
        Math.min(coordinates.length - 1, Math.ceil(rawIndex)),
      );

      const nextCoords = coordinates.slice(0, nextIndex + 1);
      setCurrentCoords(nextCoords);

      if (onCoordinateReached && nextIndex > lastIndex.current) {
        lastIndex.current = nextIndex;
        onCoordinateReached(nextIndex);
      }
    });

    // Create the animation but don't start it yet
    animation.current = Animated.timing(progress, {
      toValue: 1,
      duration: duration,
      useNativeDriver: true,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    // Start animation if not paused
    if (!isPaused) {
      animation.current.start();
    }

    return () => {
      progress.removeAllListeners();
      animation.current?.stop();
    };
  }, [coordinates, duration, onCoordinateReached, isPaused]);

  return (
    <Polyline
      coordinates={currentCoords}
      strokeColor={strokeColor}
      strokeWidth={strokeWidth}
    />
  );
};

export default AnimatedPolyline;
