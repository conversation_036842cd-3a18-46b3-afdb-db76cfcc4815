import React, { useEffect } from "react";
import { StyleSheet, Text, View, Dimensions } from "react-native";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, PROVIDER_GOOGLE } from "./MapUtils";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  withD<PERSON>y,
  Easing,
} from "react-native-reanimated";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface RetryAnimatedMapProps {
  message?: string;
  attempt?: number;
  maxAttempts?: number;
  initialRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
}

// Create animated component only if MapView is available
let AnimatedMapView: any = null;
if (MapView) {
  AnimatedMapView = Animated.createAnimatedComponent(MapView);
}

const RetryAnimatedMap: React.FC<RetryAnimatedMapProps> = ({
  message = "Retrying connection...",
  attempt = 1,
  maxAttempts = 3,
  initialRegion = {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
}) => {
  // Animation values
  const mapScale = useSharedValue(1);
  const mapOpacity = useSharedValue(0.7);
  const waveScale = useSharedValue(1);
  const waveOpacity = useSharedValue(0.3);
  const iconRotation = useSharedValue(0);
  const iconScale = useSharedValue(1);

  useEffect(() => {
    // Subtle pulsing animation for the map
    mapScale.value = withRepeat(
      withSequence(
        withTiming(1.02, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.98, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
      ),
      -1,
      true,
    );

    mapOpacity.value = withRepeat(
      withSequence(
        withTiming(0.8, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.7, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
      ),
      -1,
      true,
    );

    // Wave animation
    waveScale.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 100 }),
        withTiming(1.5, { duration: 2000, easing: Easing.out(Easing.ease) }),
      ),
      -1,
      false,
    );

    waveOpacity.value = withRepeat(
      withSequence(
        withTiming(0.3, { duration: 100 }),
        withTiming(0, { duration: 2000, easing: Easing.out(Easing.ease) }),
      ),
      -1,
      false,
    );

    // Icon animations
    iconRotation.value = withRepeat(
      withTiming(360, { duration: 3000, easing: Easing.linear }),
      -1,
      false,
    );

    // Pulse the icon on each retry attempt
    iconScale.value = withSequence(
      withTiming(1.3, { duration: 300, easing: Easing.out(Easing.ease) }),
      withTiming(1, { duration: 300, easing: Easing.inOut(Easing.ease) }),
    );
  }, []);

  // Trigger a new pulse animation when the attempt changes
  useEffect(() => {
    iconScale.value = withSequence(
      withTiming(1.3, { duration: 300, easing: Easing.out(Easing.ease) }),
      withTiming(1, { duration: 300, easing: Easing.inOut(Easing.ease) }),
    );
  }, [attempt]);

  const mapAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: mapScale.value }],
      opacity: mapOpacity.value,
    };
  });

  const waveAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: waveScale.value }],
      opacity: waveOpacity.value,
    };
  });

  const iconAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { rotate: `${iconRotation.value}deg` },
        { scale: iconScale.value },
      ],
    };
  });

  // If MapView is not available (e.g., on web), show a fallback
  if (!MapView) {
    return (
      <View style={styles.container}>
        <View style={[styles.map, { backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center' }]}>
          <Text>Map not available on this platform</Text>
        </View>
        <View style={styles.messageContainer}>
          <Text style={styles.message}>{message}</Text>
          <Text style={styles.attemptText}>
            Attempt {attempt} of {maxAttempts}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AnimatedMapView
        style={[styles.map, mapAnimatedStyle]}
        provider={PROVIDER_GOOGLE}
        initialRegion={initialRegion}
        scrollEnabled={false}
        zoomEnabled={false}
        rotateEnabled={false}
        pitchEnabled={false}
      >
        <Marker
          coordinate={{
            latitude: initialRegion.latitude,
            longitude: initialRegion.longitude,
          }}
        >
          <View style={styles.markerContainer}>
            <Animated.View style={[styles.wave, waveAnimatedStyle]} />
            <Animated.View style={[styles.iconContainer, iconAnimatedStyle]}>
              <MaterialCommunityIcons
                name="refresh"
                size={24}
                color="#2196F3"
              />
            </Animated.View>
          </View>
        </Marker>
      </AnimatedMapView>

      <View style={styles.messageContainer}>
        <Text style={styles.message}>{message}</Text>
        <Text style={styles.attemptText}>
          Attempt {attempt} of {maxAttempts}
        </Text>
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    width: width * 0.9,
    height: height * 0.3,
    maxHeight: 250,
    borderRadius: 16,
    overflow: "hidden",
    marginVertical: 20,
    alignSelf: "center",
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  wave: {
    position: "absolute",
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#2196F3",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "white",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  messageContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    padding: 12,
    alignItems: "center",
  },
  message: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  attemptText: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 14,
    marginTop: 4,
  },
});

export default RetryAnimatedMap;
