import React, { useMemo } from "react";
import { ShapeSource, LineLayer } from "./MapUtils";
import { Activity } from "../../lib/types";

interface TripPolylineProps {
  activities: Activity[];
  color?: string;
  strokeWidth?: number;
}

const TripPolyline: React.FC<TripPolylineProps> = ({
  activities,
  color = "#007AFF",
  strokeWidth = 3,
}) => {
  // Memoize the GeoJSON LineString to prevent re-renders
  const geoJsonData = useMemo(() => {
    const coordinates = activities
      .filter((activity) => activity.coordinates)
      .map((activity) => [
        activity.coordinates!.lng, // Mapbox uses [lng, lat] format
        activity.coordinates!.lat,
      ]);

    if (coordinates.length < 2) {
      return null;
    }

    return {
      type: "FeatureCollection" as const,
      features: [
        {
          type: "Feature" as const,
          geometry: {
            type: "LineString" as const,
            coordinates,
          },
          properties: {},
        },
      ],
    };
  }, [activities]);

  if (!geoJsonData) {
    return null;
  }

  return (
    <ShapeSource id="trip-polyline-source" shape={geoJsonData}>
      <LineLayer
        id="trip-polyline-layer"
        style={{
          lineColor: color,
          lineWidth: strokeWidth,
          lineCap: "round",
          lineJoin: "round",
        }}
      />
    </ShapeSource>
  );
};

export default React.memo(TripPolyline);
