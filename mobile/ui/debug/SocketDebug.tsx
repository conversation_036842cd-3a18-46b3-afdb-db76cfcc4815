import React, { useState } from "react";
import { Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useAuth } from "../../lib/hooks/use-auth";
import { useSocket } from "../../lib/hooks/use-socket";

export default function SocketDebug() {
  const [modalVisible, setModalVisible] = useState(false);
  const { auth } = useAuth();
  // Get socket connection status
  const { socket, isConnected, error, reconnect } = useSocket({
    auth: {
      token: auth.token,
    },
    autoConnect: true,
  });

  const getSocketInfo = () => {
    if (!socket) return "No socket instance";

    return {
      id: socket.id,
      connected: socket.connected,
      disconnected: socket.disconnected,
    };
  };

  // Only show in development
  if (!__DEV__) return null;

  return (
    <>
      <TouchableOpacity
        style={styles.debugButton}
        onPress={() => setModalVisible(true)}
      >
        <View
          style={[
            styles.indicator,
            { backgroundColor: isConnected ? "#4CAF50" : "#F44336" },
          ]}
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Socket Debug</Text>

            <View style={styles.infoContainer}>
              <Text style={styles.label}>Status:</Text>
              <Text
                style={[
                  styles.value,
                  { color: isConnected ? "#4CAF50" : "#F44336" },
                ]}
              >
                {isConnected ? "Connected" : "Disconnected"}
              </Text>
            </View>

            {error && (
              <View style={styles.infoContainer}>
                <Text style={styles.label}>Error:</Text>
                <Text style={[styles.value, { color: "#F44336" }]}>
                  {error.message}
                </Text>
              </View>
            )}

            <View style={styles.infoContainer}>
              <Text style={styles.label}>Socket Info:</Text>
              <Text style={styles.value}>
                {JSON.stringify(getSocketInfo(), null, 2)}
              </Text>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.reconnectButton]}
                onPress={() => {
                  reconnect();
                }}
              >
                <Text style={styles.buttonText}>Reconnect</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.closeButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.buttonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  debugButton: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  indicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "80%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
    textAlign: "center",
  },
  infoContainer: {
    marginBottom: 10,
  },
  label: {
    fontWeight: "bold",
    marginBottom: 5,
  },
  value: {
    fontFamily: "monospace",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: 5,
    alignItems: "center",
  },
  reconnectButton: {
    backgroundColor: "#2196F3",
  },
  closeButton: {
    backgroundColor: "#757575",
  },
  buttonText: {
    color: "white",
    fontWeight: "bold",
  },
});
