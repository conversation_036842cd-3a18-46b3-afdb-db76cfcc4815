import React from "react";
import { Activity } from "../../lib/types";
import ActivityCard from "../common/ActivityCard";

interface TimelineItemProps {
  activity: Activity;
  index: number;
  isLast: boolean;
  onPress: () => void;
}

/**
 * Timeline item component for daily itinerary
 * Represents a single activity in the timeline
 */
export const TimelineItem: React.FC<TimelineItemProps> = ({
  activity,
  index,
  isLast,
  onPress,
}) => {
  return (
    <ActivityCard
      activity={activity}
      context="timeline"
      onPress={onPress}
      index={index}
      isLast={isLast}
    />
  );
};


