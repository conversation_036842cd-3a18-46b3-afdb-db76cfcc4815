import React from "react";
import { ScrollView, RefreshControl, StyleSheet } from "react-native";
import { Activity } from "../../lib/types";
import { EmptyState } from "./EmptyState";
import { TimelineItem } from "./TimelineItem";

interface ActivityTimelineProps {
  activities: Activity[];
  loading: boolean;
  onRefresh: () => void;
  onActivitySelect: (index: number) => void;
  onClose: () => void;
  bottomPadding: number;
}

/**
 * Activity timeline component for daily itinerary
 * Shows a scrollable list of activities
 */
export const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  activities,
  loading,
  onRefresh,
  onActivitySelect,
  onClose,
  bottomPadding,
}) => {
  return (
    <ScrollView
      contentContainerStyle={[
        styles.timeline,
        { paddingBottom: bottomPadding + 32 },
      ]}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={onRefresh} />
      }
    >
      {activities.length === 0 ? (
        <EmptyState />
      ) : (
        activities.map((activity, index) => (
          <TimelineItem
            key={activity.id}
            activity={activity}
            index={index}
            isLast={index === activities.length - 1}
            onPress={() => {
              onActivitySelect(index);
              onClose();
            }}
          />
        ))
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  timeline: {
    padding: 16,
  },
});
