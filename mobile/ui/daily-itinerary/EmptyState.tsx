import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

/**
 * Empty state component for daily itinerary
 * Shown when no activities are available for the selected day
 */
export const EmptyState: React.FC = () => {
  return (
    <View style={styles.emptyState}>
      <Ionicons name="calendar-outline" size={48} color="#666" />
      <Text style={styles.emptyStateText}>
        No activities planned for this day
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginTop: 16,
  },
});
